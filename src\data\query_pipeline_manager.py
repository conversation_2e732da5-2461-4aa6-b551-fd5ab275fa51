#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库查询管道管理器 - 重构版本

该模块统一管理所有数据库查询管道，消除重复代码：
1. 统一的聚合管道构建函数
2. 标准化的查询条件和过滤器逻辑
3. 优化的查询结果处理和数据转换
4. 统一的查询性能监控和错误处理机制

重构特性：
- 消除了重复的聚合管道构建逻辑
- 统一了查询条件和过滤器构建
- 标准化了查询结果处理流程
- 提供了查询性能监控和缓存机制

作者: SmartFileManger开发团队
日期: 2025-07-27
版本: 2.0.0 (重构版)
"""

from typing import Dict, List, Any, Optional, Union, Callable
from datetime import datetime
from dataclasses import dataclass
import time
import hashlib
import json

from ..utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class QueryMetrics:
    """查询性能指标"""
    query_type: str
    execution_time: float
    result_count: int
    cache_hit: bool = False
    error: Optional[str] = None


class QueryPipelineManager:
    """
    统一的数据库查询管道管理器
    
    提供标准化的查询管道构建、执行和结果处理功能
    """
    
    def __init__(self, enable_cache: bool = True, cache_ttl: int = 300):
        """
        初始化查询管道管理器
        
        参数:
            enable_cache: 是否启用查询缓存
            cache_ttl: 缓存生存时间（秒）
        """
        self.enable_cache = enable_cache
        self.cache_ttl = cache_ttl
        self._query_cache: Dict[str, Dict[str, Any]] = {}
        self._metrics: List[QueryMetrics] = []
        
        logger.info(f"查询管道管理器初始化完成，缓存: {enable_cache}, TTL: {cache_ttl}s")

    # ==================== 标准化聚合管道构建 ====================
    
    def build_duplicate_files_pipeline(self, min_size: Optional[int] = None, 
                                     exists: bool = True, 
                                     extensions: Optional[List[str]] = None,
                                     sort_by_size: bool = True) -> List[Dict[str, Any]]:
        """
        构建重复文件查找聚合管道 - 统一版本
        
        参数:
            min_size: 最小文件大小
            exists: 是否只查找存在的文件
            extensions: 文件扩展名过滤
            sort_by_size: 是否按大小排序
            
        返回:
            聚合管道列表
        """
        # 第一阶段：构建匹配条件
        match_query = {"hash": {"$ne": None}}
        
        if min_size is not None:
            match_query["size"] = {"$gte": min_size}
        
        if exists is not None:
            match_query["exists"] = exists
            
        if extensions:
            # 标准化扩展名
            normalized_extensions = []
            for ext in extensions:
                if not ext.startswith('.'):
                    ext = '.' + ext
                normalized_extensions.append(ext.lower())
            match_query["extension"] = {"$in": normalized_extensions}
        
        # 构建聚合管道
        pipeline = [
            {"$match": match_query},
            {"$group": {
                "_id": "$hash",
                "count": {"$sum": 1},
                "total_size": {"$sum": "$size"},
                "files": {"$push": {
                    "id": "$_id",
                    "path": {"$ifNull": ["$file_path", "$path"]},
                    "size": "$size",
                    "modified_time": "$modified_time",
                    "extension": "$extension"
                }}
            }},
            {"$match": {"count": {"$gt": 1}}}
        ]
        
        # 添加排序
        if sort_by_size:
            pipeline.append({"$sort": {"total_size": -1}})
        
        return pipeline

    def build_files_by_size_pipeline(self, min_size: Optional[int] = None,
                                   max_size: Optional[int] = None,
                                   sort_order: str = "desc") -> List[Dict[str, Any]]:
        """
        构建按大小查找文件的聚合管道
        
        参数:
            min_size: 最小文件大小
            max_size: 最大文件大小
            sort_order: 排序顺序 ("asc" 或 "desc")
            
        返回:
            聚合管道列表
        """
        match_query = {"exists": True}
        
        if min_size is not None or max_size is not None:
            size_query = {}
            if min_size is not None:
                size_query["$gte"] = min_size
            if max_size is not None:
                size_query["$lte"] = max_size
            match_query["size"] = size_query
        
        pipeline = [
            {"$match": match_query},
            {"$sort": {"size": -1 if sort_order == "desc" else 1}},
            {"$project": {
                "path": {"$ifNull": ["$file_path", "$path"]},
                "size": 1,
                "modified_time": 1,
                "extension": 1,
                "hash": 1
            }}
        ]
        
        return pipeline

    def build_files_by_date_pipeline(self, start_date: Optional[datetime] = None,
                                   end_date: Optional[datetime] = None,
                                   date_field: str = "modified_time") -> List[Dict[str, Any]]:
        """
        构建按日期范围查找文件的聚合管道
        
        参数:
            start_date: 开始日期
            end_date: 结束日期
            date_field: 日期字段名称
            
        返回:
            聚合管道列表
        """
        match_query = {"exists": True}
        
        if start_date is not None or end_date is not None:
            date_query = {}
            if start_date is not None:
                date_query["$gte"] = start_date
            if end_date is not None:
                date_query["$lte"] = end_date
            match_query[date_field] = date_query
        
        pipeline = [
            {"$match": match_query},
            {"$sort": {date_field: -1}},
            {"$project": {
                "path": {"$ifNull": ["$file_path", "$path"]},
                "size": 1,
                "modified_time": 1,
                "created_time": 1,
                "extension": 1,
                "hash": 1
            }}
        ]
        
        return pipeline

    def build_statistics_pipeline(self, group_by: str = "extension") -> List[Dict[str, Any]]:
        """
        构建统计信息聚合管道
        
        参数:
            group_by: 分组字段 ("extension", "size_range", "date_range")
            
        返回:
            聚合管道列表
        """
        if group_by == "extension":
            pipeline = [
                {"$match": {"exists": True}},
                {"$group": {
                    "_id": "$extension",
                    "count": {"$sum": 1},
                    "total_size": {"$sum": "$size"},
                    "avg_size": {"$avg": "$size"}
                }},
                {"$sort": {"total_size": -1}}
            ]
        elif group_by == "size_range":
            pipeline = [
                {"$match": {"exists": True}},
                {"$bucket": {
                    "groupBy": "$size",
                    "boundaries": [0, 1024, 1024*1024, 100*1024*1024, 1024*1024*1024],
                    "default": "large",
                    "output": {
                        "count": {"$sum": 1},
                        "total_size": {"$sum": "$size"}
                    }
                }}
            ]
        else:
            # 默认按扩展名分组
            pipeline = self.build_statistics_pipeline("extension")
        
        return pipeline

    # ==================== 查询条件构建器 ====================
    
    def build_query_conditions(self, **kwargs) -> Dict[str, Any]:
        """
        构建标准化查询条件
        
        参数:
            **kwargs: 查询参数
            
        返回:
            查询条件字典
        """
        query = {}
        
        # 基本条件
        if kwargs.get('exists') is not None:
            query['exists'] = kwargs['exists']
        
        # 大小条件
        if 'min_size' in kwargs or 'max_size' in kwargs:
            size_query = {}
            if 'min_size' in kwargs and kwargs['min_size'] is not None:
                size_query['$gte'] = kwargs['min_size']
            if 'max_size' in kwargs and kwargs['max_size'] is not None:
                size_query['$lte'] = kwargs['max_size']
            if size_query:
                query['size'] = size_query
        
        # 哈希条件
        if kwargs.get('has_hash'):
            query['hash'] = {'$ne': None}
        elif kwargs.get('no_hash'):
            query['hash'] = {'$in': [None, ""]}
        
        # 扩展名条件
        if 'extensions' in kwargs and kwargs['extensions']:
            extensions = kwargs['extensions']
            if isinstance(extensions, str):
                extensions = [extensions]
            
            normalized_extensions = []
            for ext in extensions:
                if not ext.startswith('.'):
                    ext = '.' + ext
                normalized_extensions.append(ext.lower())
            query['extension'] = {'$in': normalized_extensions}
        
        # 路径条件
        if 'path_pattern' in kwargs and kwargs['path_pattern']:
            query['path'] = {'$regex': kwargs['path_pattern'], '$options': 'i'}
        
        return query

    # ==================== 查询缓存管理 ====================
    
    def _get_cache_key(self, query_type: str, params: Dict[str, Any]) -> str:
        """生成查询缓存键"""
        cache_data = {
            'type': query_type,
            'params': params
        }
        cache_str = json.dumps(cache_data, sort_keys=True, default=str)
        return hashlib.md5(cache_str.encode()).hexdigest()
    
    def _get_cached_result(self, cache_key: str) -> Optional[Any]:
        """获取缓存结果"""
        if not self.enable_cache or cache_key not in self._query_cache:
            return None
        
        cache_entry = self._query_cache[cache_key]
        if time.time() - cache_entry['timestamp'] > self.cache_ttl:
            del self._query_cache[cache_key]
            return None
        
        return cache_entry['result']
    
    def _set_cached_result(self, cache_key: str, result: Any) -> None:
        """设置缓存结果"""
        if self.enable_cache:
            self._query_cache[cache_key] = {
                'result': result,
                'timestamp': time.time()
            }

    # ==================== 性能监控 ====================
    
    def record_query_metrics(self, query_type: str, execution_time: float,
                           result_count: int, cache_hit: bool = False,
                           error: Optional[str] = None) -> None:
        """记录查询性能指标"""
        metrics = QueryMetrics(
            query_type=query_type,
            execution_time=execution_time,
            result_count=result_count,
            cache_hit=cache_hit,
            error=error
        )
        self._metrics.append(metrics)
        
        # 保持最近1000条记录
        if len(self._metrics) > 1000:
            self._metrics = self._metrics[-1000:]
    
    def get_query_statistics(self) -> Dict[str, Any]:
        """获取查询统计信息"""
        if not self._metrics:
            return {}
        
        total_queries = len(self._metrics)
        cache_hits = sum(1 for m in self._metrics if m.cache_hit)
        errors = sum(1 for m in self._metrics if m.error)
        avg_time = sum(m.execution_time for m in self._metrics) / total_queries
        
        return {
            'total_queries': total_queries,
            'cache_hit_rate': cache_hits / total_queries if total_queries > 0 else 0,
            'error_rate': errors / total_queries if total_queries > 0 else 0,
            'average_execution_time': avg_time,
            'recent_queries': self._metrics[-10:]  # 最近10次查询
        }

    def clear_cache(self) -> None:
        """清空查询缓存"""
        self._query_cache.clear()
        logger.info("查询缓存已清空")


# ==================== 全局实例 ====================

# 创建全局查询管道管理器实例
_query_pipeline_manager = None

def get_query_pipeline_manager() -> QueryPipelineManager:
    """获取全局查询管道管理器实例"""
    global _query_pipeline_manager
    if _query_pipeline_manager is None:
        _query_pipeline_manager = QueryPipelineManager()
    return _query_pipeline_manager


# ==================== 向后兼容性函数 ====================

def build_duplicate_files_pipeline(min_size=None, exists=True):
    """向后兼容的重复文件管道构建函数"""
    manager = get_query_pipeline_manager()
    return manager.build_duplicate_files_pipeline(min_size=min_size, exists=exists)
