# 智能文件管理器重构指南

## 🎯 重构目标与原则

### 重构目标
1. **模块化**: 将单体代码拆分为职责明确的模块
2. **解耦合**: 通过事件驱动架构实现模块间解耦
3. **可维护性**: 提高代码的可读性和可维护性
4. **可扩展性**: 支持新功能的快速开发和集成
5. **性能优化**: 优化大文件处理和并发性能

### 重构原则
- **渐进式重构**: 分阶段进行，保持系统稳定
- **向后兼容**: 重构过程中保持功能完整性
- **测试驱动**: 每个重构步骤都有测试保障
- **文档同步**: 及时更新相关文档

## 📋 重构实施计划

### 阶段一: 基础架构搭建 (2周)
**目标**: 建立新架构的基础设施

#### 第1周: DTO和事件系统
**任务列表**:
- [ ] 创建DTO模型定义
- [ ] 实现事件总线系统
- [ ] 定义事件类型和数据格式
- [ ] 编写基础单元测试

**交付物**:
```
src/data/dto/
├── scan_dto.py          # 扫描相关DTO
├── duplicate_dto.py     # 重复文件DTO
├── rename_dto.py        # 重命名DTO
├── progress_dto.py      # 进度报告DTO
└── base_dto.py          # DTO基类

src/ui/events/
├── event_definitions.py # 事件定义
├── event_bus.py         # 事件总线实现
└── event_dispatcher.py  # 事件分发器
```

#### 第2周: 服务接口定义
**任务列表**:
- [ ] 定义业务服务接口
- [ ] 创建依赖注入容器
- [ ] 实现配置管理系统
- [ ] 建立日志和监控框架

**交付物**:
```
src/services/interfaces/
├── file_service.py      # 文件服务接口
├── duplicate_service.py # 重复文件服务接口
├── rename_service.py    # 重命名服务接口
└── base_service.py      # 服务基类

src/core/
├── dependency_injection.py # 依赖注入
├── config_manager.py       # 配置管理
└── logging_config.py       # 日志配置
```

### 阶段二: 业务服务重构 (3周)
**目标**: 重构核心业务逻辑

#### 第3周: 文件扫描服务
**重构步骤**:
1. **分析现有代码**
   ```python
   # 现有代码分析
   class FileScanner:  # 原始类，约800行
       def scan_directories(self, dirs, recursive=True, update_db=True):
           # 混合了扫描、哈希计算、数据库操作
           pass
   ```

2. **创建新的服务实现**
   ```python
   # 新的服务实现
   class FileScanService(IFileScanService):
       def __init__(self, db_service: IDatabaseService, event_bus: IEventBus):
           self._db_service = db_service
           self._event_bus = event_bus
       
       async def scan_directories(self, request: ScanRequest) -> AsyncGenerator[ProgressUpdate, None]:
           # 纯扫描逻辑，通过事件通知其他服务
           pass
   ```

3. **渐进式替换**
   - 创建适配器类保持兼容性
   - 逐步将调用转移到新服务
   - 移除旧代码

#### 第4周: Hash计算和重复检测服务
**重构重点**:
- 将Hash计算从文件扫描中分离
- 实现简化的MD5计算服务
- 重构重复文件检测逻辑

#### 第5周: 白名单和垃圾文件服务
**重构重点**:
- 分离白名单检测逻辑
- 重构垃圾文件检测算法
- 实现独立的业务规则引擎

### 阶段三: UI层重构 (2周)
**目标**: 重构用户界面层

#### 第6周: 主窗口和面板拆分
**重构策略**:
1. **分析MainWindow类**
   ```python
   # 现有MainWindow类问题分析
   class MainWindow:  # 约2000行代码
       def __init__(self):
           # 混合了窗口管理、事件处理、业务逻辑
           pass
   ```

2. **拆分为多个组件**
   ```python
   # 拆分后的结构
   class WindowManager:      # 窗口布局管理
   class EventDispatcher:    # UI事件分发
   class PanelContainer:     # 面板容器管理
   class StatusManager:      # 状态栏管理
   ```

#### 第7周: 文件树组件重构
**重构重点**:
- 拆分FileTreePanel为多个组件
- 实现虚拟化渲染
- 优化大量文件的显示性能

### 阶段四: 优化与完善 (1周)
**目标**: 性能优化和系统完善

#### 第8周: 性能优化和测试
**优化重点**:
- 内存使用优化
- 并发处理优化
- 数据库查询优化
- UI响应性优化

## 🔧 重构技术指南

### DTO重构指南
**步骤1: 识别数据传递点**
```python
# 重构前: 多参数传递
def scan_directories(self, directories: List[str], task_id: str = None, 
                    update_database: bool = True, interrupt_event = None):
    pass

# 重构后: DTO传递
def scan_directories(self, request: ScanRequest) -> AsyncGenerator[ProgressUpdate, None]:
    pass
```

**步骤2: 创建DTO类**
```python
@dataclass(frozen=True)
class ScanRequest:
    task_id: str
    directories: List[str]
    recursive: bool = True
    update_database: bool = True
    interrupt_event: Optional[Any] = None
    
    def validate(self) -> List[str]:
        errors = []
        if not self.task_id:
            errors.append("task_id不能为空")
        if not self.directories:
            errors.append("directories不能为空")
        return errors
```

**步骤3: 渐进式替换**
```python
# 适配器模式保持兼容性
class FileScannerAdapter:
    def __init__(self, new_service: IFileScanService):
        self._service = new_service
    
    def scan_directories(self, directories: List[str], task_id: str = None, 
                        update_database: bool = True, interrupt_event = None):
        # 转换为新的DTO格式
        request = ScanRequest(
            task_id=task_id or str(uuid.uuid4()),
            directories=directories,
            update_database=update_database,
            interrupt_event=interrupt_event
        )
        
        # 调用新服务
        return self._service.scan_directories(request)
```

### 事件驱动重构指南
**步骤1: 识别模块间调用**
```python
# 重构前: 直接调用
class RenamePanel:
    def apply_rename(self):
        self.main_window.task_queue.put({  # 紧耦合
            "type": "apply_rename_rules",
            "data": {"files": self.preview_files}
        })
```

**步骤2: 定义事件**
```python
# 定义事件类型
class UIEventType(Enum):
    RENAME_REQUESTED = "rename_requested"
    RENAME_STARTED = "rename_started"
    RENAME_PROGRESS = "rename_progress"
    RENAME_COMPLETED = "rename_completed"

# 定义事件数据
@dataclass
class RenameEventData(EventData):
    task_id: str
    files: List[Dict[str, str]]
    backup: bool = True
```

**步骤3: 重构为事件驱动**
```python
# 重构后: 事件驱动
class RenamePanel:
    def apply_rename(self):
        request = RenameRequest(
            task_id=str(uuid.uuid4()),
            files=self.preview_files,
            backup=True
        )
        
        event_data = create_event_data(
            UIEventType.RENAME_REQUESTED.value,
            source="rename_panel",
            data={"request": request.__dict__}
        )
        self.event_bus.publish(UIEventType.RENAME_REQUESTED.value, event_data)
```

### 服务层重构指南
**步骤1: 定义服务接口**
```python
class IFileScanService(ABC):
    @abstractmethod
    async def scan_directories(self, request: ScanRequest) -> AsyncGenerator[ProgressUpdate, None]:
        pass
    
    @abstractmethod
    async def get_scan_result(self, task_id: str) -> Optional[ScanResult]:
        pass
    
    @abstractmethod
    async def cancel_scan(self, task_id: str) -> bool:
        pass
```

**步骤2: 实现服务类**
```python
class FileScanService(IFileScanService):
    def __init__(self, db_service: IDatabaseService, event_bus: IEventBus):
        self._db_service = db_service
        self._event_bus = event_bus
        self._active_tasks: Dict[str, ScanTask] = {}
    
    async def scan_directories(self, request: ScanRequest) -> AsyncGenerator[ProgressUpdate, None]:
        # 验证请求
        errors = request.validate()
        if errors:
            raise ValidationError(errors)
        
        # 创建任务
        task = ScanTask(request)
        self._active_tasks[request.task_id] = task
        
        try:
            # 发布开始事件
            self._publish_event(BusinessEventType.SCAN_STARTED, {"task_id": request.task_id})
            
            # 执行扫描
            async for progress in self._execute_scan(task):
                yield progress
                
            # 发布完成事件
            self._publish_event(BusinessEventType.SCAN_COMPLETED, {"task_id": request.task_id})
            
        finally:
            # 清理任务
            self._active_tasks.pop(request.task_id, None)
```

## 🧪 重构测试策略

### 测试金字塔
```
    /\
   /  \     E2E Tests (少量)
  /____\    
 /      \   Integration Tests (适量)
/________\  Unit Tests (大量)
```

### 单元测试重构
```python
class TestFileScanService:
    @pytest.fixture
    def mock_dependencies(self):
        db_service = Mock(spec=IDatabaseService)
        event_bus = Mock(spec=IEventBus)
        return db_service, event_bus
    
    @pytest.fixture
    def service(self, mock_dependencies):
        db_service, event_bus = mock_dependencies
        return FileScanService(db_service, event_bus)
    
    async def test_scan_directories_success(self, service):
        # 准备测试数据
        request = ScanRequest(
            task_id="test_task",
            directories=["/test/path"],
            recursive=True
        )
        
        # 执行测试
        progress_updates = []
        async for progress in service.scan_directories(request):
            progress_updates.append(progress)
        
        # 验证结果
        assert len(progress_updates) > 0
        assert progress_updates[-1].progress == 100.0
```

### 集成测试重构
```python
class TestScanWorkflow:
    async def test_complete_scan_workflow(self):
        # 设置测试环境
        container = setup_test_container()
        event_bus = container.get(IEventBus)
        scan_service = container.get(IFileScanService)
        
        # 创建测试数据
        test_dir = create_test_directory_with_files()
        
        # 执行完整工作流
        request = ScanRequest(
            task_id="integration_test",
            directories=[test_dir],
            recursive=True
        )
        
        # 验证扫描结果
        results = []
        async for progress in scan_service.scan_directories(request):
            results.append(progress)
        
        # 验证事件发布
        assert event_bus.published_events[0].event_type == "SCAN_STARTED"
        assert event_bus.published_events[-1].event_type == "SCAN_COMPLETED"
```

## 🚨 重构风险控制

### 风险识别
1. **功能回归风险**: 重构可能导致功能缺失
2. **性能下降风险**: 新架构可能影响性能
3. **数据丢失风险**: 数据库结构变更风险
4. **用户体验风险**: UI变更影响用户习惯

### 风险控制措施
1. **功能对比测试**: 确保重构前后功能一致
2. **性能基准测试**: 监控性能指标变化
3. **数据备份策略**: 重构前完整备份数据
4. **灰度发布**: 逐步推出新功能

### 回滚策略
```python
# 版本控制策略
class VersionManager:
    def create_checkpoint(self, version: str):
        """创建版本检查点"""
        pass
    
    def rollback_to_version(self, version: str):
        """回滚到指定版本"""
        pass
    
    def compare_versions(self, v1: str, v2: str):
        """比较版本差异"""
        pass
```

## 📊 重构进度跟踪

### 进度指标
- **代码行数减少**: 目标减少30%
- **模块数量**: 从5个大模块拆分为15个小模块
- **测试覆盖率**: 提升到80%以上
- **性能指标**: 响应时间提升20%

### 质量指标
- **圈复杂度**: 平均复杂度 < 10
- **代码重复率**: < 5%
- **技术债务**: 减少50%
- **文档覆盖率**: > 90%

### 进度报告模板
```markdown
## 重构进度报告 - 第X周

### 完成情况
- [x] 任务1: DTO模型创建
- [x] 任务2: 事件系统实现
- [ ] 任务3: 服务接口定义

### 质量指标
- 测试覆盖率: 75% (目标: 80%)
- 代码复杂度: 8.5 (目标: <10)
- 性能基准: 响应时间提升15%

### 风险和问题
- 风险1: 数据库迁移复杂度高
- 问题1: 事件处理性能需要优化

### 下周计划
- 完成服务接口定义
- 开始业务服务实现
- 编写集成测试
```

## 📚 相关文档
- [编程规则](../rules/CODING_RULES.md)
- [架构规则](../rules/ARCHITECTURE_RULES.md)
- [性能规则](../rules/PERFORMANCE_RULES.md)
- [代码审查清单](../rules/CODE_REVIEW_CHECKLIST.md)
