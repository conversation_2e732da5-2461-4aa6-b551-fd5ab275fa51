#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
文件操作服务实现

基于新架构重构的文件操作服务，遵循RULE-001、RULE-003和RULE-005
使用DTO进行数据传输，通过事件总线进行通信
实现安全的文件操作和完整的回滚机制

作者: SmartFileManager开发团队
日期: 2024-01-20
版本: 2.0.0
"""

import asyncio
import time
import uuid
import os
import shutil
import hashlib
from pathlib import Path
from typing import AsyncGenerator, Optional, List, Dict, Any
from concurrent.futures import ThreadPoolExecutor

from src.services.interfaces import IFileOperationsService, BaseServiceImpl, ServiceStatus
from src.data.dto.file_operations_dto import (
    FileOperationRequest, FileOperationBatchResult, FileOperationProgress,
    FileOperationConfig, FileOperationItem, FileOperationResult,
    FileOperationType, FileOperationStatus, ConflictResolution,
    DEFAULT_BUFFER_SIZE, DEFAULT_MAX_CONCURRENT_OPERATIONS, DEFAULT_TIMEOUT_SECONDS,
    DEFAULT_RETRY_ATTEMPTS, DEFAULT_RETRY_DELAY, DEFAULT_PROGRESS_UPDATE_INTERVAL
)
from src.data.dto.base_dto import ProgressUpdate, ErrorInfo
from src.data.dto.interface_dto import OperationPreviewInfo, OperationHistoryInfo
from src.ui.events.event_bus import IEventBus
from src.ui.events.event_definitions import (
    create_business_event, BusinessEventType, EventPriority
)

# 常量定义（遵循QUAL-001规则）
DEFAULT_MAX_WORKERS = 4
DEFAULT_BATCH_SIZE = 100
DEFAULT_BACKUP_SUFFIX = ".bak"
PROGRESS_UPDATE_INTERVAL = 0.5  # 进度更新间隔（秒）
TRANSACTION_TIMEOUT = 300.0  # 事务超时时间（秒）
MAX_OPERATION_HISTORY = 100  # 最大操作历史记录数


class FileOperationsServiceImpl(BaseServiceImpl, IFileOperationsService):
    """
    文件操作服务实现
    
    遵循RULE-001: 模块职责单一原则 - 只负责文件系统操作
    遵循RULE-003: 事件驱动通信规范 - 通过事件总线通信
    遵循RULE-005: 异步任务实现 - 长时间运行的文件操作
    """
    
    def __init__(self, event_bus: IEventBus, max_workers: int = DEFAULT_MAX_WORKERS):
        super().__init__("FileOperationsService", "2.0.0")
        self._event_bus = event_bus
        self._max_workers = max_workers
        self._executor = ThreadPoolExecutor(max_workers=max_workers)
        
        # 任务管理
        self._operation_tasks: Dict[str, asyncio.Task] = {}
        self._operation_results: Dict[str, FileOperationBatchResult] = {}
        self._operation_progress: Dict[str, FileOperationProgress] = {}
        
        # 配置
        self._config = FileOperationConfig()
        
        # 操作历史
        self._operation_history: List[Dict[str, Any]] = []
        
        # 备份管理
        self._backup_dir: Optional[str] = None
        
    async def start_service(self) -> bool:
        """启动服务"""
        if await super().start_service():
            # 初始化备份目录
            await self._initialize_backup_directory()
            
            # 发布服务启动事件
            event = create_business_event(
                BusinessEventType.DUPLICATE_CHECK_STARTED,  # 复用现有事件类型
                self.get_service_name(),
                {"service_version": self.get_service_version(), "action": "service_started"}
            )
            self._event_bus.publish("SERVICE_STARTED", event)
            return True
        return False
    
    async def stop_service(self) -> bool:
        """停止服务"""
        # 取消所有活动的操作任务
        await self.cancel_all_tasks()
        
        # 关闭线程池
        self._executor.shutdown(wait=True)
        
        if await super().stop_service():
            # 发布服务停止事件
            event = create_business_event(
                BusinessEventType.DUPLICATE_CHECK_COMPLETED,  # 复用现有事件类型
                self.get_service_name(),
                {"action": "service_stopped"}
            )
            self._event_bus.publish("SERVICE_STOPPED", event)
            return True
        return False
    
    async def execute_operations(self, request: FileOperationRequest) -> AsyncGenerator[FileOperationProgress, None]:
        """
        执行批量文件操作
        
        遵循RULE-005: 长时间运行的操作实现为异步任务
        """
        # 验证请求
        validation_errors = await self.validate_operations(request)
        if validation_errors:
            error_info = ErrorInfo(
                code="FILE_OPERATION_VALIDATION_FAILED",
                message="文件操作请求验证失败",
                details="; ".join(validation_errors)
            )
            raise ValueError(error_info.message)
        
        # 发布操作开始事件
        event = create_business_event(
            BusinessEventType.DUPLICATE_CHECK_STARTED,  # 复用现有事件类型
            self.get_service_name(),
            {
                "task_id": request.task_id,
                "operation_count": len(request.operations),
                "operation_types": [op.operation_type.value for op in request.operations]
            }
        )
        self._event_bus.publish("FILE_OPERATIONS_STARTED", event)
        
        # 创建操作任务
        operation_task = asyncio.create_task(self._execute_operations_task(request))
        self._operation_tasks[request.task_id] = operation_task
        
        try:
            # 异步生成进度更新
            async for progress in self._monitor_operation_progress(request.task_id):
                yield progress
                
                # 发布进度事件
                progress_event = create_business_event(
                    BusinessEventType.DUPLICATE_CHECK_PROGRESS,  # 复用现有事件类型
                    self.get_service_name(),
                    progress.to_dict()
                )
                self._event_bus.publish("FILE_OPERATIONS_PROGRESS", progress_event)
        
        except Exception as e:
            # 处理操作错误
            error_info = ErrorInfo(
                code="FILE_OPERATION_EXECUTION_FAILED",
                message=f"文件操作执行失败: {str(e)}",
                details=f"任务ID: {request.task_id}"
            )
            
            error_event = create_business_event(
                BusinessEventType.DUPLICATE_CHECK_COMPLETED,  # 复用现有事件类型
                self.get_service_name(),
                error_info.to_dict(),
                EventPriority.HIGH
            )
            self._event_bus.publish("FILE_OPERATIONS_FAILED", error_event)
            raise
        
        finally:
            # 清理任务
            if request.task_id in self._operation_tasks:
                del self._operation_tasks[request.task_id]
    
    async def _execute_operations_task(self, request: FileOperationRequest) -> FileOperationBatchResult:
        """执行文件操作任务的核心逻辑"""
        start_time = time.time()
        
        # 创建批量结果
        batch_result = FileOperationBatchResult(
            task_id=request.task_id,
            total_operations=len(request.operations),
            start_time=start_time
        )
        
        try:
            # 初始化进度
            progress = FileOperationProgress(
                task_id=request.task_id,
                progress=0.0,
                status_message="开始文件操作...",
                processed_items=0,
                total_items=len(request.operations)
            )
            self._operation_progress[request.task_id] = progress
            
            # 按优先级排序操作
            sorted_operations = self._sort_operations_by_priority(request.operations)
            
            # 更新进度 - 创建新的进度对象
            progress = FileOperationProgress(
                task_id=request.task_id,
                progress=5.0,
                status_message=f"准备执行 {len(sorted_operations)} 个文件操作...",
                processed_items=0,
                total_items=len(request.operations)
            )
            self._operation_progress[request.task_id] = progress
            
            # 执行操作
            semaphore = asyncio.Semaphore(request.max_concurrent_operations)
            operation_tasks = []
            
            for i, operation in enumerate(sorted_operations):
                task = asyncio.create_task(
                    self._execute_single_operation(operation, semaphore, request)
                )
                operation_tasks.append(task)
            
            # 等待所有操作完成
            operation_results = await asyncio.gather(*operation_tasks, return_exceptions=True)
            
            # 处理结果
            for i, result in enumerate(operation_results):
                if isinstance(result, Exception):
                    # 创建失败结果
                    failed_result = FileOperationResult(
                        operation_item=sorted_operations[i],
                        status=FileOperationStatus.FAILED,
                        error_message=str(result),
                        start_time=start_time,
                        end_time=time.time()
                    )
                    batch_result.operation_results.append(failed_result)
                    batch_result.failed_operations += 1
                else:
                    batch_result.operation_results.append(result)
                    if result.status == FileOperationStatus.COMPLETED:
                        batch_result.successful_operations += 1
                        batch_result.total_bytes_processed += result.bytes_processed
                    elif result.status == FileOperationStatus.SKIPPED:
                        batch_result.skipped_operations += 1
                    elif result.status == FileOperationStatus.CANCELLED:
                        batch_result.cancelled_operations += 1
                    else:
                        batch_result.failed_operations += 1
                
                # 更新进度 - 创建新的进度对象
                processed = i + 1
                progress_percent = 5.0 + (processed / len(sorted_operations)) * 90.0
                progress = FileOperationProgress(
                    task_id=request.task_id,
                    progress=min(progress_percent, 95.0),
                    status_message=f"已处理 {processed}/{len(sorted_operations)} 个操作",
                    processed_items=processed,
                    total_items=len(request.operations)
                )
                self._operation_progress[request.task_id] = progress
            
            # 完成操作
            end_time = time.time()
            batch_result.end_time = end_time

            # 设置最终进度为100%
            final_progress = FileOperationProgress(
                task_id=request.task_id,
                progress=100.0,
                status_message="文件操作完成",
                processed_items=len(request.operations),
                total_items=len(request.operations)
            )
            self._operation_progress[request.task_id] = final_progress

            # 保存结果
            self._operation_results[request.task_id] = batch_result
            
            # 添加到历史记录
            await self._add_to_history(batch_result)
            
            # 发布完成事件
            event = create_business_event(
                BusinessEventType.DUPLICATE_CHECK_COMPLETED,  # 复用现有事件类型
                self.get_service_name(),
                batch_result.to_dict()
            )
            self._event_bus.publish("FILE_OPERATIONS_COMPLETED", event)
            
            return batch_result
        
        except Exception as e:
            # 创建失败结果
            end_time = time.time()
            batch_result.end_time = end_time
            batch_result.failed_operations = len(request.operations)
            batch_result.errors.append(str(e))

            # 设置错误进度
            error_progress = FileOperationProgress(
                task_id=request.task_id,
                progress=100.0,  # 即使失败也设为100%以结束监控
                status_message=f"操作失败: {str(e)}",
                processed_items=len(request.operations),
                total_items=len(request.operations)
            )
            self._operation_progress[request.task_id] = error_progress

            self._operation_results[request.task_id] = batch_result
            raise
    
    def _sort_operations_by_priority(self, operations: List[FileOperationItem]) -> List[FileOperationItem]:
        """按优先级排序操作"""
        from src.data.dto.file_operations_dto import OPERATION_PRIORITY
        
        return sorted(operations, key=lambda op: OPERATION_PRIORITY.get(op.operation_type, 999))
    
    async def _execute_single_operation(self, operation: FileOperationItem, 
                                      semaphore: asyncio.Semaphore,
                                      request: FileOperationRequest) -> FileOperationResult:
        """执行单个文件操作"""
        async with semaphore:
            start_time = time.time()
            
            result = FileOperationResult(
                operation_item=operation,
                status=FileOperationStatus.IN_PROGRESS,
                start_time=start_time
            )
            
            try:
                # 根据操作类型执行不同的操作
                if operation.operation_type == FileOperationType.COPY:
                    await self._copy_file(operation, result, request)
                elif operation.operation_type == FileOperationType.MOVE:
                    await self._move_file(operation, result, request)
                elif operation.operation_type == FileOperationType.DELETE:
                    await self._delete_file(operation, result, request)
                elif operation.operation_type == FileOperationType.RENAME:
                    await self._rename_file(operation, result, request)
                elif operation.operation_type == FileOperationType.CREATE_DIRECTORY:
                    await self._create_directory(operation, result, request)
                elif operation.operation_type == FileOperationType.BACKUP:
                    await self._backup_file(operation, result, request)
                else:
                    raise ValueError(f"不支持的操作类型: {operation.operation_type}")

                # 只有在状态不是SKIPPED或FAILED时才设置为COMPLETED
                if result.status not in [FileOperationStatus.SKIPPED, FileOperationStatus.FAILED]:
                    result.status = FileOperationStatus.COMPLETED
                result.end_time = time.time()
                
            except Exception as e:
                result.status = FileOperationStatus.FAILED
                result.error_message = str(e)
                result.end_time = time.time()
            
            return result

    async def _copy_file(self, operation: FileOperationItem, result: FileOperationResult,
                        request: FileOperationRequest):
        """复制文件"""
        source_path = Path(operation.source_path)
        target_path = Path(operation.target_path)

        # 确保目标目录存在
        target_path.parent.mkdir(parents=True, exist_ok=True)

        # 检查冲突
        if target_path.exists():
            await self._handle_conflict(operation, result, request)
            if result.status in [FileOperationStatus.FAILED, FileOperationStatus.SKIPPED]:
                return
            # 更新target_path以反映可能的重命名
            target_path = Path(operation.target_path)

        # 执行复制
        await asyncio.get_event_loop().run_in_executor(
            self._executor,
            shutil.copy2,
            str(source_path),
            str(target_path)
        )

        result.bytes_processed = source_path.stat().st_size if source_path.exists() else 0
        result.actual_target_path = str(target_path)

    async def _move_file(self, operation: FileOperationItem, result: FileOperationResult,
                        request: FileOperationRequest):
        """移动文件"""
        source_path = Path(operation.source_path)
        target_path = Path(operation.target_path)

        # 确保目标目录存在
        target_path.parent.mkdir(parents=True, exist_ok=True)

        # 检查冲突
        if target_path.exists():
            await self._handle_conflict(operation, result, request)
            if result.status in [FileOperationStatus.FAILED, FileOperationStatus.SKIPPED]:
                return
            # 更新target_path以反映可能的重命名
            target_path = Path(operation.target_path)

        # 创建备份（如果需要）
        if request.create_backup:
            backup_path = await self._create_backup(source_path)
            result.backup_path = backup_path

        # 执行移动
        await asyncio.get_event_loop().run_in_executor(
            self._executor,
            shutil.move,
            str(source_path),
            str(target_path)
        )

        result.bytes_processed = target_path.stat().st_size if target_path.exists() else 0
        result.actual_target_path = str(target_path)

    async def _delete_file(self, operation: FileOperationItem, result: FileOperationResult,
                          request: FileOperationRequest):
        """删除文件"""
        source_path = Path(operation.source_path)

        if not source_path.exists():
            result.status = FileOperationStatus.SKIPPED
            result.error_message = "文件不存在"
            return

        # 创建备份（如果需要）
        if request.create_backup:
            backup_path = await self._create_backup(source_path)
            result.backup_path = backup_path

        file_size = source_path.stat().st_size if source_path.is_file() else 0

        # 执行删除
        if source_path.is_file():
            await asyncio.get_event_loop().run_in_executor(
                self._executor,
                source_path.unlink
            )
        elif source_path.is_dir():
            await asyncio.get_event_loop().run_in_executor(
                self._executor,
                shutil.rmtree,
                str(source_path)
            )

        result.bytes_processed = file_size

    async def _rename_file(self, operation: FileOperationItem, result: FileOperationResult,
                          request: FileOperationRequest):
        """重命名文件"""
        source_path = Path(operation.source_path)
        target_path = Path(operation.target_path)

        # 检查冲突
        if target_path.exists():
            await self._handle_conflict(operation, result, request)
            if result.status in [FileOperationStatus.FAILED, FileOperationStatus.SKIPPED]:
                return
            # 更新target_path以反映可能的重命名
            target_path = Path(operation.target_path)

        # 创建备份（如果需要）
        if request.create_backup:
            backup_path = await self._create_backup(source_path)
            result.backup_path = backup_path

        # 执行重命名
        await asyncio.get_event_loop().run_in_executor(
            self._executor,
            source_path.rename,
            target_path
        )

        result.bytes_processed = target_path.stat().st_size if target_path.exists() else 0
        result.actual_target_path = str(target_path)

    async def _create_directory(self, operation: FileOperationItem, result: FileOperationResult,
                               request: FileOperationRequest):
        """创建目录"""
        target_path = Path(operation.target_path)

        # 执行创建目录
        await asyncio.get_event_loop().run_in_executor(
            self._executor,
            target_path.mkdir,
            True,  # parents
            True   # exist_ok
        )

        result.actual_target_path = str(target_path)

    async def _backup_file(self, operation: FileOperationItem, result: FileOperationResult,
                          request: FileOperationRequest):
        """备份文件"""
        source_path = Path(operation.source_path)

        if not source_path.exists():
            result.status = FileOperationStatus.SKIPPED
            result.error_message = "源文件不存在"
            return

        backup_path = await self._create_backup(source_path)
        result.backup_path = backup_path
        result.actual_target_path = backup_path
        result.bytes_processed = source_path.stat().st_size if source_path.is_file() else 0

    async def _handle_conflict(self, operation: FileOperationItem, result: FileOperationResult,
                              request: FileOperationRequest):
        """处理文件冲突"""
        if request.conflict_resolution == ConflictResolution.SKIP:
            result.status = FileOperationStatus.SKIPPED
            result.error_message = "目标文件已存在，跳过操作"
        elif request.conflict_resolution == ConflictResolution.OVERWRITE:
            # 允许覆盖，不做处理
            pass
        elif request.conflict_resolution == ConflictResolution.RENAME:
            # 生成新的目标路径
            new_target = await self._generate_unique_path(operation.target_path)
            # 使用object.__setattr__来设置frozen dataclass的属性
            object.__setattr__(operation, 'target_path', new_target)
        elif request.conflict_resolution == ConflictResolution.BACKUP:
            # 备份现有文件
            existing_path = Path(operation.target_path)
            backup_path = await self._create_backup(existing_path)
            result.metadata["existing_file_backup"] = backup_path
        else:
            result.status = FileOperationStatus.FAILED
            result.error_message = "未知的冲突解决策略"

    async def _create_backup(self, file_path: Path) -> str:
        """创建文件备份"""
        if not self._backup_dir:
            await self._initialize_backup_directory()

        # 生成备份文件名
        timestamp = int(time.time())
        backup_name = f"{file_path.name}.{timestamp}{DEFAULT_BACKUP_SUFFIX}"
        backup_path = Path(self._backup_dir) / backup_name

        # 复制文件到备份目录
        await asyncio.get_event_loop().run_in_executor(
            self._executor,
            shutil.copy2,
            str(file_path),
            str(backup_path)
        )

        return str(backup_path)

    async def _generate_unique_path(self, base_path: str) -> str:
        """生成唯一的文件路径"""
        path = Path(base_path)
        if not path.exists():
            return base_path

        counter = 1
        while True:
            stem = path.stem
            suffix = path.suffix
            parent = path.parent

            new_name = f"{stem}_{counter:03d}{suffix}"
            new_path = parent / new_name

            if not new_path.exists():
                return str(new_path)

            counter += 1
            if counter > 999:
                raise ValueError(f"无法为文件生成唯一名称: {base_path}")

    async def _initialize_backup_directory(self):
        """初始化备份目录"""
        if not self._backup_dir:
            import tempfile
            temp_dir = tempfile.gettempdir()
            self._backup_dir = os.path.join(temp_dir, "smartfilemanager_file_operations_backup")

            # 创建目录（如果不存在）
            def create_dir():
                os.makedirs(self._backup_dir, exist_ok=True)

            await asyncio.get_event_loop().run_in_executor(
                self._executor,
                create_dir
            )

    async def _monitor_operation_progress(self, task_id: str) -> AsyncGenerator[FileOperationProgress, None]:
        """监控操作进度"""
        last_progress = 0.0
        max_wait_time = 60.0  # 最大等待1分钟（减少等待时间）
        start_time = time.time()
        max_iterations = 120  # 最大迭代次数（60秒 / 0.5秒间隔）
        iteration_count = 0

        while task_id in self._operation_tasks and iteration_count < max_iterations:
            # 检查超时
            if time.time() - start_time > max_wait_time:
                break

            current_progress = self._operation_progress.get(task_id)

            if current_progress:
                # 总是yield当前进度，即使没有变化（为了测试）
                if current_progress.progress > last_progress or iteration_count == 0:
                    yield current_progress
                    last_progress = current_progress.progress

                # 如果完成，退出监控
                if current_progress.progress >= 100.0:
                    break

            # 等待一段时间再检查
            await asyncio.sleep(PROGRESS_UPDATE_INTERVAL)
            iteration_count += 1

        # 确保最终进度被发送
        final_progress = self._operation_progress.get(task_id)
        if final_progress:
            # 创建新的进度对象而不是修改现有的
            final_progress_copy = FileOperationProgress(
                task_id=final_progress.task_id,
                progress=100.0,
                status_message="文件操作完成",
                processed_items=final_progress.processed_items,
                total_items=final_progress.total_items,
                current_operation=final_progress.current_operation,
                current_file_progress=final_progress.current_file_progress,
                bytes_per_second=final_progress.bytes_per_second,
                estimated_time_remaining=0.0,
                active_operations=final_progress.active_operations
            )
            yield final_progress_copy

    async def _add_to_history(self, batch_result: FileOperationBatchResult):
        """添加到操作历史"""
        history_entry = {
            "history_id": str(uuid.uuid4()),
            "task_id": batch_result.task_id,
            "timestamp": time.time(),
            "operation_count": batch_result.total_operations,
            "successful_count": batch_result.successful_operations,
            "failed_count": batch_result.failed_operations,
            "duration": batch_result.duration,
            "bytes_processed": batch_result.total_bytes_processed,
            "success_rate": batch_result.success_rate
        }

        self._operation_history.append(history_entry)

        # 限制历史记录数量
        if len(self._operation_history) > MAX_OPERATION_HISTORY:
            self._operation_history = self._operation_history[-MAX_OPERATION_HISTORY:]

    # 实现IFileOperationsService接口的其他方法
    async def get_operation_result(self, task_id: str) -> Optional[FileOperationBatchResult]:
        """获取操作结果"""
        return self._operation_results.get(task_id)

    async def validate_operations(self, request: FileOperationRequest) -> List[str]:
        """验证文件操作请求"""
        errors = []

        # 使用DTO的内置验证
        dto_errors = request.validate()
        errors.extend(dto_errors)

        # 额外的业务验证
        for operation in request.operations:
            # 验证源路径
            if operation.operation_type != FileOperationType.CREATE_DIRECTORY:
                if not os.path.exists(operation.source_path):
                    errors.append(f"源文件不存在: {operation.source_path}")
                elif not os.access(operation.source_path, os.R_OK):
                    errors.append(f"源文件不可读: {operation.source_path}")

            # 验证目标路径（删除操作不需要目标路径）
            if operation.operation_type != FileOperationType.DELETE:
                target_dir = os.path.dirname(operation.target_path)
                if target_dir and not os.path.exists(target_dir):
                    # 检查是否可以创建目录
                    try:
                        os.makedirs(target_dir, exist_ok=True)
                    except PermissionError:
                        errors.append(f"目标目录不可写: {target_dir}")
                elif target_dir and not os.access(target_dir, os.W_OK):
                    errors.append(f"目标目录不可写: {target_dir}")

        return errors

    async def preview_operations(self, request: FileOperationRequest) -> OperationPreviewInfo:
        """
        预览文件操作

        Args:
            request: 文件操作请求DTO

        Returns:
            OperationPreviewInfo: 操作预览信息DTO
        """
        previews = []

        for operation in request.operations:
            preview = {
                "operation_type": operation.operation_type.value,
                "source_path": operation.source_path,
                "target_path": operation.target_path,
                "file_size": operation.file_size,
                "will_overwrite": False,
                "has_conflict": False,
                "estimated_duration": 0.0,
                "error_message": None
            }

            try:
                # 检查源文件
                if operation.operation_type != FileOperationType.CREATE_DIRECTORY:
                    source_path = Path(operation.source_path)
                    if not source_path.exists():
                        preview["error_message"] = "源文件不存在"
                        preview["has_conflict"] = True
                    else:
                        preview["file_size"] = source_path.stat().st_size if source_path.is_file() else 0

                # 检查目标文件冲突
                if operation.operation_type != FileOperationType.DELETE:
                    target_path = Path(operation.target_path)
                    if target_path.exists():
                        preview["will_overwrite"] = True
                        if request.conflict_resolution == ConflictResolution.SKIP:
                            preview["has_conflict"] = True

                # 估算操作时间（简单估算）
                if preview["file_size"] > 0:
                    # 假设传输速度为100MB/s
                    preview["estimated_duration"] = preview["file_size"] / (100 * 1024 * 1024)

            except Exception as e:
                preview["error_message"] = str(e)
                preview["has_conflict"] = True

            previews.append(preview)

        return OperationPreviewInfo.from_preview_list(previews)

    async def cancel_operations(self, task_id: str) -> bool:
        """取消文件操作"""
        if task_id in self._operation_tasks:
            task = self._operation_tasks[task_id]
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass
            finally:
                self._operation_tasks.pop(task_id, None)
                self._operation_progress.pop(task_id, None)
            return True
        return False

    async def rollback_operations(self, task_id: str) -> bool:
        """回滚文件操作"""
        result = self._operation_results.get(task_id)
        if not result:
            return False

        try:
            rollback_count = 0
            for operation_result in reversed(result.operation_results):
                if operation_result.success:
                    await self._rollback_single_operation(operation_result)
                    rollback_count += 1

            # 添加回滚记录到历史
            rollback_history = {
                "history_id": str(uuid.uuid4()),
                "task_id": f"{task_id}_rollback",
                "timestamp": time.time(),
                "operation_type": "rollback",
                "original_task_id": task_id,
                "rollback_count": rollback_count
            }
            self._operation_history.append(rollback_history)

            return True

        except Exception as e:
            import logging
            logging.getLogger(__name__).error(f"回滚操作失败: {task_id}, 错误: {e}")
            return False

    async def _rollback_single_operation(self, operation_result: FileOperationResult):
        """回滚单个操作"""
        operation = operation_result.operation_item

        if operation.operation_type == FileOperationType.COPY:
            # 删除复制的文件
            if operation_result.actual_target_path and os.path.exists(operation_result.actual_target_path):
                await asyncio.get_event_loop().run_in_executor(
                    self._executor,
                    os.remove,
                    operation_result.actual_target_path
                )

        elif operation.operation_type == FileOperationType.MOVE:
            # 移回文件
            if operation_result.actual_target_path and os.path.exists(operation_result.actual_target_path):
                await asyncio.get_event_loop().run_in_executor(
                    self._executor,
                    shutil.move,
                    operation_result.actual_target_path,
                    operation.source_path
                )

        elif operation.operation_type == FileOperationType.DELETE:
            # 从备份恢复
            if operation_result.backup_path and os.path.exists(operation_result.backup_path):
                await asyncio.get_event_loop().run_in_executor(
                    self._executor,
                    shutil.copy2,
                    operation_result.backup_path,
                    operation.source_path
                )

        elif operation.operation_type == FileOperationType.RENAME:
            # 重命名回去
            if operation_result.actual_target_path and os.path.exists(operation_result.actual_target_path):
                await asyncio.get_event_loop().run_in_executor(
                    self._executor,
                    os.rename,
                    operation_result.actual_target_path,
                    operation.source_path
                )

    async def get_operation_history(self, limit: int = 100) -> OperationHistoryInfo:
        """
        获取操作历史

        Args:
            limit: 返回的历史条目数量限制

        Returns:
            OperationHistoryInfo: 操作历史信息DTO
        """
        # 按时间倒序排列
        sorted_history = sorted(self._operation_history, key=lambda h: h["timestamp"], reverse=True)

        # 限制返回数量
        limited_history = sorted_history[:limit]

        return OperationHistoryInfo.from_history_list(limited_history)

    async def cancel_all_tasks(self):
        """取消所有活动任务"""
        for task_id, task in list(self._operation_tasks.items()):
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass
            finally:
                self._operation_tasks.pop(task_id, None)
                self._operation_progress.pop(task_id, None)
