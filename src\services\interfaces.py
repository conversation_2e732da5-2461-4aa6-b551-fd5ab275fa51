#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
业务服务层接口定义

该模块定义了业务服务层的标准接口，实现UI层与业务逻辑的解耦。
所有业务服务都应该实现这些接口，确保一致的调用方式。

作者: AI助手
日期: 2023-06-01
版本: 2.0.0
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Callable, AsyncGenerator
from src.data.dto_models import (
    ScanRequest, ScanResult, DuplicateDetectionRequest, DuplicateDetectionResult,
    RenameRequest, RenameResult, FileOperationRequest, FileOperationResult,
    ProgressUpdate
)


class IFileService(ABC):
    """文件服务接口"""
    
    @abstractmethod
    async def scan_directories(self, request: ScanRequest) -> AsyncGenerator[ProgressUpdate, None]:
        """
        异步扫描目录
        
        参数:
            request: 扫描请求DTO
            
        返回:
            异步生成器，产生进度更新
        """
        pass
    
    @abstractmethod
    async def get_scan_result(self, task_id: str) -> Optional[ScanResult]:
        """
        获取扫描结果
        
        参数:
            task_id: 任务ID
            
        返回:
            扫描结果DTO或None
        """
        pass
    
    @abstractmethod
    async def cancel_scan(self, task_id: str) -> bool:
        """
        取消扫描任务
        
        参数:
            task_id: 任务ID
            
        返回:
            是否成功取消
        """
        pass


class IDuplicateService(ABC):
    """重复文件服务接口"""
    
    @abstractmethod
    async def detect_duplicates(self, request: DuplicateDetectionRequest) -> AsyncGenerator[ProgressUpdate, None]:
        """
        异步检测重复文件
        
        参数:
            request: 重复文件检测请求DTO
            
        返回:
            异步生成器，产生进度更新
        """
        pass
    
    @abstractmethod
    async def get_detection_result(self, task_id: str) -> Optional[DuplicateDetectionResult]:
        """
        获取重复文件检测结果
        
        参数:
            task_id: 任务ID
            
        返回:
            检测结果DTO或None
        """
        pass
    
    @abstractmethod
    async def resolve_duplicates(self, task_id: str, resolution_strategy: str) -> FileOperationResult:
        """
        解决重复文件
        
        参数:
            task_id: 检测任务ID
            resolution_strategy: 解决策略 ("keep_largest", "keep_oldest", "manual")
            
        返回:
            文件操作结果DTO
        """
        pass


class IRenameService(ABC):
    """重命名服务接口"""
    
    @abstractmethod
    async def preview_rename(self, rules: List[Dict[str, Any]], files: List[str]) -> List[Dict[str, str]]:
        """
        预览重命名结果
        
        参数:
            rules: 重命名规则列表
            files: 文件路径列表
            
        返回:
            重命名预览结果 [{"old_path": "", "new_path": ""}]
        """
        pass
    
    @abstractmethod
    async def apply_rename(self, request: RenameRequest) -> AsyncGenerator[ProgressUpdate, None]:
        """
        应用重命名
        
        参数:
            request: 重命名请求DTO
            
        返回:
            异步生成器，产生进度更新
        """
        pass
    
    @abstractmethod
    async def get_rename_result(self, task_id: str) -> Optional[RenameResult]:
        """
        获取重命名结果
        
        参数:
            task_id: 任务ID
            
        返回:
            重命名结果DTO或None
        """
        pass


class IFileOperationService(ABC):
    """文件操作服务接口"""
    
    @abstractmethod
    async def execute_operation(self, request: FileOperationRequest) -> AsyncGenerator[ProgressUpdate, None]:
        """
        执行文件操作
        
        参数:
            request: 文件操作请求DTO
            
        返回:
            异步生成器，产生进度更新
        """
        pass
    
    @abstractmethod
    async def get_operation_result(self, task_id: str) -> Optional[FileOperationResult]:
        """
        获取操作结果
        
        参数:
            task_id: 任务ID
            
        返回:
            操作结果DTO或None
        """
        pass
    
    @abstractmethod
    async def validate_operation(self, request: FileOperationRequest) -> List[str]:
        """
        验证文件操作
        
        参数:
            request: 文件操作请求DTO
            
        返回:
            验证错误列表，空列表表示验证通过
        """
        pass


class IJunkService(ABC):
    """垃圾文件服务接口"""
    
    @abstractmethod
    async def detect_junk_files(self, directories: List[str], rules: List[Dict[str, Any]]) -> AsyncGenerator[ProgressUpdate, None]:
        """
        检测垃圾文件
        
        参数:
            directories: 目录列表
            rules: 垃圾文件规则
            
        返回:
            异步生成器，产生进度更新
        """
        pass
    
    @abstractmethod
    async def cleanup_junk_files(self, file_paths: List[str], backup: bool = True) -> FileOperationResult:
        """
        清理垃圾文件
        
        参数:
            file_paths: 垃圾文件路径列表
            backup: 是否备份
            
        返回:
            文件操作结果DTO
        """
        pass
    
    @abstractmethod
    def get_junk_rules(self) -> List[Dict[str, Any]]:
        """
        获取垃圾文件规则
        
        返回:
            规则列表
        """
        pass
    
    @abstractmethod
    def save_junk_rules(self, rules: List[Dict[str, Any]]) -> bool:
        """
        保存垃圾文件规则
        
        参数:
            rules: 规则列表
            
        返回:
            是否保存成功
        """
        pass


class IWhitelistService(ABC):
    """白名单服务接口"""
    
    @abstractmethod
    async def check_whitelist(self, directories: List[str], rules: List[Dict[str, Any]]) -> AsyncGenerator[ProgressUpdate, None]:
        """
        检查白名单文件
        
        参数:
            directories: 目录列表
            rules: 白名单规则
            
        返回:
            异步生成器，产生进度更新
        """
        pass
    
    @abstractmethod
    async def update_whitelist_status(self, file_paths: List[str], whitelist_type: str) -> bool:
        """
        更新白名单状态
        
        参数:
            file_paths: 文件路径列表
            whitelist_type: 白名单类型
            
        返回:
            是否更新成功
        """
        pass
    
    @abstractmethod
    def get_whitelist_rules(self) -> List[Dict[str, Any]]:
        """
        获取白名单规则
        
        返回:
            规则列表
        """
        pass
    
    @abstractmethod
    def save_whitelist_rules(self, rules: List[Dict[str, Any]]) -> bool:
        """
        保存白名单规则
        
        参数:
            rules: 规则列表
            
        返回:
            是否保存成功
        """
        pass


class IConfigService(ABC):
    """配置服务接口"""
    
    @abstractmethod
    def get_config(self, config_name: str) -> Dict[str, Any]:
        """
        获取配置
        
        参数:
            config_name: 配置名称
            
        返回:
            配置字典
        """
        pass
    
    @abstractmethod
    def save_config(self, config_name: str, config_data: Dict[str, Any]) -> bool:
        """
        保存配置
        
        参数:
            config_name: 配置名称
            config_data: 配置数据
            
        返回:
            是否保存成功
        """
        pass
    
    @abstractmethod
    def validate_config(self, config_name: str, config_data: Dict[str, Any]) -> List[str]:
        """
        验证配置
        
        参数:
            config_name: 配置名称
            config_data: 配置数据
            
        返回:
            验证错误列表，空列表表示验证通过
        """
        pass


class ITaskService(ABC):
    """任务服务接口"""
    
    @abstractmethod
    def get_active_tasks(self) -> List[Dict[str, Any]]:
        """
        获取活动任务列表
        
        返回:
            任务信息列表
        """
        pass
    
    @abstractmethod
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务状态
        
        参数:
            task_id: 任务ID
            
        返回:
            任务状态信息或None
        """
        pass
    
    @abstractmethod
    async def cancel_task(self, task_id: str) -> bool:
        """
        取消任务
        
        参数:
            task_id: 任务ID
            
        返回:
            是否成功取消
        """
        pass
    
    @abstractmethod
    def clear_completed_tasks(self) -> int:
        """
        清理已完成的任务
        
        返回:
            清理的任务数量
        """
        pass


class IFileOperationsService(ABC):
    """
    文件操作服务接口

    遵循RULE-001: 模块职责单一原则 - 只负责文件系统操作
    遵循RULE-003: 事件驱动通信规范 - 通过事件总线通信
    遵循RULE-005: 异步任务实现 - 长时间运行的文件操作
    """

    @abstractmethod
    async def execute_operations(self, request) -> AsyncGenerator:
        """
        执行批量文件操作

        参数:
            request: 文件操作请求DTO

        返回:
            异步生成器，产生进度更新
        """
        pass

    @abstractmethod
    async def get_operation_result(self, task_id: str):
        """
        获取操作结果

        参数:
            task_id: 任务ID

        返回:
            操作结果DTO或None
        """
        pass

    @abstractmethod
    async def validate_operations(self, request) -> List[str]:
        """
        验证文件操作请求

        参数:
            request: 文件操作请求DTO

        返回:
            验证错误列表
        """
        pass

    @abstractmethod
    async def preview_operations(self, request) -> List:
        """
        预览文件操作

        参数:
            request: 文件操作请求DTO

        返回:
            操作预览列表
        """
        pass

    @abstractmethod
    async def cancel_operations(self, task_id: str) -> bool:
        """
        取消文件操作

        参数:
            task_id: 任务ID

        返回:
            是否成功取消
        """
        pass

    @abstractmethod
    async def rollback_operations(self, task_id: str) -> bool:
        """
        回滚文件操作

        参数:
            task_id: 任务ID

        返回:
            是否成功回滚
        """
        pass

    @abstractmethod
    async def get_operation_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """
        获取操作历史

        参数:
            limit: 限制返回数量

        返回:
            操作历史列表
        """
        pass
