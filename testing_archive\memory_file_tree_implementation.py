#!/usr/bin/env python3
"""
内存预加载文件树实现方案
"""

import asyncio
import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import threading

@dataclass
class FileNode:
    """文件节点数据结构"""
    path: str
    name: str
    is_dir: bool
    size: int = 0
    depth: int = 0
    parent_path: str = ""
    children: Dict[str, 'FileNode'] = None
    file_data: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.children is None:
            self.children = {}

class MemoryFileTreeManager:
    """内存文件树管理器"""
    
    def __init__(self, db_manager, logger):
        self.db_manager = db_manager
        self.logger = logger
        
        # 内存存储
        self.memory_tree: Dict[str, FileNode] = {}
        self.root_nodes: List[FileNode] = []
        self.path_index: Dict[str, FileNode] = {}
        
        # 分层缓存
        self.layer_cache: Dict[int, List[FileNode]] = {}
        self.loaded_layers: set = set()
        
        # 状态管理
        self.is_loading = False
        self.load_progress = 0
        self.total_files = 0
        self.loaded_files = 0
        
        # 配置
        self.preload_depth = 2  # 预加载深度
        self.batch_size = 1000  # 批处理大小
        
    async def load_to_memory(self, progress_callback=None):
        """加载所有文件到内存"""
        try:
            self.is_loading = True
            start_time = time.time()
            
            self.logger.info("[内存树] 开始加载文件到内存...")
            
            # 1. 获取所有文件数据
            files_data = await self._get_all_files_async()
            if not files_data:
                self.logger.warning("[内存树] 没有文件数据")
                return False
            
            self.total_files = len(files_data)
            self.logger.info(f"[内存树] 开始处理 {self.total_files} 个文件")
            
            # 2. 构建内存树结构
            await self._build_memory_tree(files_data, progress_callback)
            
            # 3. 构建索引
            self._build_indexes()
            
            # 4. 分层缓存
            self._build_layer_cache()
            
            elapsed_time = time.time() - start_time
            self.logger.info(f"[内存树] 内存加载完成，耗时: {elapsed_time:.2f}秒, "
                           f"根节点数: {len(self.root_nodes)}, "
                           f"总节点数: {len(self.path_index)}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"[内存树] 加载到内存失败: {e}")
            return False
        finally:
            self.is_loading = False
    
    async def _get_all_files_async(self):
        """异步获取所有文件"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.db_manager.get_all_files)
    
    async def _build_memory_tree(self, files_data, progress_callback=None):
        """构建内存树结构"""
        self.logger.info("[内存树] 开始构建内存树结构...")
        
        # 按路径长度排序，确保父目录先创建
        files_data.sort(key=lambda x: len(x.get('path', '').split('/')))
        
        batch_count = 0
        for i, file_data in enumerate(files_data):
            # 检查中断
            if hasattr(self, 'interrupt_event') and self.interrupt_event.is_set():
                self.logger.info("[内存树] 内存树构建被中断")
                return
            
            # 处理单个文件
            await self._process_file_to_memory(file_data)
            
            self.loaded_files += 1
            batch_count += 1
            
            # 批量更新进度
            if batch_count >= self.batch_size:
                self.load_progress = int((self.loaded_files / self.total_files) * 100)
                
                if progress_callback:
                    progress_callback(self.load_progress, 
                                    f"构建内存树: {self.loaded_files}/{self.total_files} ({self.load_progress}%)")
                
                self.logger.info(f"[内存树] 构建进度: {self.load_progress}% ({self.loaded_files}/{self.total_files})")
                
                # 让出控制权
                await asyncio.sleep(0.001)
                batch_count = 0
    
    async def _process_file_to_memory(self, file_data):
        """处理单个文件到内存"""
        try:
            file_path = file_data.get('path', '')
            if not file_path:
                return
            
            # 规范化路径
            file_path = file_path.replace('\\', '/')
            path_parts = file_path.split('/')
            
            # 创建文件节点
            file_node = FileNode(
                path=file_path,
                name=path_parts[-1],
                is_dir=file_data.get('is_dir', False),
                size=file_data.get('size', 0),
                depth=file_data.get('depth', len(path_parts)),
                file_data=file_data
            )
            
            # 添加到路径索引
            self.path_index[file_path] = file_node
            
            # 构建父子关系
            if len(path_parts) == 1:
                # 根节点
                self.root_nodes.append(file_node)
                self.memory_tree[file_path] = file_node
            else:
                # 子节点
                parent_path = '/'.join(path_parts[:-1])
                file_node.parent_path = parent_path
                
                # 确保父目录存在
                parent_node = await self._ensure_parent_exists(parent_path, path_parts[:-1])
                if parent_node:
                    parent_node.children[file_node.name] = file_node
                    
        except Exception as e:
            self.logger.error(f"[内存树] 处理文件失败 {file_path}: {e}")
    
    async def _ensure_parent_exists(self, parent_path, path_parts):
        """确保父目录存在"""
        if parent_path in self.path_index:
            return self.path_index[parent_path]
        
        # 创建父目录节点
        parent_node = FileNode(
            path=parent_path,
            name=path_parts[-1],
            is_dir=True,
            depth=len(path_parts),
            parent_path='/'.join(path_parts[:-1]) if len(path_parts) > 1 else ""
        )
        
        self.path_index[parent_path] = parent_node
        
        # 递归确保祖父目录存在
        if len(path_parts) > 1:
            grandparent_path = '/'.join(path_parts[:-1])
            grandparent_node = await self._ensure_parent_exists(grandparent_path, path_parts[:-1])
            if grandparent_node:
                grandparent_node.children[parent_node.name] = parent_node
        else:
            # 根目录
            self.root_nodes.append(parent_node)
            self.memory_tree[parent_path] = parent_node
        
        return parent_node
    
    def _build_indexes(self):
        """构建索引"""
        self.logger.info("[内存树] 构建索引...")
        
        # 路径索引已在构建过程中创建
        # 可以添加其他索引，如按文件类型、大小等
        
        self.logger.info(f"[内存树] 索引构建完成，路径索引: {len(self.path_index)} 项")
    
    def _build_layer_cache(self):
        """构建分层缓存"""
        self.logger.info("[内存树] 构建分层缓存...")
        
        for node in self.path_index.values():
            depth = node.depth
            if depth not in self.layer_cache:
                self.layer_cache[depth] = []
            self.layer_cache[depth].append(node)
        
        # 按深度排序
        for depth in self.layer_cache:
            self.layer_cache[depth].sort(key=lambda x: x.path)
        
        self.logger.info(f"[内存树] 分层缓存构建完成，层级数: {len(self.layer_cache)}")
    
    def get_children(self, parent_path: str = "") -> List[FileNode]:
        """获取子节点"""
        if not parent_path:
            return self.root_nodes
        
        parent_node = self.path_index.get(parent_path)
        if parent_node:
            return list(parent_node.children.values())
        return []
    
    def get_node(self, path: str) -> Optional[FileNode]:
        """获取节点"""
        return self.path_index.get(path)
    
    def search_files(self, query: str, max_results: int = 1000) -> List[FileNode]:
        """搜索文件"""
        results = []
        query_lower = query.lower()
        
        for node in self.path_index.values():
            if len(results) >= max_results:
                break
                
            if query_lower in node.name.lower() or query_lower in node.path.lower():
                results.append(node)
        
        return results
    
    def get_layer_nodes(self, depth: int) -> List[FileNode]:
        """获取指定深度的节点"""
        return self.layer_cache.get(depth, [])
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = {
            'total_nodes': len(self.path_index),
            'root_nodes': len(self.root_nodes),
            'max_depth': max(self.layer_cache.keys()) if self.layer_cache else 0,
            'layer_distribution': {depth: len(nodes) for depth, nodes in self.layer_cache.items()},
            'memory_usage_mb': self._estimate_memory_usage(),
            'is_loading': self.is_loading,
            'load_progress': self.load_progress
        }
        return stats
    
    def _estimate_memory_usage(self) -> float:
        """估算内存使用量（MB）"""
        # 粗略估算：每个节点约200字节
        node_count = len(self.path_index)
        estimated_bytes = node_count * 200
        return estimated_bytes / (1024 * 1024)

class VirtualizedFileTreeRenderer:
    """虚拟化文件树渲染器"""
    
    def __init__(self, memory_manager: MemoryFileTreeManager, tree_widget):
        self.memory_manager = memory_manager
        self.tree_widget = tree_widget
        
        # 虚拟化参数
        self.visible_start = 0
        self.visible_count = 100  # 同时显示的节点数
        self.item_height = 20
        
        # 当前显示状态
        self.current_nodes = []
        self.expanded_paths = set()
    
    def render_initial_view(self):
        """渲染初始视图"""
        # 清空现有内容
        self.tree_widget.delete(*self.tree_widget.get_children())
        
        # 获取根节点
        root_nodes = self.memory_manager.get_children("")
        
        # 渲染可见部分
        self._render_nodes(root_nodes[:self.visible_count])
    
    def _render_nodes(self, nodes: List[FileNode]):
        """渲染节点列表"""
        for node in nodes:
            # 创建树项
            item_id = self.tree_widget.insert(
                "", "end",
                text=node.name,
                values=(
                    node.name,
                    self._format_size(node.size),
                    "文件夹" if node.is_dir else "文件",
                    "",
                    node.path
                ),
                tags=("folder" if node.is_dir else "file",)
            )
            
            # 如果是文件夹且有子项，添加占位符
            if node.is_dir and node.children:
                self.tree_widget.insert(item_id, "end", text="加载中...")
    
    def expand_folder(self, folder_path: str):
        """展开文件夹"""
        if folder_path in self.expanded_paths:
            return
        
        # 从内存获取子节点
        children = self.memory_manager.get_children(folder_path)
        
        # 找到对应的树项
        for item_id in self.tree_widget.get_children():
            item_values = self.tree_widget.item(item_id, 'values')
            if len(item_values) > 4 and item_values[4] == folder_path:
                # 清除占位符
                self.tree_widget.delete(*self.tree_widget.get_children(item_id))
                
                # 添加子项
                for child in children:
                    child_id = self.tree_widget.insert(
                        item_id, "end",
                        text=child.name,
                        values=(
                            child.name,
                            self._format_size(child.size),
                            "文件夹" if child.is_dir else "文件",
                            "",
                            child.path
                        ),
                        tags=("folder" if child.is_dir else "file",)
                    )
                    
                    # 如果是文件夹且有子项，添加占位符
                    if child.is_dir and child.children:
                        self.tree_widget.insert(child_id, "end", text="加载中...")
                
                break
        
        self.expanded_paths.add(folder_path)
    
    def _format_size(self, size: int) -> str:
        """格式化文件大小"""
        if size < 1024:
            return f"{size} B"
        elif size < 1024 * 1024:
            return f"{size / 1024:.1f} KB"
        elif size < 1024 * 1024 * 1024:
            return f"{size / (1024 * 1024):.1f} MB"
        else:
            return f"{size / (1024 * 1024 * 1024):.1f} GB"

# 使用示例
async def example_usage():
    """使用示例"""
    # 模拟数据库管理器和日志器
    class MockDBManager:
        def get_all_files(self):
            # 模拟返回文件数据
            return [
                {'path': '/root/folder1/file1.txt', 'size': 1024, 'depth': 3, 'is_dir': False},
                {'path': '/root/folder1', 'size': 0, 'depth': 2, 'is_dir': True},
                {'path': '/root', 'size': 0, 'depth': 1, 'is_dir': True},
                # ... 更多文件
            ]
    
    class MockLogger:
        def info(self, msg): print(f"INFO: {msg}")
        def warning(self, msg): print(f"WARNING: {msg}")
        def error(self, msg): print(f"ERROR: {msg}")
    
    # 创建内存管理器
    memory_manager = MemoryFileTreeManager(MockDBManager(), MockLogger())
    
    # 加载到内存
    def progress_callback(percent, message):
        print(f"进度: {percent}% - {message}")
    
    success = await memory_manager.load_to_memory(progress_callback)
    
    if success:
        # 获取统计信息
        stats = memory_manager.get_statistics()
        print(f"统计信息: {stats}")
        
        # 搜索文件
        results = memory_manager.search_files("file1")
        print(f"搜索结果: {len(results)} 个文件")

if __name__ == "__main__":
    asyncio.run(example_usage())
