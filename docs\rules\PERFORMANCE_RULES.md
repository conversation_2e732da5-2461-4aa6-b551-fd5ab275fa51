# 智能文件管理器性能规则

## ⚡ 性能优化强制规则

### PERF-001: 大文件处理规范
**优先级**: 🔴 最高

**文件大小分类**:
- **小文件**: < 10MB
- **中文件**: 10MB - 100MB  
- **大文件**: 100MB - 1GB
- **超大文件**: > 1GB

**处理规范**:
- ✅ **必须**: 大文件使用分块读取，默认块大小8MB
- ✅ **必须**: 超大文件块大小可调整至32MB
- ✅ **必须**: 实现断点续传机制
- ✅ **必须**: 提供进度报告和取消功能
- ❌ **禁止**: 一次性将大文件完全加载到内存
- ❌ **禁止**: 在UI线程中处理大文件

**大文件处理示例**:
```python
def process_large_file(self, file_path: str, chunk_size: int = 8 * 1024 * 1024):
    """处理大文件的标准模式"""
    file_size = os.path.getsize(file_path)
    processed_bytes = 0
    
    with open(file_path, 'rb') as f:
        while chunk := f.read(chunk_size):
            # 处理数据块
            self.process_chunk(chunk)
            
            # 更新进度
            processed_bytes += len(chunk)
            progress = (processed_bytes / file_size) * 100
            self.report_progress(progress)
            
            # 检查中断信号
            if self.interrupt_event.is_set():
                raise InterruptedException("处理被中断")
```

### PERF-002: 内存使用限制
**优先级**: 🔴 最高

**内存限制标准**:
- **总内存限制**: 最大2GB
- **单任务内存限制**: 最大512MB
- **UI组件内存限制**: 最大256MB
- **缓存内存限制**: 最大256MB

**内存管理规范**:
- ✅ **必须**: 实现内存使用监控
- ✅ **必须**: 超过限制时自动触发垃圾回收
- ✅ **必须**: 使用LRU缓存策略
- ✅ **必须**: 及时释放不再使用的资源
- ❌ **禁止**: 无限制地缓存数据
- ❌ **禁止**: 创建大量临时对象

**内存监控示例**:
```python
import psutil
import gc

class MemoryManager:
    def __init__(self, max_memory_mb: int = 2048):
        self.max_memory_bytes = max_memory_mb * 1024 * 1024
        self.warning_threshold = 0.8  # 80%警告阈值
    
    def check_memory_usage(self):
        current_usage = psutil.Process().memory_info().rss
        usage_ratio = current_usage / self.max_memory_bytes
        
        if usage_ratio > self.warning_threshold:
            self.logger.warning(f"内存使用率: {usage_ratio:.1%}")
            
        if usage_ratio > 1.0:
            self.logger.error("内存使用超限，触发垃圾回收")
            gc.collect()
            
        return current_usage
```

### PERF-003: 数据库查询优化
**优先级**: 🔴 最高

**查询性能标准**:
- **简单查询**: < 100ms
- **复杂查询**: < 500ms
- **批量查询**: < 2000ms
- **聚合查询**: < 5000ms

**优化规范**:
- ✅ **必须**: 所有查询必须使用索引
- ✅ **必须**: 使用批量操作替代循环查询
- ✅ **必须**: 实现查询结果缓存
- ✅ **必须**: 使用分页查询处理大结果集
- ❌ **禁止**: 全表扫描查询
- ❌ **禁止**: 在循环中执行查询
- ❌ **禁止**: 返回超过10000条记录的查询

**索引设计规范**:
```python
# 必需索引
REQUIRED_INDEXES = [
    {"fields": ["path"], "unique": True},
    {"fields": ["hash"], "sparse": True},
    {"fields": ["size", "modified_time"]},
    {"fields": ["is_junk", "size"]},
    {"fields": ["is_whitelist", "whitelist_type"]},
    {"fields": ["scan_id", "created_time"]},
]

# 查询优化示例
def find_files_optimized(self, criteria: Dict[str, Any], limit: int = 1000):
    """优化的文件查询"""
    # 使用索引友好的查询条件
    query = self._build_indexed_query(criteria)
    
    # 分页查询
    return self.db.find(query).limit(limit).hint("path_1")
```

### PERF-004: 并发处理优化
**优先级**: 🟡 高

**并发限制标准**:
- **CPU密集型任务**: 最多4个并发线程
- **I/O密集型任务**: 最多16个并发线程
- **混合型任务**: 最多8个并发线程
- **数据库连接**: 最多20个并发连接

**并发控制规范**:
- ✅ **必须**: 使用线程池管理并发任务
- ✅ **必须**: 实现任务队列和优先级调度
- ✅ **必须**: 监控线程池使用情况
- ✅ **必须**: 实现背压控制机制
- ❌ **禁止**: 无限制创建线程
- ❌ **禁止**: 在UI线程中执行长时间任务

**线程池配置示例**:
```python
import concurrent.futures
from threading import Semaphore

class TaskExecutor:
    def __init__(self):
        # CPU密集型线程池
        self.cpu_executor = concurrent.futures.ThreadPoolExecutor(
            max_workers=4, 
            thread_name_prefix="cpu_worker"
        )
        
        # I/O密集型线程池
        self.io_executor = concurrent.futures.ThreadPoolExecutor(
            max_workers=16,
            thread_name_prefix="io_worker"
        )
        
        # 并发控制信号量
        self.file_semaphore = Semaphore(8)  # 最多8个文件同时处理
        self.db_semaphore = Semaphore(20)   # 最多20个数据库连接
```

### PERF-005: UI响应性能规范
**优先级**: 🟡 高

**响应时间标准**:
- **按钮点击响应**: < 50ms
- **界面切换**: < 200ms
- **数据加载显示**: < 500ms
- **进度更新频率**: 最多每100ms一次

**UI优化规范**:
- ✅ **必须**: UI操作在主线程中执行
- ✅ **必须**: 长时间操作使用异步任务
- ✅ **必须**: 实现虚拟滚动处理大列表
- ✅ **必须**: 使用防抖和节流优化频繁操作
- ❌ **禁止**: 在UI线程中执行超过100ms的操作
- ❌ **禁止**: 频繁更新UI组件(>10次/秒)

**UI优化示例**:
```python
import asyncio
from functools import wraps
import time

def debounce(wait_time):
    """防抖装饰器"""
    def decorator(func):
        last_called = [0]
        
        @wraps(func)
        def wrapper(*args, **kwargs):
            current_time = time.time()
            if current_time - last_called[0] >= wait_time:
                last_called[0] = current_time
                return func(*args, **kwargs)
        return wrapper
    return decorator

class FileTreePanel:
    @debounce(0.3)  # 300ms防抖
    def on_search_text_changed(self, text):
        """搜索文本变化处理"""
        asyncio.create_task(self.filter_files_async(text))
```

## 📊 性能监控指标

### 关键性能指标(KPI)
```python
PERFORMANCE_METRICS = {
    "response_time": {
        "file_scan_per_10k": 3.0,      # 秒/万文件
        "hash_calc_per_gb": 8.0,       # 秒/GB
        "duplicate_detection": 20.0,    # 秒
        "ui_response": 0.1,            # 秒
    },
    "throughput": {
        "file_scan_rate": 3000,        # 文件/秒
        "hash_calc_rate": 150,         # MB/秒
        "duplicate_check_rate": 800,   # 文件/秒
        "ui_update_rate": 60,          # fps
    },
    "resource_usage": {
        "max_memory_mb": 2048,         # MB
        "max_cpu_percent": 80,         # %
        "max_disk_io_percent": 70,     # %
        "max_network_mbps": 100,       # Mbps
    }
}
```

### 性能测试用例
```python
class PerformanceTest:
    def test_large_file_processing(self):
        """测试大文件处理性能"""
        file_size = 1024 * 1024 * 1024  # 1GB
        start_time = time.time()
        
        result = self.hash_service.calculate_hash(large_file_path)
        
        elapsed_time = time.time() - start_time
        assert elapsed_time < 8.0, f"大文件处理超时: {elapsed_time}秒"
    
    def test_memory_usage(self):
        """测试内存使用限制"""
        initial_memory = psutil.Process().memory_info().rss
        
        # 执行内存密集型操作
        self.scan_service.scan_large_directory()
        
        peak_memory = psutil.Process().memory_info().rss
        memory_increase = peak_memory - initial_memory
        
        assert memory_increase < 512 * 1024 * 1024, "内存使用超限"
```

## 🔧 性能优化工具

### 性能分析工具
```python
import cProfile
import pstats
from functools import wraps

def profile_performance(func):
    """性能分析装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        profiler = cProfile.Profile()
        profiler.enable()
        
        try:
            result = func(*args, **kwargs)
        finally:
            profiler.disable()
            
            # 保存性能分析结果
            stats = pstats.Stats(profiler)
            stats.sort_stats('cumulative')
            stats.dump_stats(f'profile_{func.__name__}.prof')
            
        return result
    return wrapper
```

### 性能监控服务
```python
class PerformanceMonitor:
    def __init__(self):
        self.metrics = {}
        self.start_time = time.time()
    
    def record_metric(self, name: str, value: float, unit: str = ""):
        """记录性能指标"""
        if name not in self.metrics:
            self.metrics[name] = []
        
        self.metrics[name].append({
            "value": value,
            "timestamp": time.time(),
            "unit": unit
        })
    
    def get_performance_report(self) -> Dict[str, Any]:
        """生成性能报告"""
        report = {
            "duration": time.time() - self.start_time,
            "metrics": {}
        }
        
        for name, values in self.metrics.items():
            if values:
                report["metrics"][name] = {
                    "count": len(values),
                    "avg": sum(v["value"] for v in values) / len(values),
                    "min": min(v["value"] for v in values),
                    "max": max(v["value"] for v in values),
                    "unit": values[0]["unit"]
                }
        
        return report
```

## 🚨 性能违规检测

### 自动检测规则
```bash
# 检查内存使用
python tools/performance_checker.py --check-memory

# 检查响应时间
python tools/performance_checker.py --check-response-time

# 检查并发限制
python tools/performance_checker.py --check-concurrency

# 生成性能报告
python tools/performance_checker.py --generate-report
```

### 性能基准测试
```bash
# 运行基准测试
python tests/performance/benchmark.py

# 对比性能
python tests/performance/compare.py --baseline=v1.0 --current=v2.0
```

## 📈 性能优化建议

### 文件处理优化
1. **批量处理**: 将小文件合并批量处理
2. **并行处理**: 使用多线程处理独立文件
3. **缓存策略**: 缓存频繁访问的文件信息
4. **预读优化**: 预读下一批要处理的文件

### 数据库优化
1. **索引优化**: 为常用查询创建复合索引
2. **查询优化**: 使用explain分析查询计划
3. **连接池**: 复用数据库连接减少开销
4. **批量操作**: 合并多个操作减少网络往返

### UI优化
1. **虚拟化**: 大列表使用虚拟滚动
2. **懒加载**: 按需加载UI组件和数据
3. **防抖节流**: 优化频繁触发的事件
4. **异步渲染**: 使用异步方式更新UI

## 📚 相关文档
- [编程规则](CODING_RULES.md)
- [架构规则](ARCHITECTURE_RULES.md)
- [性能测试指南](../guidelines/PERFORMANCE_TESTING_GUIDE.md)
