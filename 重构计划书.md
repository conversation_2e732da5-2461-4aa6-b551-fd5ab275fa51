# Smart File Manager 重复代码重构计划书

## 一、重构目标
- 消除项目中所有功能重复、实现重复、命名不统一的代码。
- 提高代码复用率、可维护性和可读性。
- 降低Bug率，提升开发效率和系统性能。
- 严格遵循项目命名、路径、异步、异常、日志等规范。

---

## 二、重构范围
1. 重复的重复文件查找器类
2. 重复的文件扫描方法
3. 重复的路径处理逻辑
4. 重复的数据库查询方法
5. 重复的聚合管道逻辑
6. 重复的任务处理逻辑
7. 重复的哈希计算逻辑
8. 重复的进度更新模式
9. 重复的异常处理模式
10. 重复的文件扫描逻辑
11. 重复的中断检查模式
12. 重复的日志记录模式

---

## 三、重构原则
- **单一实现**：每类通用功能只保留一个权威实现。
- **模块化**：通用逻辑抽象为工具函数或基类，放入统一模块。
- **接口统一**：对外暴露统一接口，便于调用和维护。
- **严格测试**：每步重构后补充/修正测试，确保无回归。
- **逐步推进**：每次只重构一类重复，分阶段提交，便于回滚和追踪。

---

## 四、重构步骤与任务拆解

### 1. 代码重复点梳理与定位
- 全局搜索所有重复实现，列出具体文件、行号、功能点。
- 输出一份“重复代码清单”。

### 2. 统一路径处理逻辑
- 检查所有路径相关操作，全部替换为`_normalize_path`。
- 删除手写分隔符替换、冗余路径处理函数。

### 3. 合并重复的重复文件查找器类
- 对比`src/core/duplicate_finder.py`中的两个类，合并为一个权威实现。
- 替换全项目对该功能的引用。

### 4. 合并文件扫描方法与逻辑
- 统一文件扫描入口，所有扫描操作调用同一方法。
- 删除冗余扫描实现。

### 5. 合并数据库查询与聚合管道逻辑
- 所有数据库操作集中到`db_manager.py`和`db_operations.py`。
- 聚合管道模板抽象为工具函数。

### 6. 合并任务处理与中断检查逻辑
- 所有异步任务统一交由`UnifiedTaskManager`管理。
- 中断检查封装为装饰器或工具函数。

### 7. 合并哈希计算逻辑
- 只保留`hash_calculator.py`中的实现，删除其他冗余代码。

### 8. 合并进度更新与异常处理模式
- 进度更新抽象为统一回调接口。
- 异常处理统一继承自自定义异常基类。

### 9. 合并日志记录模式
- 只允许通过统一`logger`对象记录日志，删除`print`和自定义日志。

### 10. 全项目替换与冗余代码删除
- 替换所有旧实现的调用，删除冗余代码。

### 11. 测试与回归验证
- 修正和补充测试用例，确保所有功能正常。
- 跑全量单元测试和集成测试。

### 12. 代码审查与文档更新
- 代码审查，确保无遗漏。
- 更新开发文档和重构说明。

---

## 五、重构进度追踪（Todo清单）

- [x] 1. 梳理重复代码清单
- [x] 2. 统一路径处理逻辑
- [x] 3. 合并重复文件查找器类
- [x] 4. 合并文件扫描方法
- [x] 5. 合并数据库查询与聚合管道
- [x] 6. 合并任务处理与中断检查
- [x] 7. 合并哈希计算逻辑
- [x] 8. 合并进度更新与异常处理
- [x] 9. 合并日志记录模式
- [x] 10. 全项目替换与冗余代码删除
- [x] 11. 测试与回归验证
- [x] 12. 代码审查与文档更新

---

#### 【进度说明】
- 已完成代码审查和文档同步更新，重构计划全部闭环，项目结构规范、无冗余、文档与实现一致。

### 1. 代码重复点清单（部分示例）

#### 1.1 重复文件查找器类
- `src/core/duplicate_finder.py`  
  - `DuplicateFinder`（基于数据库）
  - `LocalDuplicateFinder`（本地不依赖数据库）
  - 两者结构、方法高度相似，存在大量重复代码（如中断处理、进度跟踪、扫描流程等）

#### 1.2 文件扫描方法与逻辑
- `src/core/file_scanner.py`  
  - `scan_and_update_multiple_directories_async`
  - `scan_directory_async`
  - 还有部分同步/异步扫描逻辑与`duplicate_finder.py`、`LocalDuplicateFinder`中的扫描方法重复

#### 1.3 路径处理逻辑
- 多处手动`os.path`拼接、分隔符替换，未统一调用`_normalize_path`  
  - 见`duplicate_finder.py`、`file_scanner.py`、`LocalDuplicateFinder`等

#### 1.4 数据库查询与聚合管道
- `src/data/db_manager.py`、`src/data/db_operations.py`  
  - `find_duplicate_files`、`find_duplicate_files_async`、`find_duplicates_with_timeout`等
  - 聚合管道模板、查询条件、结果处理高度重复

#### 1.5 任务处理与中断检查
- `DuplicateFinder`、`LocalDuplicateFinder`、`FileScanner`等均有各自的中断标志、检查方法、线程/协程管理，未统一

#### 1.6 哈希计算逻辑
- `src/core/hash_calculator.py`、`duplicate_finder.py`、`LocalDuplicateFinder`等均有哈希计算相关实现

#### 1.7 进度更新与异常处理
- 进度回调、异常捕获、日志记录等模式在多个类/方法中重复实现

#### 1.8 日志记录模式
- 多处自定义日志、`print`、不同风格的日志调用，未统一

---

## 六、重构注意事项
- 每步重构后立即提交并测试，防止大范围冲突。
- 保证所有接口、字段、异常、日志、路径等命名一致。
- 发现新重复点及时补充进计划。
- 保证所有重构均有测试覆盖。

---

## 七、重构完成标准
- 所有重复实现被消除，功能只保留唯一实现。
- 所有测试通过，功能无回归。
- 代码结构清晰，文档同步更新。
- 团队成员能快速理解和维护。 