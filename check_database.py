#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
检查数据库中的数据插入情况
"""

import asyncio
from src.data.db_manager import MongoDBManager
from src.data.config_manager import ConfigManager

async def check_database():
    """检查数据库中的数据"""
    try:
        # 初始化配置管理器
        config_manager = ConfigManager()
        
        # 初始化数据库管理器
        db_manager = MongoDBManager()
        
        # 连接数据库
        if not db_manager._ensure_connected("检查数据库"):
            print("❌ 数据库连接失败")
            return
            
        print("✅ 数据库连接成功")
        
        # 检查文件集合中的数据
        file_count = db_manager.collection.count_documents({})
        print(f"📁 文件集合中的文档数量: {file_count}")
        
        if file_count > 0:
            # 获取最新的几个文件记录
            latest_files = list(db_manager.collection.find().sort("_id", -1).limit(5))
            print("\n📋 最新的5个文件记录:")
            for i, file_doc in enumerate(latest_files, 1):
                print(f"  {i}. 路径: {file_doc.get('path', 'N/A')}")
                print(f"     名称: {file_doc.get('name', 'N/A')}")
                print(f"     大小: {file_doc.get('size', 'N/A')} 字节")
                print(f"     修改时间: {file_doc.get('modified_time', 'N/A')}")
                print(f"     创建时间: {file_doc.get('created_time', 'N/A')}")
                print()
        
        # 检查文件夹集合中的数据
        if hasattr(db_manager, 'folders_collection'):
            folder_count = db_manager.folders_collection.count_documents({})
            print(f"📂 文件夹集合中的文档数量: {folder_count}")
        
        # 检查树节点集合中的数据
        if hasattr(db_manager, 'tree_nodes_collection'):
            tree_node_count = db_manager.tree_nodes_collection.count_documents({})
            print(f"🌳 树节点集合中的文档数量: {tree_node_count}")
            
        print("\n✅ 数据库检查完成")
        
    except Exception as e:
        print(f"❌ 检查数据库时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(check_database())