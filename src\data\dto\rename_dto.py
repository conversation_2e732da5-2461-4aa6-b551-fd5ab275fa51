#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
重命名相关DTO定义

包含文件重命名、批量重命名等相关的数据传输对象
"""

from dataclasses import dataclass
from typing import List, Optional, Dict, Any, Pattern
from enum import Enum
import uuid
import re
from .base_dto import BaseDTO


# 常量定义
DEFAULT_COUNTER_DIGITS = 3  # 默认计数器位数


class RenameRuleType(Enum):
    """重命名规则类型枚举"""
    REPLACE_TEXT = "replace_text"
    ADD_PREFIX = "add_prefix"
    ADD_SUFFIX = "add_suffix"
    REMOVE_TEXT = "remove_text"
    CHANGE_CASE = "change_case"
    ADD_COUNTER = "add_counter"
    REGEX_REPLACE = "regex_replace"
    DATE_FORMAT = "date_format"
    CUSTOM_FORMAT = "custom_format"


class CaseType(Enum):
    """大小写类型枚举"""
    UPPER = "upper"
    LOWER = "lower"
    TITLE = "title"
    SENTENCE = "sentence"


@dataclass(frozen=True)
class RenameRule(BaseDTO):
    """
    重命名规则DTO
    """
    rule_type: RenameRuleType
    old_text: Optional[str] = None
    new_text: Optional[str] = None
    case_type: Optional[CaseType] = None
    regex_pattern: Optional[str] = None
    date_format: Optional[str] = None
    counter_start: int = 1
    counter_step: int = 1
    counter_digits: int = DEFAULT_COUNTER_DIGITS
    apply_to_extension: bool = False
    
    def validate(self) -> List[str]:
        """验证重命名规则"""
        errors = []

        # 验证规则类型特定的字段
        errors.extend(self._validate_rule_type_fields())

        # 验证计数器相关字段
        errors.extend(self._validate_counter_fields())

        return errors

    def _validate_rule_type_fields(self) -> List[str]:
        """验证规则类型特定的字段"""
        errors = []

        validation_map = {
            RenameRuleType.REPLACE_TEXT: self._validate_replace_text,
            RenameRuleType.ADD_PREFIX: self._validate_add_prefix,
            RenameRuleType.ADD_SUFFIX: self._validate_add_suffix,
            RenameRuleType.REMOVE_TEXT: self._validate_remove_text,
            RenameRuleType.CHANGE_CASE: self._validate_change_case,
            RenameRuleType.REGEX_REPLACE: self._validate_regex_replace,
            RenameRuleType.DATE_FORMAT: self._validate_date_format,
        }

        validator = validation_map.get(self.rule_type)
        if validator:
            errors.extend(validator())

        return errors

    def _validate_replace_text(self) -> List[str]:
        """验证替换文本规则"""
        return ["替换文本规则需要指定原文本"] if not self.old_text else []

    def _validate_add_prefix(self) -> List[str]:
        """验证添加前缀规则"""
        return ["添加前缀规则需要指定前缀文本"] if not self.new_text else []

    def _validate_add_suffix(self) -> List[str]:
        """验证添加后缀规则"""
        return ["添加后缀规则需要指定后缀文本"] if not self.new_text else []

    def _validate_remove_text(self) -> List[str]:
        """验证删除文本规则"""
        return ["删除文本规则需要指定要删除的文本"] if not self.old_text else []

    def _validate_change_case(self) -> List[str]:
        """验证改变大小写规则"""
        return ["改变大小写规则需要指定大小写类型"] if not self.case_type else []

    def _validate_regex_replace(self) -> List[str]:
        """验证正则替换规则"""
        if not self.regex_pattern:
            return ["正则替换规则需要指定正则表达式"]

        try:
            re.compile(self.regex_pattern)
            return []
        except re.error as e:
            return [f"正则表达式无效: {e}"]

    def _validate_date_format(self) -> List[str]:
        """验证日期格式规则"""
        return ["日期格式规则需要指定日期格式"] if not self.date_format else []

    def _validate_counter_fields(self) -> List[str]:
        """验证计数器相关字段"""
        errors = []

        if self.counter_start < 0:
            errors.append("计数器起始值不能为负数")

        if self.counter_step <= 0:
            errors.append("计数器步长必须大于0")

        if self.counter_digits <= 0:
            errors.append("计数器位数必须大于0")

        return errors


@dataclass(frozen=True)
class RenameRequest(BaseDTO):
    """
    重命名请求DTO
    """
    task_id: str
    files: List[str]  # 要重命名的文件路径列表
    rules: List[RenameRule]
    preview_only: bool = True
    backup: bool = True
    overwrite_existing: bool = False
    
    def validate(self) -> List[str]:
        """验证重命名请求"""
        errors = []
        
        if not self.task_id:
            errors.append("任务ID不能为空")
        
        if not self.files:
            errors.append("文件列表不能为空")
        
        if not self.rules:
            errors.append("重命名规则列表不能为空")
        
        # 验证每个规则
        for i, rule in enumerate(self.rules):
            rule_errors = rule.validate()
            for error in rule_errors:
                errors.append(f"规则{i+1}: {error}")
        
        return errors
    
    @classmethod
    def new_simple_replace(cls, files: List[str], old_text: str, new_text: str) -> 'RenameRequest':
        """创建简单文本替换请求"""
        rule = RenameRule(
            rule_type=RenameRuleType.REPLACE_TEXT,
            old_text=old_text,
            new_text=new_text
        )
        return cls(
            task_id=str(uuid.uuid4()),
            files=files,
            rules=[rule],
            preview_only=True
        )


@dataclass(frozen=True)
class RenamePreview(BaseDTO):
    """
    重命名预览DTO
    """
    original_path: str
    new_path: str
    original_name: str
    new_name: str
    will_overwrite: bool = False
    has_conflict: bool = False
    error_message: Optional[str] = None
    
    def validate(self) -> List[str]:
        """验证重命名预览"""
        errors = []
        
        if not self.original_path:
            errors.append("原始路径不能为空")
        
        if not self.new_path:
            errors.append("新路径不能为空")
        
        if not self.original_name:
            errors.append("原始文件名不能为空")
        
        if not self.new_name:
            errors.append("新文件名不能为空")
        
        return errors
    
    @property
    def is_valid(self) -> bool:
        """检查重命名是否有效"""
        return not self.has_conflict and not self.error_message


@dataclass(frozen=True)
class RenameResult(BaseDTO):
    """
    重命名结果DTO
    """
    task_id: str
    total_files: int
    successful_renames: int
    failed_renames: int
    skipped_files: int
    previews: List[RenamePreview]
    errors: Optional[List[str]] = None
    warnings: Optional[List[str]] = None
    duration: Optional[float] = None
    
    def validate(self) -> List[str]:
        """验证重命名结果"""
        errors = []
        
        if not self.task_id:
            errors.append("任务ID不能为空")
        
        if self.total_files < 0:
            errors.append("总文件数不能为负数")
        
        if self.successful_renames < 0:
            errors.append("成功重命名数不能为负数")
        
        if self.failed_renames < 0:
            errors.append("失败重命名数不能为负数")
        
        if self.skipped_files < 0:
            errors.append("跳过文件数不能为负数")
        
        if (self.successful_renames + self.failed_renames + self.skipped_files 
            != self.total_files):
            errors.append("成功、失败、跳过的文件数之和应等于总文件数")
        
        if self.duration is not None and self.duration < 0:
            errors.append("执行时长不能为负数")
        
        return errors
    
    @property
    def success_rate(self) -> float:
        """计算成功率"""
        if self.total_files == 0:
            return 0.0
        return self.successful_renames / self.total_files * 100.0


@dataclass(frozen=True)
class RenameOperation(BaseDTO):
    """
    单个重命名操作DTO

    用于记录和回滚单个文件的重命名操作
    """
    operation_id: str
    original_path: str
    new_path: str
    timestamp: float
    backup_path: Optional[str] = None
    success: bool = False
    error_message: Optional[str] = None

    def validate(self) -> List[str]:
        """验证重命名操作"""
        errors = []

        if not self.operation_id:
            errors.append("操作ID不能为空")

        if not self.original_path:
            errors.append("原始路径不能为空")

        if not self.new_path:
            errors.append("新路径不能为空")

        if self.timestamp <= 0:
            errors.append("时间戳必须为正数")

        return errors


@dataclass(frozen=True)
class RenameTransaction(BaseDTO):
    """
    重命名事务DTO

    用于管理一组相关的重命名操作，支持原子性回滚
    """
    transaction_id: str
    task_id: str
    operations: List[RenameOperation]
    start_time: float
    end_time: Optional[float] = None
    committed: bool = False
    rolled_back: bool = False

    def validate(self) -> List[str]:
        """验证重命名事务"""
        errors = []

        if not self.transaction_id:
            errors.append("事务ID不能为空")

        if not self.task_id:
            errors.append("任务ID不能为空")

        if not self.operations:
            errors.append("操作列表不能为空")

        if self.start_time <= 0:
            errors.append("开始时间必须为正数")

        if self.end_time is not None and self.end_time < self.start_time:
            errors.append("结束时间不能早于开始时间")

        if self.committed and self.rolled_back:
            errors.append("事务不能同时被提交和回滚")

        # 验证每个操作
        for i, operation in enumerate(self.operations):
            op_errors = operation.validate()
            for error in op_errors:
                errors.append(f"操作{i+1}: {error}")

        return errors

    @property
    def duration(self) -> Optional[float]:
        """计算事务持续时间"""
        if self.end_time is None:
            return None
        return self.end_time - self.start_time

    @property
    def success_count(self) -> int:
        """成功操作数量"""
        return sum(1 for op in self.operations if op.success)

    @property
    def failure_count(self) -> int:
        """失败操作数量"""
        return sum(1 for op in self.operations if not op.success)


@dataclass(frozen=True)
class RenameHistory(BaseDTO):
    """
    重命名历史记录DTO
    """
    history_id: str
    task_id: str
    transaction_id: str
    timestamp: float
    operation_type: str  # "rename", "undo", "redo"
    file_count: int
    success_count: int
    failure_count: int
    description: Optional[str] = None

    def validate(self) -> List[str]:
        """验证重命名历史"""
        errors = []

        if not self.history_id:
            errors.append("历史记录ID不能为空")

        if not self.task_id:
            errors.append("任务ID不能为空")

        if not self.transaction_id:
            errors.append("事务ID不能为空")

        if self.timestamp <= 0:
            errors.append("时间戳必须为正数")

        if self.operation_type not in ["rename", "undo", "redo"]:
            errors.append("操作类型必须是 rename、undo 或 redo")

        if self.file_count < 0:
            errors.append("文件数量不能为负数")

        if self.success_count < 0:
            errors.append("成功数量不能为负数")

        if self.failure_count < 0:
            errors.append("失败数量不能为负数")

        if self.success_count + self.failure_count != self.file_count:
            errors.append("成功和失败数量之和应等于文件总数")

        return errors
