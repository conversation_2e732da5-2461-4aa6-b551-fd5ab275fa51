#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
基础DTO类定义

提供所有DTO类的基础功能，包括验证、序列化等
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass, asdict
from typing import List, Dict, Any, Optional
import json


@dataclass(frozen=True)
class BaseDTO(ABC):
    """
    所有DTO类的基类
    
    特性:
    - 不可变对象 (frozen=True)
    - 提供验证方法
    - 提供序列化/反序列化方法
    - 类型安全
    """
    
    @abstractmethod
    def validate(self) -> List[str]:
        """
        验证DTO数据的有效性
        
        Returns:
            List[str]: 验证错误列表，空列表表示验证通过
        """
        pass
    
    def to_dict(self) -> Dict[str, Any]:
        """
        将DTO转换为字典

        Returns:
            Dict[str, Any]: DTO的字典表示
        """
        def convert_value(value):
            """转换值为可序列化的格式"""
            from enum import Enum

            if isinstance(value, Enum):
                # Enum类型，返回其值
                return value.value
            elif hasattr(value, '__dataclass_fields__'):
                # dataclass对象
                return asdict(value)
            elif isinstance(value, (list, tuple)):
                # 列表或元组
                return [convert_value(item) for item in value]
            elif isinstance(value, dict):
                # 字典
                return {k: convert_value(v) for k, v in value.items()}
            else:
                # 基本类型
                return value

        result = asdict(self)
        return {k: convert_value(v) for k, v in result.items()}
    
    def to_json(self) -> str:
        """
        将DTO转换为JSON字符串
        
        Returns:
            str: DTO的JSON表示
        """
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BaseDTO':
        """
        从字典创建DTO实例

        Args:
            data: 字典数据

        Returns:
            BaseDTO: DTO实例

        Raises:
            TypeError: 当数据类型不匹配时
            ValueError: 当数据验证失败时
        """
        try:
            # 处理Enum类型的反序列化
            import inspect
            from enum import Enum

            # 获取类的字段类型信息
            if hasattr(cls, '__dataclass_fields__'):
                converted_data = {}
                for field_name, field_info in cls.__dataclass_fields__.items():
                    if field_name in data:
                        field_type = field_info.type
                        value = data[field_name]

                        # 检查是否为Enum类型
                        if (inspect.isclass(field_type) and
                            issubclass(field_type, Enum) and
                            isinstance(value, str)):
                            converted_data[field_name] = field_type(value)
                        else:
                            converted_data[field_name] = value
                    else:
                        # 使用默认值
                        if field_info.default is not field_info.default_factory:
                            converted_data[field_name] = field_info.default
                        elif field_info.default_factory is not field_info.default_factory:
                            converted_data[field_name] = field_info.default_factory()

                data = converted_data

            instance = cls(**data)
            errors = instance.validate()
            if errors:
                raise ValueError(f"DTO验证失败: {', '.join(errors)}")
            return instance
        except TypeError as e:
            raise TypeError(f"DTO创建失败: {e}")
    
    @classmethod
    def from_json(cls, json_str: str) -> 'BaseDTO':
        """
        从JSON字符串创建DTO实例
        
        Args:
            json_str: JSON字符串
            
        Returns:
            BaseDTO: DTO实例
            
        Raises:
            ValueError: 当JSON格式错误或验证失败时
        """
        try:
            data = json.loads(json_str)
            return cls.from_dict(data)
        except json.JSONDecodeError as e:
            raise ValueError(f"JSON解析失败: {e}")
    
    def is_valid(self) -> bool:
        """
        检查DTO是否有效
        
        Returns:
            bool: True表示有效，False表示无效
        """
        return len(self.validate()) == 0


@dataclass(frozen=True)
class ErrorInfo(BaseDTO):
    """
    错误信息DTO
    
    用于标准化错误信息的传递
    """
    code: str
    message: str
    details: Optional[str] = None
    context: Optional[Dict[str, Any]] = None
    
    def validate(self) -> List[str]:
        """验证错误信息"""
        errors = []
        if not self.code:
            errors.append("错误码不能为空")
        if not self.message:
            errors.append("错误消息不能为空")
        return errors


@dataclass(frozen=True)
class ProgressUpdate(BaseDTO):
    """
    进度更新DTO
    
    用于报告任务执行进度
    """
    task_id: str
    progress: float  # 0.0 - 100.0
    status_message: str
    current_item: Optional[str] = None
    total_items: Optional[int] = None
    processed_items: Optional[int] = None
    estimated_time: Optional[int] = None  # 剩余秒数
    speed: Optional[float] = None  # 处理速度
    
    def validate(self) -> List[str]:
        """验证进度更新数据"""
        errors = []
        if not self.task_id:
            errors.append("任务ID不能为空")
        if not (0.0 <= self.progress <= 100.0):
            errors.append("进度值必须在0-100之间")
        if not self.status_message:
            errors.append("状态消息不能为空")
        if self.total_items is not None and self.total_items < 0:
            errors.append("总项目数不能为负数")
        if self.processed_items is not None and self.processed_items < 0:
            errors.append("已处理项目数不能为负数")
        if (self.total_items is not None and self.processed_items is not None 
            and self.processed_items > self.total_items):
            errors.append("已处理项目数不能超过总项目数")
        return errors
