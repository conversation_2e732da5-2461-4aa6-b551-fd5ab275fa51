# 智能文件管理器重构规则使用指南

## 📋 规则文件概览

我已经为您的智能文件管理器项目创建了完整的重构规则和工具集。以下是所有创建的文件：

### 📁 规则文档
```
docs/
├── rules/
│   ├── CODING_RULES.md           # 编程规则 (强制性规则)
│   ├── ARCHITECTURE_RULES.md     # 架构规则 (分层架构、依赖注入等)
│   ├── PERFORMANCE_RULES.md      # 性能规则 (大文件处理、内存限制等)
│   └── CODE_REVIEW_CHECKLIST.md  # 代码审查清单
└── guidelines/
    └── REFACTORING_GUIDE.md      # 重构指南 (详细实施步骤)
```

### 🔧 工具和配置
```
tools/
└── code_checker.py               # 自定义代码规则检查器

config/
├── pylint.rc                     # Pylint配置
├── mypy.ini                      # MyPy类型检查配置
└── pre-commit-config.yaml        # Pre-commit钩子配置

.github/
├── workflows/
│   └── code_quality.yml          # GitHub Actions CI/CD
└── PULL_REQUEST_TEMPLATE.md      # PR模板

.vscode/
├── settings.json                 # VS Code配置
└── tasks.json                    # VS Code任务配置
```

## 🚀 快速开始

### 1. 安装依赖工具
```bash
# 安装Python代码质量工具
pip install pylint mypy black isort flake8 bandit safety pre-commit pytest pytest-cov

# 安装pre-commit钩子
pre-commit install
```

### 2. 运行代码规则检查
```bash
# 检查所有规则
python tools/code_checker.py --check-rules

# 生成详细报告
python tools/code_checker.py --generate-report --output-file=reports/quality_report.txt

# 运行完整质量检查
python tools/code_checker.py --check-rules --generate-report
```

### 3. 在VS Code中使用
- 打开项目后，VS Code会自动应用配置
- 使用 `Ctrl+Shift+P` 运行任务：
  - "Check Code Rules" - 运行规则检查
  - "Run All Quality Checks" - 运行所有质量检查
  - "Format Code" - 格式化代码

## 📋 核心规则说明

### 🔴 强制性规则 (MANDATORY)

#### RULE-001: 模块化设计原则
- ✅ **必须**: UI层只负责界面展示，禁止直接数据库操作
- ✅ **必须**: 业务层只负责业务逻辑，禁止直接UI操作
- ✅ **必须**: 数据层只负责数据存储，禁止业务判定逻辑

#### RULE-002: DTO使用规范
- ✅ **必须**: 模块间数据传递使用DTO对象
- ✅ **必须**: DTO类使用@dataclass装饰器
- ✅ **必须**: 所有字段有类型注解

#### RULE-003: 事件驱动通信规范
- ✅ **必须**: 模块间通过事件总线通信
- ✅ **必须**: 事件命名格式: 动词_名词_状态
- ✅ **必须**: 事件处理异步执行

#### RULE-004: 数据库操作规范
- ✅ **必须**: 使用批量操作 (插入≤1000条, 更新≤500条)
- ✅ **必须**: 使用参数化查询
- ✅ **必须**: 数据库连接使用连接池

#### RULE-005: 异步任务管理规范
- ✅ **必须**: 长时间操作实现为异步任务
- ✅ **必须**: 支持暂停、恢复、取消操作
- ✅ **必须**: 提供进度报告

### ⚡ 性能规则 (PERFORMANCE)

#### PERF-001: 大文件处理规范
- ✅ **必须**: 大文件使用分块读取 (8MB-32MB块)
- ✅ **必须**: 内存使用限制 (总计<2GB, 单任务<512MB)
- ✅ **必须**: 实现断点续传机制

#### PERF-002: 数据库查询优化
- ✅ **必须**: 所有查询使用索引
- ✅ **必须**: 查询结果集<10000条
- ✅ **必须**: 使用批量操作替代循环查询

## 🔍 使用代码检查器

### 基本用法
```bash
# 检查所有规则
python tools/code_checker.py --check-rules

# 生成报告
python tools/code_checker.py --generate-report

# 指定项目根目录
python tools/code_checker.py --check-rules --project-root ./src

# 输出JSON格式
python tools/code_checker.py --generate-report --output-format json
```

### 检查结果示例
```
🔍 开始代码规则检查...
  进度: 100.0% (45/45)

❌ 发现 3 个规则违规:
  ERROR: RULE-001 - src/ui/main_window.py:156 - UI层禁止直接进行数据库操作
  ERROR: RULE-002 - src/services/scan_service.py:89 - DTO类 ScanRequest 必须使用@dataclass装饰器
  ERROR: RULE-004 - src/data/file_repository.py:234 - 禁止在循环中进行单独的数据库操作
```

## 📊 质量报告

代码检查器会生成详细的质量报告，包含：

- **统计信息**: 检查文件数、代码行数、违规数量
- **违规分布**: 按规则和严重程度分类
- **主要违规**: 最需要关注的问题
- **改进建议**: 具体的修复指导

## 🔄 重构实施步骤

按照 `docs/guidelines/REFACTORING_GUIDE.md` 中的详细步骤进行重构：

### 阶段一: 基础架构搭建 (2周)
1. 创建DTO模型定义
2. 实现事件总线系统
3. 定义服务接口

### 阶段二: 业务服务重构 (3周)
1. 重构FileScanService
2. 重构HashCalculationService
3. 重构其他业务服务

### 阶段三: UI层重构 (2周)
1. 拆分MainWindow
2. 重构文件树组件

### 阶段四: 优化与完善 (1周)
1. 性能优化
2. 测试完善

## 🚨 违规处理

### 严重违规 (RULE-001, RULE-002, RULE-003)
- 代码审查不通过
- 必须立即修复
- 不允许合并到主分支

### 一般违规 (RULE-004, RULE-005)
- 警告提示
- 建议在下次迭代中修复
- 可以合并但需要创建技术债务任务

## 📚 相关文档

- [编程规则详细说明](docs/rules/CODING_RULES.md)
- [架构规则详细说明](docs/rules/ARCHITECTURE_RULES.md)
- [性能规则详细说明](docs/rules/PERFORMANCE_RULES.md)
- [代码审查清单](docs/rules/CODE_REVIEW_CHECKLIST.md)
- [重构实施指南](docs/guidelines/REFACTORING_GUIDE.md)

## 💡 最佳实践

1. **每次提交前运行规则检查**
2. **定期生成质量报告监控趋势**
3. **在PR中使用检查清单**
4. **团队定期review规则执行情况**
5. **根据项目发展调整规则**

## 🆘 获取帮助

如果在使用过程中遇到问题：

1. 查看相关规则文档的详细说明
2. 运行 `python tools/code_checker.py --help` 查看工具帮助
3. 检查VS Code任务配置是否正确
4. 确认所有依赖工具已正确安装

---

**注意**: 这些规则旨在提高代码质量和可维护性，请在开发过程中严格遵守。如有特殊情况需要例外处理，请在团队中讨论并记录决策原因。
