#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
树优化管理器 - 智能文件管理器

功能：
- 目录树结构优化
- 文件夹层次分析
- 树节点管理
- 性能优化建议
- 与现有扫描流程集成

作者: Assistant
版本: 1.0.0
创建时间: 2024-01-20
"""

import os
import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Tuple, Set
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path
from collections import defaultdict, deque

# 导入配置管理器
from src.data.config_manager import ConfigManager
from src.data.db_manager import MongoDBManager
from src.utils.unified_progress_manager import get_progress_manager

# 设置日志
logger = logging.getLogger(__name__)


@dataclass
class TreeNode:
    """
    树节点数据结构
    
    表示目录树中的一个节点（文件夹或文件）
    """
    node_id: str  # 唯一标识符
    path: str  # 完整路径
    name: str  # 节点名称
    parent_id: Optional[str] = None  # 父节点ID
    is_directory: bool = True  # 是否为目录
    depth: int = 0  # 树深度
    child_count: int = 0  # 子节点数量
    file_count: int = 0  # 包含的文件数量
    total_size: int = 0  # 总大小（字节）
    created_time: Optional[datetime] = None
    modified_time: Optional[datetime] = None
    
    # 优化相关字段
    optimization_score: float = 0.0  # 优化评分
    suggested_actions: List[str] = field(default_factory=list)  # 建议操作
    performance_impact: str = "low"  # 性能影响：low, medium, high
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "node_id": self.node_id,
            "path": self.path,
            "name": self.name,
            "parent_id": self.parent_id,
            "is_directory": self.is_directory,
            "depth": self.depth,
            "child_count": self.child_count,
            "file_count": self.file_count,
            "total_size": self.total_size,
            "created_time": self.created_time,
            "modified_time": self.modified_time,
            "optimization_score": self.optimization_score,
            "suggested_actions": self.suggested_actions,
            "performance_impact": self.performance_impact
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TreeNode':
        """从字典创建TreeNode对象"""
        return cls(
            node_id=data.get("node_id", ""),
            path=data.get("path", ""),
            name=data.get("name", ""),
            parent_id=data.get("parent_id"),
            is_directory=data.get("is_directory", True),
            depth=data.get("depth", 0),
            child_count=data.get("child_count", 0),
            file_count=data.get("file_count", 0),
            total_size=data.get("total_size", 0),
            created_time=data.get("created_time"),
            modified_time=data.get("modified_time"),
            optimization_score=data.get("optimization_score", 0.0),
            suggested_actions=data.get("suggested_actions", []),
            performance_impact=data.get("performance_impact", "low")
        )


@dataclass
class FolderInfo:
    """
    文件夹信息数据结构

    存储文件夹的详细信息和统计数据
    """
    folder_id: str  # 唯一标识符
    path: str  # 完整路径
    name: str  # 文件夹名称
    parent_path: Optional[str] = None  # 父目录路径
    depth: int = 0  # 目录深度

    # 统计信息
    direct_file_count: int = 0  # 直接包含的文件数量
    total_file_count: int = 0  # 总文件数量（包括子目录）
    direct_folder_count: int = 0  # 直接子文件夹数量
    total_folder_count: int = 0  # 总文件夹数量（包括子目录）
    total_size: int = 0  # 总大小（字节）

    # 新增：文件夹为核心架构的字段
    parent_id: Optional[str] = None  # 父文件夹ID
    child_folder_ids: List[str] = field(default_factory=list)  # 子文件夹ID列表
    files_hash: Optional[str] = None  # 文件内容哈希值（基于文件名）
    files_count: int = 0  # 直接文件数量（与direct_file_count保持一致性）
    subfolders_count: int = 0  # 子文件夹数量（与direct_folder_count保持一致性）
    content_hash: Optional[str] = None  # 内容哈希（兼容现有实现）

    # 时间信息
    created_time: Optional[datetime] = None
    modified_time: Optional[datetime] = None
    last_scanned: Optional[datetime] = None
    last_hash_update: Optional[datetime] = None  # 最后哈希更新时间

    # 扫描和状态信息
    scan_status: str = "pending"  # 扫描状态：pending, scanning, completed, error
    is_network_folder: bool = False  # 是否为网络文件夹

    # 优化信息
    is_optimized: bool = False  # 是否已优化
    optimization_suggestions: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "folder_id": self.folder_id,
            "path": self.path,
            "name": self.name,
            "parent_path": self.parent_path,
            "depth": self.depth,
            "direct_file_count": self.direct_file_count,
            "total_file_count": self.total_file_count,
            "direct_folder_count": self.direct_folder_count,
            "total_folder_count": self.total_folder_count,
            "total_size": self.total_size,
            # 新增字段
            "parent_id": self.parent_id,
            "child_folder_ids": self.child_folder_ids.copy(),
            "files_hash": self.files_hash,
            "files_count": self.files_count,
            "subfolders_count": self.subfolders_count,
            "content_hash": self.content_hash,
            # 时间信息
            "created_time": self.created_time.isoformat() if self.created_time else None,
            "modified_time": self.modified_time.isoformat() if self.modified_time else None,
            "last_scanned": self.last_scanned.isoformat() if self.last_scanned else None,
            "last_hash_update": self.last_hash_update.isoformat() if self.last_hash_update else None,
            # 状态信息
            "scan_status": self.scan_status,
            "is_network_folder": self.is_network_folder,
            # 优化信息
            "is_optimized": self.is_optimized,
            "optimization_suggestions": self.optimization_suggestions.copy()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'FolderInfo':
        """从字典创建FolderInfo对象"""
        # 处理时间字段
        created_time = None
        if data.get("created_time"):
            if isinstance(data["created_time"], str):
                created_time = datetime.fromisoformat(data["created_time"])
            else:
                created_time = data["created_time"]

        modified_time = None
        if data.get("modified_time"):
            if isinstance(data["modified_time"], str):
                modified_time = datetime.fromisoformat(data["modified_time"])
            else:
                modified_time = data["modified_time"]

        last_scanned = None
        if data.get("last_scanned"):
            if isinstance(data["last_scanned"], str):
                last_scanned = datetime.fromisoformat(data["last_scanned"])
            else:
                last_scanned = data["last_scanned"]

        last_hash_update = None
        if data.get("last_hash_update"):
            if isinstance(data["last_hash_update"], str):
                last_hash_update = datetime.fromisoformat(data["last_hash_update"])
            else:
                last_hash_update = data["last_hash_update"]

        return cls(
            folder_id=data.get("folder_id", ""),
            path=data.get("path", ""),
            name=data.get("name", ""),
            parent_path=data.get("parent_path"),
            depth=data.get("depth", 0),
            direct_file_count=data.get("direct_file_count", 0),
            total_file_count=data.get("total_file_count", 0),
            direct_folder_count=data.get("direct_folder_count", 0),
            total_folder_count=data.get("total_folder_count", 0),
            total_size=data.get("total_size", 0),
            # 新增字段
            parent_id=data.get("parent_id"),
            child_folder_ids=data.get("child_folder_ids", []).copy(),
            files_hash=data.get("files_hash"),
            files_count=data.get("files_count", data.get("direct_file_count", 0)),  # 向后兼容
            subfolders_count=data.get("subfolders_count", data.get("direct_folder_count", 0)),  # 向后兼容
            content_hash=data.get("content_hash"),
            # 时间信息
            created_time=created_time,
            modified_time=modified_time,
            last_scanned=last_scanned,
            last_hash_update=last_hash_update,
            # 状态信息
            scan_status=data.get("scan_status", "pending"),
            is_network_folder=data.get("is_network_folder", False),
            # 优化信息
            is_optimized=data.get("is_optimized", False),
            optimization_suggestions=data.get("optimization_suggestions", []).copy()
        )

    def add_child_folder(self, child_folder_id: str) -> None:
        """
        添加子文件夹ID

        参数:
            child_folder_id: 子文件夹ID
        """
        if child_folder_id not in self.child_folder_ids:
            self.child_folder_ids.append(child_folder_id)
            self.subfolders_count = len(self.child_folder_ids)
            self.direct_folder_count = self.subfolders_count  # 保持一致性

    def remove_child_folder(self, child_folder_id: str) -> bool:
        """
        移除子文件夹ID

        参数:
            child_folder_id: 子文件夹ID

        返回:
            是否成功移除
        """
        try:
            self.child_folder_ids.remove(child_folder_id)
            self.subfolders_count = len(self.child_folder_ids)
            self.direct_folder_count = self.subfolders_count  # 保持一致性
            return True
        except ValueError:
            return False

    def update_files_hash(self, new_hash: str, update_time: Optional[datetime] = None) -> None:
        """
        更新文件哈希值

        参数:
            new_hash: 新的哈希值
            update_time: 更新时间（默认为当前时间）
        """
        self.files_hash = new_hash
        self.content_hash = new_hash  # 保持一致性
        self.last_hash_update = update_time or datetime.now()

    def sync_counts(self) -> None:
        """
        同步新旧字段的计数，确保向后兼容性
        """
        # 同步文件数量
        self.files_count = self.direct_file_count
        # 同步文件夹数量
        self.subfolders_count = self.direct_folder_count
        # 确保子文件夹ID列表长度一致
        if len(self.child_folder_ids) != self.subfolders_count:
            # 如果不一致，清空列表（需要重新扫描）
            self.child_folder_ids.clear()

    def is_root_folder(self) -> bool:
        """
        判断是否为根文件夹

        返回:
            是否为根文件夹
        """
        return self.parent_id is None or self.depth <= 1

    def get_children_count(self) -> int:
        """
        获取子项总数（文件夹 + 文件）

        返回:
            子项总数
        """
        return self.subfolders_count + self.files_count


@dataclass
class OptimizationResult:
    """
    优化结果数据结构
    """
    total_nodes: int = 0
    optimized_nodes: int = 0
    suggestions_generated: int = 0
    performance_improvements: List[str] = field(default_factory=list)
    execution_time: float = 0.0
    errors: List[str] = field(default_factory=list)


class TreeOptimizationManager:
    """
    树优化管理器
    
    负责目录树结构的分析、优化和管理
    """
    
    def __init__(self, db_manager: MongoDBManager, config_path: Optional[str] = None):
        """
        初始化树优化管理器
        
        Args:
            db_manager: 数据库管理器实例
            config_path: 配置文件路径，默认使用config/tree_optimization.yaml
        """
        self.db_manager = db_manager
        
        # 加载配置
        self.config_manager = ConfigManager(config_path)
        self.config = self.config_manager.get_tree_optimization_config()
        
        # 性能配置
        self.performance_config = self.config_manager.get_performance_config()
        
        # 初始化组件
        self.progress_manager = get_progress_manager()
        
        # 缓存
        self._node_cache: Dict[str, TreeNode] = {}
        self._folder_cache: Dict[str, FolderInfo] = {}
        
        # 统计信息
        self.stats = {
            "nodes_processed": 0,
            "folders_analyzed": 0,
            "optimizations_applied": 0,
            "last_optimization_time": None
        }
        
        logger.info(f"树优化管理器初始化完成 - 最大深度: {self.config.get('max_depth', 10)}")
    
    async def analyze_directory_tree(self, root_paths: List[str], 
                                   task_id: Optional[str] = None) -> OptimizationResult:
        """
        分析目录树结构
        
        Args:
            root_paths: 根目录路径列表
            task_id: 任务ID，用于进度跟踪
            
        Returns:
            OptimizationResult: 分析结果
        """
        if not task_id:
            task_id = f"tree_analysis_{int(time.time())}"
            
        start_time = time.time()
        result = OptimizationResult()
        
        try:
            logger.info(f"开始分析目录树: {root_paths}")
            
            # 更新进度
            self.progress_manager.update_progress(
                task_id=task_id,
                progress=0,
                status_message="开始分析目录树结构",
                details="正在初始化分析任务"
            )
            
            total_paths = len(root_paths)
            
            for i, root_path in enumerate(root_paths):
                if not os.path.exists(root_path):
                    error_msg = f"路径不存在: {root_path}"
                    logger.warning(error_msg)
                    result.errors.append(error_msg)
                    continue
                
                # 更新进度
                progress = (i / total_paths) * 100
                self.progress_manager.update_progress(
                    task_id=task_id,
                    progress=progress,
                    status_message=f"分析目录: {root_path}",
                    details=f"正在处理目录 {i+1}/{total_paths}"
                )
                
                # 分析单个目录树
                tree_result = await self._analyze_single_tree(root_path, task_id)
                
                # 合并结果
                result.total_nodes += tree_result.total_nodes
                result.optimized_nodes += tree_result.optimized_nodes
                result.suggestions_generated += tree_result.suggestions_generated
                result.performance_improvements.extend(tree_result.performance_improvements)
                result.errors.extend(tree_result.errors)
            
            # 完成分析
            result.execution_time = time.time() - start_time
            
            self.progress_manager.update_progress(
                task_id=task_id,
                progress=100,
                status_message="目录树分析完成",
                details=f"处理了 {result.total_nodes} 个节点，生成 {result.suggestions_generated} 个优化建议"
            )
            
            # 更新统计信息
            self.stats["last_optimization_time"] = datetime.now()
            self.stats["nodes_processed"] += result.total_nodes
            
            logger.info(f"目录树分析完成 - 耗时: {result.execution_time:.2f}秒")
            return result
            
        except Exception as e:
            error_msg = f"目录树分析失败: {e}"
            logger.error(error_msg, exc_info=True)
            result.errors.append(error_msg)
            result.execution_time = time.time() - start_time
            
            self.progress_manager.complete_task(
                task_id=task_id,
                success=False,
                message=error_msg
            )
            
            return result
    
    async def _analyze_single_tree(self, root_path: str, task_id: str) -> OptimizationResult:
        """
        分析单个目录树
        
        Args:
            root_path: 根目录路径
            task_id: 任务ID
            
        Returns:
            OptimizationResult: 分析结果
        """
        result = OptimizationResult()
        
        try:
            # 构建目录树
            tree_nodes = await self._build_directory_tree(root_path)
            result.total_nodes = len(tree_nodes)
            
            # 分析每个节点
            for node in tree_nodes:
                # 计算优化评分
                await self._calculate_optimization_score(node)
                
                # 生成优化建议
                suggestions = await self._generate_optimization_suggestions(node)
                node.suggested_actions = suggestions
                
                if suggestions:
                    result.suggestions_generated += len(suggestions)
                    result.optimized_nodes += 1
                
                # 保存到数据库
                await self._save_tree_node(node)
            
            # 生成性能改进建议
            result.performance_improvements = await self._generate_performance_improvements(tree_nodes)
            
            return result
            
        except Exception as e:
            error_msg = f"分析目录树 {root_path} 失败: {e}"
            logger.error(error_msg, exc_info=True)
            result.errors.append(error_msg)
            return result
    
    async def _build_directory_tree(self, root_path: str) -> List[TreeNode]:
        """
        构建目录树结构
        
        Args:
            root_path: 根目录路径
            
        Returns:
            List[TreeNode]: 树节点列表
        """
        nodes = []
        node_id_counter = 0
        max_depth = self.config.get('max_depth', 10)
        
        # 使用广度优先搜索构建树
        queue = deque([(root_path, None, 0)])  # (path, parent_id, depth)
        
        while queue:
            current_path, parent_id, depth = queue.popleft()
            
            # 检查深度限制
            if depth > max_depth:
                logger.warning(f"达到最大深度限制 {max_depth}，跳过: {current_path}")
                continue
            
            try:
                # 创建节点ID
                node_id = f"node_{node_id_counter}"
                node_id_counter += 1
                
                # 获取路径信息
                path_obj = Path(current_path)
                is_directory = path_obj.is_dir()
                
                # 创建树节点
                node = TreeNode(
                    node_id=node_id,
                    path=str(path_obj),
                    name=path_obj.name,
                    parent_id=parent_id,
                    is_directory=is_directory,
                    depth=depth
                )
                
                # 获取文件/目录统计信息
                if is_directory:
                    await self._populate_directory_stats(node)
                    
                    # 添加子目录到队列
                    try:
                        for child in path_obj.iterdir():
                            if child.is_dir():
                                queue.append((str(child), node_id, depth + 1))
                    except PermissionError:
                        logger.warning(f"无权限访问目录: {current_path}")
                else:
                    await self._populate_file_stats(node)
                
                nodes.append(node)
                
            except Exception as e:
                logger.warning(f"处理路径失败 {current_path}: {e}")
                continue
        
        logger.info(f"构建目录树完成 - 节点数: {len(nodes)}")
        return nodes
    
    async def _populate_directory_stats(self, node: TreeNode):
        """
        填充目录统计信息
        
        Args:
            node: 树节点
        """
        try:
            path_obj = Path(node.path)
            
            # 统计直接子项
            child_count = 0
            file_count = 0
            total_size = 0
            
            for child in path_obj.iterdir():
                child_count += 1
                if child.is_file():
                    file_count += 1
                    try:
                        total_size += child.stat().st_size
                    except OSError:
                        pass  # 忽略无法访问的文件
            
            node.child_count = child_count
            node.file_count = file_count
            node.total_size = total_size
            
            # 获取时间信息
            stat = path_obj.stat()
            node.created_time = datetime.fromtimestamp(stat.st_ctime)
            node.modified_time = datetime.fromtimestamp(stat.st_mtime)
            
        except Exception as e:
            logger.warning(f"获取目录统计信息失败 {node.path}: {e}")
    
    async def _populate_file_stats(self, node: TreeNode):
        """
        填充文件统计信息
        
        Args:
            node: 树节点
        """
        try:
            path_obj = Path(node.path)
            stat = path_obj.stat()
            
            node.total_size = stat.st_size
            node.created_time = datetime.fromtimestamp(stat.st_ctime)
            node.modified_time = datetime.fromtimestamp(stat.st_mtime)
            
        except Exception as e:
            logger.warning(f"获取文件统计信息失败 {node.path}: {e}")
    
    async def _calculate_optimization_score(self, node: TreeNode):
        """
        计算节点的优化评分
        
        Args:
            node: 树节点
        """
        score = 0.0
        
        # 基于深度的评分（深度越深，评分越低）
        max_depth = self.config.get('max_depth', 10)
        depth_score = max(0, (max_depth - node.depth) / max_depth) * 30
        score += depth_score
        
        # 基于子项数量的评分
        if node.is_directory:
            optimal_child_count = self.config.get('optimal_child_count', 20)
            if node.child_count <= optimal_child_count:
                child_score = 30
            else:
                # 子项过多，评分降低
                child_score = max(0, 30 - (node.child_count - optimal_child_count) * 0.5)
            score += child_score
        
        # 基于文件大小的评分
        if node.total_size > 0:
            # 大文件夹可能需要优化
            size_mb = node.total_size / (1024 * 1024)
            if size_mb < 100:  # 小于100MB
                size_score = 20
            elif size_mb < 1000:  # 100MB-1GB
                size_score = 15
            else:  # 大于1GB
                size_score = 10
            score += size_score
        
        # 基于命名规范的评分
        name_score = self._evaluate_naming_convention(node.name)
        score += name_score
        
        node.optimization_score = min(100, score)  # 限制在100分以内
    
    def _evaluate_naming_convention(self, name: str) -> float:
        """
        评估命名规范
        
        Args:
            name: 文件/目录名称
            
        Returns:
            float: 命名评分
        """
        score = 20.0  # 基础分
        
        # 检查特殊字符
        special_chars = set('!@#$%^&*()+=[]{}|\\:;"<>?,./~`')
        if any(char in special_chars for char in name):
            score -= 5
        
        # 检查空格
        if ' ' in name:
            score -= 2
        
        # 检查长度
        if len(name) > 50:
            score -= 3
        elif len(name) < 3:
            score -= 2
        
        # 检查中文字符（可能导致兼容性问题）
        if any('\u4e00' <= char <= '\u9fff' for char in name):
            score -= 1  # 轻微扣分
        
        return max(0, score)
    
    async def _generate_optimization_suggestions(self, node: TreeNode) -> List[str]:
        """
        生成优化建议
        
        Args:
            node: 树节点
            
        Returns:
            List[str]: 优化建议列表
        """
        suggestions = []
        
        # 深度优化建议
        max_depth = self.config.get('max_depth', 10)
        if node.depth > max_depth * 0.8:  # 超过80%的最大深度
            suggestions.append(f"目录层级过深（{node.depth}层），建议重新组织目录结构")
            node.performance_impact = "high"
        
        # 子项数量优化建议
        if node.is_directory:
            optimal_child_count = self.config.get('optimal_child_count', 20)
            if node.child_count > optimal_child_count * 2:
                suggestions.append(f"子项过多（{node.child_count}个），建议创建子分类目录")
                node.performance_impact = "medium"
            elif node.child_count > optimal_child_count:
                suggestions.append(f"子项较多（{node.child_count}个），可考虑进一步分类")
        
        # 文件大小优化建议
        if node.total_size > 5 * 1024 * 1024 * 1024:  # 大于5GB
            suggestions.append("目录占用空间较大，建议检查是否有重复或不必要的文件")
        
        # 命名规范建议
        if node.optimization_score < 70:  # 评分较低
            if any(char in '!@#$%^&*()+=[]{}|\\:;"<>?,./~`' for char in node.name):
                suggestions.append("文件名包含特殊字符，建议使用标准命名规范")
            if len(node.name) > 50:
                suggestions.append("文件名过长，建议简化")
            if ' ' in node.name:
                suggestions.append("文件名包含空格，建议使用下划线或连字符")
        
        return suggestions
    
    async def _generate_performance_improvements(self, nodes: List[TreeNode]) -> List[str]:
        """
        生成性能改进建议
        
        Args:
            nodes: 树节点列表
            
        Returns:
            List[str]: 性能改进建议
        """
        improvements = []
        
        # 统计分析
        total_nodes = len(nodes)
        deep_nodes = sum(1 for node in nodes if node.depth > 8)
        large_dirs = sum(1 for node in nodes if node.is_directory and node.child_count > 50)
        
        # 深度分析
        if deep_nodes > total_nodes * 0.1:  # 超过10%的节点深度过深
            improvements.append("检测到大量深层目录，建议重新设计目录结构以提高访问效率")
        
        # 广度分析
        if large_dirs > 0:
            improvements.append(f"发现 {large_dirs} 个包含大量子项的目录，建议进行分类整理")
        
        # 整体建议
        if total_nodes > 10000:
            improvements.append("目录结构复杂，建议定期进行清理和优化")
        
        return improvements
    
    async def _save_tree_node(self, node: TreeNode):
        """
        保存树节点到数据库
        
        Args:
            node: 树节点
        """
        try:
            await self.db_manager.insert_tree_node(node.to_dict())
            self._node_cache[node.node_id] = node
            
        except Exception as e:
            logger.error(f"保存树节点失败 {node.path}: {e}")
    
    async def get_optimization_report(self, root_path: str) -> Dict[str, Any]:
        """
        获取优化报告
        
        Args:
            root_path: 根目录路径
            
        Returns:
            Dict[str, Any]: 优化报告
        """
        try:
            # 从数据库获取树节点
            nodes = await self.db_manager.get_tree_nodes_by_parent(root_path)
            
            if not nodes:
                return {"error": "未找到相关的树节点数据"}
            
            # 统计分析
            total_nodes = len(nodes)
            optimized_nodes = sum(1 for node in nodes if node.get('suggested_actions'))
            avg_score = sum(node.get('optimization_score', 0) for node in nodes) / total_nodes
            
            # 按性能影响分类
            impact_stats = defaultdict(int)
            for node in nodes:
                impact = node.get('performance_impact', 'low')
                impact_stats[impact] += 1
            
            # 生成报告
            report = {
                "summary": {
                    "total_nodes": total_nodes,
                    "optimized_nodes": optimized_nodes,
                    "optimization_rate": (optimized_nodes / total_nodes) * 100 if total_nodes > 0 else 0,
                    "average_score": round(avg_score, 2)
                },
                "performance_impact": dict(impact_stats),
                "top_suggestions": self._get_top_suggestions(nodes),
                "statistics": self.stats,
                "generated_at": datetime.now().isoformat()
            }
            
            return report
            
        except Exception as e:
            logger.error(f"生成优化报告失败: {e}")
            return {"error": str(e)}
    
    def _get_top_suggestions(self, nodes: List[Dict[str, Any]], limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取最重要的优化建议
        
        Args:
            nodes: 节点列表
            limit: 返回数量限制
            
        Returns:
            List[Dict[str, Any]]: 优化建议列表
        """
        suggestions = []
        
        for node in nodes:
            if node.get('suggested_actions'):
                for action in node['suggested_actions']:
                    suggestions.append({
                        "path": node.get('path', ''),
                        "suggestion": action,
                        "score": node.get('optimization_score', 0),
                        "impact": node.get('performance_impact', 'low')
                    })
        
        # 按评分和影响排序
        impact_weight = {'high': 3, 'medium': 2, 'low': 1}
        suggestions.sort(
            key=lambda x: (impact_weight.get(x['impact'], 1), x['score']),
            reverse=True
        )
        
        return suggestions[:limit]
    
    async def apply_optimization(self, node_id: str, action: str) -> bool:
        """
        应用优化操作
        
        Args:
            node_id: 节点ID
            action: 优化操作
            
        Returns:
            bool: 是否成功
        """
        try:
            # 获取节点信息
            node_data = await self.db_manager.get_tree_node_by_id(node_id)
            if not node_data:
                logger.error(f"未找到节点: {node_id}")
                return False
            
            # 这里可以实现具体的优化操作
            # 例如：重命名、移动、创建子目录等
            logger.info(f"应用优化操作: {action} 到节点 {node_id}")
            
            # 更新统计信息
            self.stats["optimizations_applied"] += 1
            
            return True
            
        except Exception as e:
            logger.error(f"应用优化操作失败: {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        return self.stats.copy()
    
    async def cleanup_cache(self):
        """
        清理缓存
        """
        self._node_cache.clear()
        self._folder_cache.clear()
        logger.info("缓存已清理")