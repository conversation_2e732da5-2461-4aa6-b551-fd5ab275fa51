#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
重复文件服务接口定义

定义重复文件检测和处理相关的业务服务接口
遵循RULE-001: 模块职责单一原则
遵循RULE-003: 事件驱动通信规范
"""

from abc import ABC, abstractmethod
from typing import AsyncGenerator, Optional, List, Dict, Any
from enum import Enum

from src.data.dto.duplicate_dto import (
    DuplicateCheckRequest, DuplicateCheckResult, DuplicateGroup, 
    DuplicateActionRequest, DuplicateCheckType, DuplicateAction
)
from src.data.dto.scan_dto import FileInfo
from src.data.dto.base_dto import ProgressUpdate, ErrorInfo
from .base_service import IBaseService, ITaskService, IValidationService


# 常量定义
DEFAULT_SIMILARITY_THRESHOLD = 0.8  # 默认相似度阈值


class DuplicateResolutionStrategy(Enum):
    """重复文件解决策略枚举"""
    KEEP_NEWEST = "keep_newest"
    KEEP_OLDEST = "keep_oldest"
    KEEP_LARGEST = "keep_largest"
    KEEP_SMALLEST = "keep_smallest"
    KEEP_FIRST_FOUND = "keep_first_found"
    MANUAL_SELECTION = "manual_selection"


class IDuplicateDetectionService(IBaseService, ITaskService, IValidationService):
    """
    重复文件检测服务接口
    
    负责重复文件的检测和分析
    """
    
    @abstractmethod
    async def detect_duplicates(self, request: DuplicateCheckRequest) -> AsyncGenerator[ProgressUpdate, None]:
        """
        异步检测重复文件
        
        Args:
            request: 重复检查请求DTO
            
        Yields:
            ProgressUpdate: 检测进度更新
            
        Raises:
            ValueError: 当请求数据无效时
            PermissionError: 当没有访问权限时
        """
        pass
    
    @abstractmethod
    async def get_detection_result(self, task_id: str) -> Optional[DuplicateCheckResult]:
        """
        获取重复文件检测结果
        
        Args:
            task_id: 任务ID
            
        Returns:
            DuplicateCheckResult: 检测结果DTO，如果任务不存在返回None
        """
        pass
    
    @abstractmethod
    async def get_duplicate_groups(self, task_id: str) -> List[DuplicateGroup]:
        """
        获取重复文件组
        
        Args:
            task_id: 任务ID
            
        Returns:
            List[DuplicateGroup]: 重复文件组列表
        """
        pass
    
    @abstractmethod
    async def get_duplicate_group(self, task_id: str, group_id: str) -> Optional[DuplicateGroup]:
        """
        获取指定的重复文件组
        
        Args:
            task_id: 任务ID
            group_id: 组ID
            
        Returns:
            DuplicateGroup: 重复文件组，如果不存在返回None
        """
        pass
    
    @abstractmethod
    async def filter_duplicate_groups(self, task_id: str, 
                                    min_file_count: int = 2,
                                    min_total_size: int = 0,
                                    file_extensions: Optional[List[str]] = None) -> List[DuplicateGroup]:
        """
        过滤重复文件组
        
        Args:
            task_id: 任务ID
            min_file_count: 最小文件数量
            min_total_size: 最小总大小
            file_extensions: 文件扩展名过滤
            
        Returns:
            List[DuplicateGroup]: 过滤后的重复文件组列表
        """
        pass
    
    @abstractmethod
    async def calculate_space_savings(self, task_id: str, strategy: DuplicateResolutionStrategy) -> int:
        """
        计算空间节省量
        
        Args:
            task_id: 任务ID
            strategy: 解决策略
            
        Returns:
            int: 可节省的字节数
        """
        pass
    
    @abstractmethod
    async def validate_detection_request(self, request: DuplicateCheckRequest) -> List[str]:
        """
        验证重复检测请求
        
        Args:
            request: 检测请求DTO
            
        Returns:
            List[str]: 验证错误列表，空列表表示验证通过
        """
        pass
    
    @abstractmethod
    async def get_detection_statistics(self, task_id: str) -> Dict[str, Any]:
        """
        获取检测统计信息
        
        Args:
            task_id: 任务ID
            
        Returns:
            Dict[str, Any]: 统计信息
        """
        pass


class IDuplicateActionService(IBaseService, ITaskService):
    """
    重复文件处理服务接口
    
    负责重复文件的处理和操作
    """
    
    @abstractmethod
    async def execute_action(self, request: DuplicateActionRequest) -> AsyncGenerator[ProgressUpdate, None]:
        """
        执行重复文件处理动作
        
        Args:
            request: 处理请求DTO
            
        Yields:
            ProgressUpdate: 处理进度更新
            
        Raises:
            ValueError: 当请求数据无效时
            PermissionError: 当没有操作权限时
            FileNotFoundError: 当文件不存在时
        """
        pass
    
    @abstractmethod
    async def preview_action(self, request: DuplicateActionRequest) -> Dict[str, Any]:
        """
        预览处理动作
        
        Args:
            request: 处理请求DTO
            
        Returns:
            Dict[str, Any]: 预览信息，包含将要执行的操作
        """
        pass
    
    @abstractmethod
    async def validate_action_request(self, request: DuplicateActionRequest) -> List[str]:
        """
        验证处理请求
        
        Args:
            request: 处理请求DTO
            
        Returns:
            List[str]: 验证错误列表，空列表表示验证通过
        """
        pass
    
    @abstractmethod
    async def can_execute_action(self, request: DuplicateActionRequest) -> bool:
        """
        检查是否可以执行动作
        
        Args:
            request: 处理请求DTO
            
        Returns:
            bool: 是否可以执行
        """
        pass
    
    @abstractmethod
    async def estimate_action_time(self, request: DuplicateActionRequest) -> float:
        """
        估算处理时间
        
        Args:
            request: 处理请求DTO
            
        Returns:
            float: 预估时间（秒）
        """
        pass
    
    @abstractmethod
    async def create_backup(self, file_paths: List[str], backup_location: str) -> bool:
        """
        创建文件备份
        
        Args:
            file_paths: 文件路径列表
            backup_location: 备份位置
            
        Returns:
            bool: 是否备份成功
        """
        pass
    
    @abstractmethod
    async def restore_from_backup(self, backup_id: str) -> bool:
        """
        从备份恢复文件
        
        Args:
            backup_id: 备份ID
            
        Returns:
            bool: 是否恢复成功
        """
        pass


class IDuplicateAnalysisService(IBaseService):
    """
    重复文件分析服务接口
    
    负责重复文件的深度分析和报告
    """
    
    @abstractmethod
    async def analyze_duplicate_patterns(self, duplicate_groups: List[DuplicateGroup]) -> Dict[str, Any]:
        """
        分析重复文件模式
        
        Args:
            duplicate_groups: 重复文件组列表
            
        Returns:
            Dict[str, Any]: 分析结果
        """
        pass
    
    @abstractmethod
    async def generate_duplicate_report(self, task_id: str, format: str = "json") -> str:
        """
        生成重复文件报告
        
        Args:
            task_id: 任务ID
            format: 报告格式 (json, html, csv)
            
        Returns:
            str: 报告内容或文件路径
        """
        pass
    
    @abstractmethod
    async def get_file_similarity(self, file1_path: str, file2_path: str) -> float:
        """
        计算文件相似度
        
        Args:
            file1_path: 文件1路径
            file2_path: 文件2路径
            
        Returns:
            float: 相似度 (0.0-1.0)
        """
        pass
    
    @abstractmethod
    async def find_similar_files(self, file_path: str, threshold: float = DEFAULT_SIMILARITY_THRESHOLD) -> List[FileInfo]:
        """
        查找相似文件
        
        Args:
            file_path: 目标文件路径
            threshold: 相似度阈值
            
        Returns:
            List[FileInfo]: 相似文件列表
        """
        pass
    
    @abstractmethod
    async def get_duplicate_trends(self, time_range: str = "30d") -> Dict[str, Any]:
        """
        获取重复文件趋势
        
        Args:
            time_range: 时间范围 (7d, 30d, 90d, 1y)
            
        Returns:
            Dict[str, Any]: 趋势数据
        """
        pass
