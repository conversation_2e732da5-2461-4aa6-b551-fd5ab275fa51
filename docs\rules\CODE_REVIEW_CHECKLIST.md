# 代码审查检查清单

## 📋 代码审查流程

### 审查前准备
- [ ] PR标题清晰描述变更内容
- [ ] PR描述包含变更原因和影响范围
- [ ] 所有自动化检查通过(CI/CD)
- [ ] 代码已经过自测
- [ ] 相关文档已更新

### 审查角色分工
- **提交者**: 确保代码质量，响应审查意见
- **审查者**: 仔细检查代码，提供建设性意见
- **架构师**: 关注架构合规性和设计决策
- **测试工程师**: 关注测试覆盖率和质量

## 🏗️ 架构合规性检查

### ARCH-CHECK-001: 分层架构检查
- [ ] **UI层职责检查**
  - [ ] UI组件只负责界面展示和用户交互
  - [ ] 无直接数据库操作代码
  - [ ] 无业务逻辑处理代码
  - [ ] 通过事件总线与其他层通信

- [ ] **业务服务层职责检查**
  - [ ] 只包含业务逻辑处理代码
  - [ ] 无UI操作代码
  - [ ] 无直接数据库操作(通过Repository)
  - [ ] 正确使用DTO进行数据传递

- [ ] **数据访问层职责检查**
  - [ ] 只包含数据存储和检索逻辑
  - [ ] 无业务逻辑判定代码
  - [ ] 无UI相关代码
  - [ ] 正确实现Repository模式

### ARCH-CHECK-002: 模块依赖检查
- [ ] **依赖方向检查**
  - [ ] 上层模块依赖下层模块
  - [ ] 无下层模块依赖上层模块
  - [ ] 无跨层直接调用

- [ ] **循环依赖检查**
  - [ ] 无模块间循环依赖
  - [ ] 无类间循环依赖
  - [ ] 依赖图为有向无环图(DAG)

- [ ] **接口依赖检查**
  - [ ] 依赖接口而非具体实现
  - [ ] 接口定义清晰稳定
  - [ ] 正确使用依赖注入

### ARCH-CHECK-003: 事件驱动架构检查
- [ ] **事件设计检查**
  - [ ] 事件命名符合规范(动词_名词_状态)
  - [ ] 事件数据结构合理
  - [ ] 事件处理具有幂等性

- [ ] **事件流检查**
  - [ ] 模块间通过事件总线通信
  - [ ] 无直接方法调用
  - [ ] 事件处理异步执行

## 💻 编程规范检查

### CODE-CHECK-001: DTO使用检查
- [ ] **DTO定义检查**
  - [ ] 使用@dataclass装饰器
  - [ ] 所有字段有类型注解
  - [ ] 设置frozen=True保证不可变性
  - [ ] 提供validate()方法

- [ ] **DTO使用检查**
  - [ ] 模块间数据传递使用DTO
  - [ ] 无传递原始字典或多参数
  - [ ] 无在传递过程中修改DTO

```python
# ✅ 正确的DTO定义示例
@dataclass(frozen=True)
class ScanRequest:
    task_id: str
    directories: List[str]
    recursive: bool = True
    
    def validate(self) -> List[str]:
        errors = []
        if not self.task_id:
            errors.append("task_id不能为空")
        return errors
```

### CODE-CHECK-002: 错误处理检查
- [ ] **异常处理检查**
  - [ ] 所有异常都被正确捕获
  - [ ] 无空的except块
  - [ ] 异常信息结构化且用户友好
  - [ ] 关键操作有重试机制

- [ ] **日志记录检查**
  - [ ] 错误被正确记录到日志
  - [ ] 日志级别使用恰当
  - [ ] 包含足够的上下文信息
  - [ ] 无敏感信息泄露

```python
# ✅ 正确的错误处理示例
try:
    result = self.risky_operation()
except SpecificException as e:
    error_info = ErrorInfo(
        code="FS_001",
        message="操作失败",
        details=str(e),
        context={"operation": "scan", "path": file_path}
    )
    self.logger.error(f"操作失败: {error_info}")
    return ErrorResult(error_info)
```

### CODE-CHECK-003: 异步任务检查
- [ ] **任务定义检查**
  - [ ] 长时间操作实现为异步任务
  - [ ] 支持暂停、恢复、取消操作
  - [ ] 提供进度报告机制
  - [ ] 实现超时控制

- [ ] **任务状态管理检查**
  - [ ] 状态变更原子性
  - [ ] 状态持久化到数据库
  - [ ] 正确的状态转换逻辑

## ⚡ 性能合规性检查

### PERF-CHECK-001: 大文件处理检查
- [ ] **文件处理策略**
  - [ ] 大文件使用分块读取
  - [ ] 块大小合理(8MB-32MB)
  - [ ] 实现断点续传
  - [ ] 提供进度报告和取消功能

- [ ] **内存使用检查**
  - [ ] 无一次性加载大文件到内存
  - [ ] 及时释放文件句柄
  - [ ] 实现内存使用监控

### PERF-CHECK-002: 数据库操作检查
- [ ] **查询优化检查**
  - [ ] 所有查询使用索引
  - [ ] 无全表扫描查询
  - [ ] 使用批量操作替代循环查询
  - [ ] 查询结果集大小合理(<10000条)

- [ ] **事务管理检查**
  - [ ] 事务范围最小化
  - [ ] 避免长时间事务
  - [ ] 正确的事务隔离级别
  - [ ] 事务超时控制

### PERF-CHECK-003: 并发处理检查
- [ ] **线程管理检查**
  - [ ] 使用线程池管理并发
  - [ ] 并发数量在限制范围内
  - [ ] 正确的线程同步机制
  - [ ] 避免死锁和竞态条件

- [ ] **资源管理检查**
  - [ ] 正确释放资源(文件、连接等)
  - [ ] 使用上下文管理器
  - [ ] 实现资源池化
  - [ ] 监控资源使用情况

## 🧪 测试质量检查

### TEST-CHECK-001: 测试覆盖率检查
- [ ] **单元测试检查**
  - [ ] 新增代码有对应单元测试
  - [ ] 测试覆盖率 ≥ 80%
  - [ ] 测试用例覆盖主要分支
  - [ ] 测试用例独立且可重复

- [ ] **集成测试检查**
  - [ ] 关键业务流程有集成测试
  - [ ] 模块间接口有测试覆盖
  - [ ] 数据库操作有测试覆盖

### TEST-CHECK-002: 测试质量检查
- [ ] **测试设计检查**
  - [ ] 测试用例设计合理
  - [ ] 包含正常和异常场景
  - [ ] 边界条件测试充分
  - [ ] 性能测试(如适用)

- [ ] **测试维护性检查**
  - [ ] 测试代码清晰易懂
  - [ ] 测试数据管理合理
  - [ ] 测试环境隔离
  - [ ] 测试执行稳定

## 📚 文档质量检查

### DOC-CHECK-001: 代码文档检查
- [ ] **注释质量检查**
  - [ ] 复杂逻辑有清晰注释
  - [ ] 公共API有详细文档字符串
  - [ ] 注释与代码保持同步
  - [ ] 无过时或误导性注释

- [ ] **API文档检查**
  - [ ] 新增API有完整文档
  - [ ] 参数和返回值说明清晰
  - [ ] 包含使用示例
  - [ ] 异常情况说明

### DOC-CHECK-002: 设计文档检查
- [ ] **架构文档检查**
  - [ ] 重大架构变更有设计文档
  - [ ] 设计决策有记录和说明
  - [ ] 架构图与实现一致

- [ ] **用户文档检查**
  - [ ] 用户可见功能有使用说明
  - [ ] 配置变更有文档更新
  - [ ] 故障排除指南完整

## 🔒 安全性检查

### SEC-CHECK-001: 数据安全检查
- [ ] **输入验证检查**
  - [ ] 所有外部输入都经过验证
  - [ ] 防止SQL注入攻击
  - [ ] 防止路径遍历攻击
  - [ ] 文件类型和大小限制

- [ ] **敏感信息检查**
  - [ ] 无硬编码密码或密钥
  - [ ] 敏感信息正确加密存储
  - [ ] 日志中无敏感信息
  - [ ] 错误信息不泄露内部细节

## ✅ 审查结论

### 审查结果分类
- **🟢 通过**: 代码质量良好，可以合并
- **🟡 有条件通过**: 有轻微问题，修复后可合并
- **🔴 不通过**: 有严重问题，必须修复后重新审查

### 审查意见模板
```markdown
## 审查意见

### 总体评价
- 代码质量: [优秀/良好/一般/需改进]
- 架构合规性: [完全符合/基本符合/部分符合/不符合]
- 性能影响: [正面/无影响/轻微负面/严重负面]

### 主要问题
1. [问题描述] - [严重程度: 高/中/低]
2. [问题描述] - [严重程度: 高/中/低]

### 改进建议
1. [建议内容]
2. [建议内容]

### 审查结论
[通过/有条件通过/不通过]
```

## 📊 审查质量度量

### 审查效率指标
- **审查时间**: 平均每100行代码审查时间
- **问题发现率**: 审查中发现的问题数量
- **问题修复率**: 审查问题的修复比例
- **重复审查率**: 需要多次审查的PR比例

### 代码质量趋势
- **缺陷密度**: 每千行代码的缺陷数量
- **技术债务**: 技术债务的累积和清理情况
- **测试覆盖率**: 测试覆盖率的变化趋势
- **性能指标**: 关键性能指标的变化

## 📚 相关文档
- [编程规则](CODING_RULES.md)
- [架构规则](ARCHITECTURE_RULES.md)
- [性能规则](PERFORMANCE_RULES.md)
- [重构指南](../guidelines/REFACTORING_GUIDE.md)
