name: Code Quality Check

on:
  push:
    branches: [ main, master, develop ]
  pull_request:
    branches: [ main, master, develop ]
  workflow_dispatch:

jobs:
  code-quality:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.8, 3.9, "3.10"]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
      with:
        fetch-depth: 0
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements*.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pylint mypy black isort flake8 bandit safety
        pip install pre-commit
        if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
        if [ -f requirements-dev.txt ]; then pip install -r requirements-dev.txt; fi
    
    - name: <PERSON> Black (Code Formatting Check)
      run: |
        echo "🎨 检查代码格式..."
        black --check --diff --color .
    
    - name: Run isort (Import Sorting Check)
      run: |
        echo "📦 检查导入排序..."
        isort --check-only --diff --color .
    
    - name: Run flake8 (Style Check)
      run: |
        echo "📏 检查代码风格..."
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=100 --statistics
    
    - name: Run MyPy (Type Check)
      run: |
        echo "🔍 检查类型注解..."
        mypy src/ --config-file=config/mypy.ini || true
    
    - name: Run Pylint (Code Quality Check)
      run: |
        echo "🔧 检查代码质量..."
        pylint src/ --rcfile=config/pylint.rc --output-format=colorized || true
    
    - name: Run Bandit (Security Check)
      run: |
        echo "🔒 检查安全问题..."
        bandit -r src/ -f json -o bandit-report.json || true
        if [ -f bandit-report.json ]; then
          echo "安全检查报告已生成"
          cat bandit-report.json
        fi
    
    - name: Run Safety (Dependency Security Check)
      run: |
        echo "🛡️ 检查依赖安全..."
        safety check --json || true
    
    - name: Run Custom Rule Checker
      run: |
        echo "📋 运行自定义规则检查..."
        python tools/code_checker.py --check-rules --generate-report --output-file=rule-check-report.txt
      continue-on-error: true
    
    - name: Upload Code Quality Reports
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: code-quality-reports-${{ matrix.python-version }}
        path: |
          bandit-report.json
          rule-check-report.txt
          .mypy_cache/
        retention-days: 30
    
    - name: Comment PR with Quality Report
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          
          let comment = '## 🔍 代码质量检查报告\n\n';
          
          // 读取自定义规则检查报告
          try {
            const ruleReport = fs.readFileSync('rule-check-report.txt', 'utf8');
            comment += '### 📋 规则检查结果\n```\n' + ruleReport + '\n```\n\n';
          } catch (e) {
            comment += '### 📋 规则检查结果\n❌ 无法读取规则检查报告\n\n';
          }
          
          // 读取安全检查报告
          try {
            const banditReport = JSON.parse(fs.readFileSync('bandit-report.json', 'utf8'));
            const highIssues = banditReport.results.filter(r => r.issue_severity === 'HIGH').length;
            const mediumIssues = banditReport.results.filter(r => r.issue_severity === 'MEDIUM').length;
            const lowIssues = banditReport.results.filter(r => r.issue_severity === 'LOW').length;
            
            comment += '### 🔒 安全检查结果\n';
            comment += `- 高风险问题: ${highIssues}\n`;
            comment += `- 中风险问题: ${mediumIssues}\n`;
            comment += `- 低风险问题: ${lowIssues}\n\n`;
          } catch (e) {
            comment += '### 🔒 安全检查结果\n✅ 未发现安全问题\n\n';
          }
          
          comment += '### 📊 检查状态\n';
          comment += '- ✅ 代码格式检查通过\n';
          comment += '- ✅ 导入排序检查通过\n';
          comment += '- ✅ 代码风格检查通过\n';
          comment += '- ⚠️ 类型检查和代码质量检查请查看详细日志\n';
          
          // 发布评论
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: comment
          });

  test-coverage:
    runs-on: ubuntu-latest
    needs: code-quality
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: 3.8
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pytest pytest-cov coverage
        if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
        if [ -f requirements-dev.txt ]; then pip install -r requirements-dev.txt; fi
    
    - name: Run tests with coverage
      run: |
        echo "🧪 运行测试并生成覆盖率报告..."
        pytest tests/ --cov=src --cov-report=xml --cov-report=html --cov-report=term
      continue-on-error: true
    
    - name: Upload coverage reports
      uses: actions/upload-artifact@v3
      with:
        name: coverage-reports
        path: |
          coverage.xml
          htmlcov/
        retention-days: 30
    
    - name: Coverage comment
      if: github.event_name == 'pull_request'
      uses: py-cov-action/python-coverage-comment-action@v3
      with:
        GITHUB_TOKEN: ${{ github.token }}
        MINIMUM_GREEN: 80
        MINIMUM_ORANGE: 60

  performance-check:
    runs-on: ubuntu-latest
    needs: code-quality
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: 3.8
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pytest-benchmark memory-profiler
        if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
    
    - name: Run performance tests
      run: |
        echo "⚡ 运行性能测试..."
        # 这里可以添加性能测试命令
        echo "性能测试功能待实现"
      continue-on-error: true
