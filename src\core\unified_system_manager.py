#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
统一系统管理器

该模块提供整个应用的统一系统管理，包括：
1. 异步进程管理
2. 统一监控系统
3. 系统资源协调
4. 性能优化

作者: SmartFileManager开发团队
日期: 2025-01-31
版本: 2.0.0
"""

import asyncio
import threading
import time
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass
from enum import Enum

from src.utils.logger import get_logger
from src.core.dependency_injection import resolve
from src.utils.event_system import EventSystem

logger = get_logger(__name__)


class SystemState(Enum):
    """系统状态枚举"""
    INITIALIZING = "initializing"
    RUNNING = "running"
    DEGRADED = "degraded"
    SHUTTING_DOWN = "shutting_down"
    STOPPED = "stopped"


@dataclass
class SystemMetrics:
    """系统指标数据类"""
    cpu_usage: float = 0.0
    memory_usage: float = 0.0
    disk_io_read: float = 0.0
    disk_io_write: float = 0.0
    active_tasks: int = 0
    pending_tasks: int = 0
    database_connected: bool = False
    database_health: float = 0.0
    timestamp: float = 0.0


@dataclass
class SystemConfiguration:
    """系统配置数据类"""
    monitoring_interval: float = 1.0
    max_concurrent_tasks: int = 100
    resource_threshold_cpu: float = 80.0
    resource_threshold_memory: float = 85.0
    adaptive_scaling_enabled: bool = True
    performance_optimization_enabled: bool = True
    debug_mode: bool = False


class UnifiedSystemManager:
    """统一系统管理器"""
    
    def __init__(self, config: Optional[SystemConfiguration] = None):
        """
        初始化统一系统管理器
        
        参数:
            config: 系统配置，如果为None则使用默认配置
        """
        self.logger = get_logger(self.__class__.__name__)
        self.config = config or SystemConfiguration()
        
        # 系统状态
        self.state = SystemState.INITIALIZING
        self.start_time = None
        self.shutdown_requested = False
        
        # 核心组件（延迟初始化）
        self._monitoring_manager = None
        self._async_manager = None
        self._resource_coordinator = None
        
        # 系统指标
        self.current_metrics = SystemMetrics()
        self.metrics_history: List[SystemMetrics] = []
        
        # 事件系统
        self.event_system = None
        
        # 线程安全锁
        self._lock = threading.RLock()
        
        # 回调注册
        self._state_change_callbacks: List[Callable[[SystemState], None]] = []
        self._metrics_callbacks: List[Callable[[SystemMetrics], None]] = []
        
        self.logger.info("统一系统管理器初始化完成")
    
    async def initialize(self) -> bool:
        """
        异步初始化系统
        
        返回:
            bool: 初始化是否成功
        """
        try:
            self.logger.info("开始初始化统一系统管理器...")
            self.start_time = time.time()
            
            # 1. 初始化事件系统
            await self._initialize_event_system()
            
            # 2. 初始化核心组件
            await self._initialize_core_components()
            
            # 3. 启动监控系统
            await self._start_monitoring()
            
            # 4. 启动异步管理器
            await self._start_async_manager()
            
            # 5. 启动资源协调器
            await self._start_resource_coordinator()
            
            # 6. 注册系统事件
            await self._register_system_events()
            
            # 7. 执行系统健康检查
            health_check_passed = await self._perform_health_check()
            
            if health_check_passed:
                self._set_state(SystemState.RUNNING)
                self.logger.info("统一系统管理器初始化成功")
                return True
            else:
                self._set_state(SystemState.DEGRADED)
                self.logger.warning("统一系统管理器初始化完成，但健康检查未通过")
                return False
                
        except Exception as e:
            self.logger.error(f"统一系统管理器初始化失败: {e}", exc_info=True)
            self._set_state(SystemState.STOPPED)
            return False
    
    async def shutdown(self, timeout: float = 30.0) -> bool:
        """
        优雅关闭系统
        
        参数:
            timeout: 关闭超时时间（秒）
            
        返回:
            bool: 关闭是否成功
        """
        if self.shutdown_requested:
            return True
            
        self.logger.info("开始关闭统一系统管理器...")
        self.shutdown_requested = True
        self._set_state(SystemState.SHUTTING_DOWN)
        
        try:
            # 创建关闭任务列表
            shutdown_tasks = []
            
            # 1. 停止资源协调器
            if self._resource_coordinator:
                shutdown_tasks.append(self._resource_coordinator.shutdown())
            
            # 2. 停止异步管理器
            if self._async_manager:
                shutdown_tasks.append(self._async_manager.shutdown())
            
            # 3. 停止监控管理器
            if self._monitoring_manager:
                shutdown_tasks.append(self._monitoring_manager.shutdown())
            
            # 并行执行关闭任务
            if shutdown_tasks:
                await asyncio.wait_for(
                    asyncio.gather(*shutdown_tasks, return_exceptions=True),
                    timeout=timeout
                )
            
            self._set_state(SystemState.STOPPED)
            
            # 计算运行时间
            if self.start_time:
                uptime = time.time() - self.start_time
                self.logger.info(f"统一系统管理器已关闭，运行时间: {uptime:.1f}秒")
            
            return True
            
        except asyncio.TimeoutError:
            self.logger.error(f"系统关闭超时（{timeout}秒）")
            return False
        except Exception as e:
            self.logger.error(f"系统关闭失败: {e}", exc_info=True)
            return False
    
    def get_system_status(self) -> Dict[str, Any]:
        """
        获取系统状态报告
        
        返回:
            Dict[str, Any]: 系统状态信息
        """
        with self._lock:
            uptime = time.time() - self.start_time if self.start_time else 0
            
            return {
                'state': self.state.value,
                'uptime': uptime,
                'current_metrics': {
                    'cpu_usage': self.current_metrics.cpu_usage,
                    'memory_usage': self.current_metrics.memory_usage,
                    'active_tasks': self.current_metrics.active_tasks,
                    'pending_tasks': self.current_metrics.pending_tasks,
                    'database_connected': self.current_metrics.database_connected,
                    'database_health': self.current_metrics.database_health
                },
                'configuration': {
                    'monitoring_interval': self.config.monitoring_interval,
                    'max_concurrent_tasks': self.config.max_concurrent_tasks,
                    'adaptive_scaling_enabled': self.config.adaptive_scaling_enabled,
                    'performance_optimization_enabled': self.config.performance_optimization_enabled
                },
                'components': {
                    'monitoring_manager': self._monitoring_manager is not None,
                    'async_manager': self._async_manager is not None,
                    'resource_coordinator': self._resource_coordinator is not None
                }
            }
    
    def register_state_callback(self, callback: Callable[[SystemState], None]):
        """注册状态变化回调"""
        self._state_change_callbacks.append(callback)
    
    def register_metrics_callback(self, callback: Callable[[SystemMetrics], None]):
        """注册指标更新回调"""
        self._metrics_callbacks.append(callback)
    
    async def _initialize_event_system(self):
        """初始化事件系统"""
        try:
            self.event_system = resolve(EventSystem)
            if not self.event_system:
                from src.utils.event_system import EventSystem
                self.event_system = EventSystem()
            
            self.logger.debug("事件系统初始化完成")
        except Exception as e:
            self.logger.error(f"事件系统初始化失败: {e}")
            raise
    
    async def _initialize_core_components(self):
        """初始化核心组件"""
        try:
            # 导入核心组件（延迟导入避免循环依赖）
            from .unified_monitoring_manager import UnifiedMonitoringManager
            from .enhanced_async_manager import EnhancedAsyncManager
            from .system_resource_coordinator import SystemResourceCoordinator
            
            # 初始化监控管理器
            self._monitoring_manager = UnifiedMonitoringManager(
                interval=self.config.monitoring_interval
            )
            
            # 初始化异步管理器
            self._async_manager = EnhancedAsyncManager(
                max_concurrent_tasks=self.config.max_concurrent_tasks,
                performance_optimization=self.config.performance_optimization_enabled
            )
            
            # 初始化资源协调器
            self._resource_coordinator = SystemResourceCoordinator(
                cpu_threshold=self.config.resource_threshold_cpu,
                memory_threshold=self.config.resource_threshold_memory,
                adaptive_scaling=self.config.adaptive_scaling_enabled
            )
            
            self.logger.debug("核心组件初始化完成")
            
        except Exception as e:
            self.logger.error(f"核心组件初始化失败: {e}")
            raise
    
    async def _start_monitoring(self):
        """启动监控系统"""
        if self._monitoring_manager:
            await self._monitoring_manager.start()
            # 注册监控回调
            self._monitoring_manager.register_metrics_callback(self._on_metrics_update)
    
    async def _start_async_manager(self):
        """启动异步管理器"""
        if self._async_manager:
            await self._async_manager.start()
    
    async def _start_resource_coordinator(self):
        """启动资源协调器"""
        if self._resource_coordinator:
            await self._resource_coordinator.start()
    
    async def _register_system_events(self):
        """注册系统事件"""
        if self.event_system:
            # 注册系统级事件监听
            self.event_system.subscribe("system_overload", self._on_system_overload)
            self.event_system.subscribe("system_recovery", self._on_system_recovery)
            self.event_system.subscribe("task_failed", self._on_task_failed)
    
    async def _perform_health_check(self) -> bool:
        """执行系统健康检查"""
        try:
            health_checks = []
            
            # 检查监控系统
            if self._monitoring_manager:
                health_checks.append(self._monitoring_manager.health_check())
            
            # 检查异步管理器
            if self._async_manager:
                health_checks.append(self._async_manager.health_check())
            
            # 检查资源协调器
            if self._resource_coordinator:
                health_checks.append(self._resource_coordinator.health_check())
            
            # 并行执行健康检查
            if health_checks:
                results = await asyncio.gather(*health_checks, return_exceptions=True)
                return all(result is True for result in results if not isinstance(result, Exception))
            
            return True
            
        except Exception as e:
            self.logger.error(f"健康检查失败: {e}")
            return False
    
    def _set_state(self, new_state: SystemState):
        """设置系统状态"""
        with self._lock:
            if self.state != new_state:
                old_state = self.state
                self.state = new_state
                
                self.logger.info(f"系统状态变化: {old_state.value} -> {new_state.value}")
                
                # 通知状态变化回调
                for callback in self._state_change_callbacks:
                    try:
                        callback(new_state)
                    except Exception as e:
                        self.logger.error(f"状态变化回调执行失败: {e}")
    
    def _on_metrics_update(self, metrics: SystemMetrics):
        """监控指标更新回调"""
        with self._lock:
            self.current_metrics = metrics
            
            # 保存历史记录（最多保留1000条）
            self.metrics_history.append(metrics)
            if len(self.metrics_history) > 1000:
                self.metrics_history.pop(0)
            
            # 通知指标更新回调
            for callback in self._metrics_callbacks:
                try:
                    callback(metrics)
                except Exception as e:
                    self.logger.error(f"指标更新回调执行失败: {e}")
    
    def _on_system_overload(self, event_data: Dict[str, Any]):
        """系统过载事件处理"""
        self.logger.warning(f"系统过载检测: {event_data}")
        if self.state == SystemState.RUNNING:
            self._set_state(SystemState.DEGRADED)
    
    def _on_system_recovery(self, event_data: Dict[str, Any]):
        """系统恢复事件处理"""
        self.logger.info(f"系统恢复检测: {event_data}")
        if self.state == SystemState.DEGRADED:
            self._set_state(SystemState.RUNNING)
    
    def _on_task_failed(self, event_data: Dict[str, Any]):
        """任务失败事件处理"""
        self.logger.warning(f"任务失败: {event_data}")


# 全局统一系统管理器实例
_unified_system_manager = None
_manager_lock = threading.Lock()


def get_unified_system_manager() -> UnifiedSystemManager:
    """获取统一系统管理器实例（单例模式）"""
    global _unified_system_manager
    with _manager_lock:
        if _unified_system_manager is None:
            _unified_system_manager = UnifiedSystemManager()
        return _unified_system_manager


async def initialize_unified_system(config: Optional[SystemConfiguration] = None) -> bool:
    """初始化统一系统"""
    manager = get_unified_system_manager()
    if config:
        manager.config = config
    return await manager.initialize()


async def shutdown_unified_system(timeout: float = 30.0) -> bool:
    """关闭统一系统"""
    manager = get_unified_system_manager()
    return await manager.shutdown(timeout)
