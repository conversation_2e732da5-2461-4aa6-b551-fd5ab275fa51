# 测试文件归档目录

## 📁 目录说明

这个文件夹包含了智能文件管理器项目的所有测试文件、调试脚本、性能测试和相关文档。为了保持项目根目录的整洁，所有测试相关的文件都被移动到这里。

## 📋 文件分类

### 🧪 测试文件
- `test_*.py` - 各种功能测试脚本
- `*test*.py` - 测试相关的Python文件
- `basic_test.py` - 基础功能测试
- `final_test.py` - 最终测试脚本

### 🔧 调试和修复脚本
- `*debug*.py` - 调试脚本
- `*fix*.py` - 修复脚本
- `*verify*.py` - 验证脚本
- `*check*.py` - 检查脚本

### 📊 性能测试
- `*performance*.py` - 性能测试脚本
- `*memory*.py` - 内存测试脚本
- `test_performance_data/` - 性能测试数据目录

### 🔄 异步和迁移
- `*async*.py` - 异步功能测试
- `*migrate*.py` - 数据迁移脚本
- `*prepare*.py` - 准备脚本
- `*setup*.py` - 设置脚本

### 📚 文档和报告
- `*报告.md` - 各种中文报告
- `*总结.md` - 总结文档
- `*方案.md` - 方案文档
- `*修复*.md` - 修复相关文档
- `*检查*.md` - 检查报告
- `*功能*.md` - 功能文档
- `*实现*.md` - 实现文档
- `*优化*.md` - 优化文档
- `*分析*.md` - 分析报告
- `*流程*.md` - 流程文档
- `*REPORT*.md` - 英文报告
- `*GUIDE*.md` - 指南文档
- `*SUMMARY*.md` - 摘要文档
- `*Plan*.md` - 计划文档

### 📄 配置和数据文件
- `*.json` - JSON配置文件
- `*.log` - 日志文件
- `*.html` - HTML报告文件
- `test_batch_files/` - 批量测试文件目录

## 🎯 主要测试套件

### 正式测试套件 (src/tests/comprehensive/)
项目的正式测试套件位于 `src/tests/comprehensive/` 目录中，包含：

1. **应用程序启动和数据库初始化测试**
2. **文件夹选择和递归文件扫描测试**
3. **文件树清除功能测试**
4. **文件操作测试**
5. **重复文件检测和管理测试**
6. **文件白名单和标签系统测试**

### 归档测试文件
本目录中的文件主要是开发过程中的临时测试、调试脚本和实验性代码，用于：

- 功能验证和调试
- 性能测试和优化
- 问题修复和验证
- 开发过程中的实验

## 🚀 使用建议

1. **日常开发**: 使用 `src/tests/comprehensive/` 中的正式测试套件
2. **问题调试**: 可以参考本目录中的调试脚本
3. **性能分析**: 查看性能测试相关文件
4. **历史记录**: 本目录保留了开发过程中的测试历史

## 🧹 清理说明

这些文件已从项目根目录移动到此处，以保持项目结构的整洁。如果某些文件不再需要，可以安全删除。建议定期清理过时的测试文件和临时脚本。

---

*最后更新: 2025-07-26*