#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能缓存管理器

提供多级缓存、LRU策略、持久化等功能
"""

import os
import json
import pickle
import hashlib
import threading
from typing import Any, Optional, Dict, List
from datetime import datetime, timedelta
from pathlib import Path

class IntelligentCacheManager:
    """智能缓存管理器"""
    
    def __init__(self, cache_dir: str = "cache", max_size: int = 1000):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.max_size = max_size
        self.memory_cache = {}
        self.access_times = {}
        self.lock = threading.RLock()
        
    def get(self, key: str, default: Any = None) -> Any:
        """获取缓存值"""
        with self.lock:
            # 先检查内存缓存
            if key in self.memory_cache:
                self.access_times[key] = datetime.now()
                return self.memory_cache[key]
                
            # 检查磁盘缓存
            cache_file = self.cache_dir / f"{self._hash_key(key)}.cache"
            if cache_file.exists():
                try:
                    with open(cache_file, 'rb') as f:
                        data = pickle.load(f)
                        
                    # 检查过期时间
                    if 'expires_at' in data and datetime.now() > data['expires_at']:
                        cache_file.unlink()
                        return default
                        
                    value = data['value']
                    # 加载到内存缓存
                    self.memory_cache[key] = value
                    self.access_times[key] = datetime.now()
                    
                    return value
                    
                except Exception:
                    cache_file.unlink()
                    
            return default
            
    def set(self, key: str, value: Any, ttl: Optional[int] = None):
        """设置缓存值"""
        with self.lock:
            # 设置内存缓存
            self.memory_cache[key] = value
            self.access_times[key] = datetime.now()
            
            # 检查内存缓存大小
            if len(self.memory_cache) > self.max_size:
                self._evict_lru()
                
            # 持久化到磁盘
            cache_file = self.cache_dir / f"{self._hash_key(key)}.cache"
            data = {'value': value}
            
            if ttl:
                data['expires_at'] = datetime.now() + timedelta(seconds=ttl)
                
            try:
                with open(cache_file, 'wb') as f:
                    pickle.dump(data, f)
            except Exception as e:
                logger.error(f"缓存持久化失败: {e}")
                
    def _hash_key(self, key: str) -> str:
        """生成键的哈希值"""
        return hashlib.md5(key.encode()).hexdigest()
        
    def _evict_lru(self):
        """LRU淘汰策略"""
        if not self.access_times:
            return
            
        # 找到最久未访问的键
        lru_key = min(self.access_times.keys(), 
                     key=lambda k: self.access_times[k])
                     
        # 从内存中移除
        del self.memory_cache[lru_key]
        del self.access_times[lru_key]
        
    def clear(self):
        """清空所有缓存"""
        with self.lock:
            self.memory_cache.clear()
            self.access_times.clear()
            
            # 清空磁盘缓存
            for cache_file in self.cache_dir.glob("*.cache"):
                try:
                    cache_file.unlink()
                except Exception:
                    pass
                    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self.lock:
            disk_files = len(list(self.cache_dir.glob("*.cache")))
            
            return {
                'memory_items': len(self.memory_cache),
                'disk_items': disk_files,
                'total_items': len(self.memory_cache) + disk_files,
                'max_size': self.max_size
            }

# 全局缓存管理器实例
_cache_manager = None

def get_cache_manager() -> IntelligentCacheManager:
    """获取全局缓存管理器实例"""
    global _cache_manager
    if _cache_manager is None:
        _cache_manager = IntelligentCacheManager()
    return _cache_manager
