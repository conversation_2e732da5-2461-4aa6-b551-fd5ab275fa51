#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库迁移工具

该脚本用于为现有数据库添加file_id字段，解决文件树索引越界问题。

使用方法:
    python migrate_database.py

作者: AI助手
日期: 2024-01-01
版本: 1.0.0
"""

import os
import sys
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.data.db_manager import MongoDBManager
from src.data.migration import DatabaseMigration, run_migration
from src.utils.logger import get_logger


def main():
    """主函数"""
    logger = get_logger("MigrationTool", task_type="system")
    
    print("=" * 60)
    print("智能文件管理器 - 数据库迁移工具")
    print("=" * 60)
    print("此工具将为现有数据库添加file_id字段，解决文件树索引越界问题")
    print()
    
    try:
        # 连接数据库
        print("正在连接数据库...")
        db_manager = MongoDBManager()
        
        if not db_manager.is_connected():
            print("❌ 数据库连接失败，请确保MongoDB服务正在运行")
            return 1
        
        print("✅ 数据库连接成功")
        print()
        
        # 创建迁移器
        migration = DatabaseMigration(db_manager)
        
        # 验证当前状态
        print("正在验证当前数据库状态...")
        verification = migration.verify_migration()
        
        print(f"📊 数据库统计:")
        print(f"   总记录数: {verification.get('total_records', 0)}")
        print(f"   有file_id的记录: {verification.get('with_file_id', 0)}")
        print(f"   没有file_id的记录: {verification.get('without_file_id', 0)}")
        print(f"   file_id覆盖率: {verification.get('file_id_coverage', 0):.1f}%")
        print()
        
        if verification.get("migration_complete", False):
            print("✅ 迁移已完成，无需重复执行")
            return 0
        
        # 询问用户是否继续
        print("⚠️  即将开始迁移，这将为所有没有file_id的记录添加唯一标识符")
        print("   此操作可能需要一些时间，请确保数据库连接稳定")
        print()
        
        response = input("是否继续迁移? (y/N): ").strip().lower()
        if response not in ['y', 'yes']:
            print("❌ 用户取消迁移")
            return 0
        
        print()
        print("🚀 开始迁移...")
        
        # 定义进度回调函数
        def progress_callback(progress, message):
            print(f"\r📈 进度: {progress:.1f}% - {message}", end="", flush=True)
        
        # 执行迁移
        start_time = time.time()
        migration_result = migration.migrate_add_file_id(progress_callback=progress_callback)
        end_time = time.time()
        
        print()  # 换行
        print()
        
        # 显示迁移结果
        if migration_result["success"]:
            print("✅ 迁移成功完成!")
            print(f"📊 迁移统计:")
            print(f"   成功处理: {migration_result['migrated_count']} 条记录")
            print(f"   失败记录: {migration_result['error_count']} 条")
            print(f"   总耗时: {end_time - start_time:.2f} 秒")
            print()
            
            # 再次验证
            print("正在验证迁移结果...")
            final_verification = migration.verify_migration()
            
            print(f"📊 最终验证结果:")
            print(f"   总记录数: {final_verification.get('total_records', 0)}")
            print(f"   有file_id的记录: {final_verification.get('with_file_id', 0)}")
            print(f"   没有file_id的记录: {final_verification.get('without_file_id', 0)}")
            print(f"   file_id覆盖率: {final_verification.get('file_id_coverage', 0):.1f}%")
            print(f"   重复file_id: {final_verification.get('duplicate_file_ids', 0)} 个")
            
            if final_verification.get("migration_complete", False):
                print("✅ 迁移验证通过，所有记录都已添加file_id")
            else:
                print("⚠️  迁移验证失败，部分记录可能没有file_id")
                return 1
        else:
            print("❌ 迁移失败!")
            print(f"错误信息: {migration_result['message']}")
            return 1
        
        print()
        print("🎉 数据库迁移完成，现在可以正常使用文件树功能了!")
        return 0
        
    except KeyboardInterrupt:
        print("\n❌ 用户中断迁移")
        return 1
    except Exception as e:
        print(f"❌ 迁移过程中发生错误: {e}")
        logger.error(f"迁移失败: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 