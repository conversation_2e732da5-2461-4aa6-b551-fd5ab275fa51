#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
增强的服务容器

基于新的服务接口体系，提供完整的依赖注入和服务管理功能
遵循RULE-001: 模块职责单一原则
集成新的DTO和事件系统
"""

import asyncio
import inspect
import threading
import time
from typing import Dict, Any, Type, TypeVar, Callable, Optional, Set, List
from enum import Enum
from dataclasses import dataclass
from contextlib import asynccontextmanager, contextmanager

from src.core.dependency_injection import DIContainer, ServiceLifetime
from src.services.interfaces import (
    IBaseService, ITaskService, IValidationService, IConfigurableService,
    BaseServiceImpl, ServiceStatus
)
from src.ui.events.event_bus import EventBus, IEventBus
from src.ui.events.event_definitions import (
    create_system_event, SystemEventType, EventPriority
)
from src.data.dto.base_dto import ErrorInfo

T = TypeVar('T')

# 常量定义
DEFAULT_HEALTH_CHECK_INTERVAL = 30.0  # 默认健康检查间隔（秒）


class ServiceContainerError(Exception):
    """服务容器异常"""
    pass


class ServiceHealthStatus(Enum):
    """服务健康状态枚举"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"


@dataclass
class ServiceRegistration:
    """服务注册信息"""
    service_type: Type
    implementation_type: Type
    lifetime: ServiceLifetime
    instance: Optional[Any] = None
    health_check: Optional[Callable[[], bool]] = None
    dependencies: List[Type] = None
    configuration: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []
        if self.configuration is None:
            self.configuration = {}


class EnhancedServiceContainer:
    """
    增强的服务容器
    
    基于原有DI容器，增加了服务健康检查、配置管理、事件集成等功能
    """
    
    def __init__(self, base_container: Optional[DIContainer] = None):
        self._base_container = base_container or DIContainer()
        self._registrations: Dict[Type, ServiceRegistration] = {}
        self._event_bus: Optional[IEventBus] = None
        self._sync_lock = threading.RLock()  # 同步操作锁
        self._async_lock: Optional[asyncio.Lock] = None  # 异步操作锁（延迟初始化）
        self._health_check_interval = DEFAULT_HEALTH_CHECK_INTERVAL
        self._health_check_task: Optional[asyncio.Task] = None
        self._is_running = False

    def _ensure_async_lock(self) -> asyncio.Lock:
        """确保异步锁已初始化"""
        if self._async_lock is None:
            self._async_lock = asyncio.Lock()
        return self._async_lock
        
    async def start(self) -> None:
        """启动服务容器"""
        async with self._ensure_async_lock():
            if self._is_running:
                return

            self._is_running = True

            # 启动所有已注册的服务
            await self._start_all_services()

            # 启动健康检查
            self._health_check_task = asyncio.create_task(self._health_check_loop())

            # 发布容器启动事件
            if self._event_bus:
                event = create_system_event(
                    SystemEventType.APPLICATION_STARTED,
                    "service_container",
                    {"container_id": id(self), "services_count": len(self._registrations)}
                )
                self._event_bus.publish("APPLICATION_STARTED", event)
    
    async def stop(self) -> None:
        """停止服务容器"""
        async with self._ensure_async_lock():
            if not self._is_running:
                return

            self._is_running = False

            # 停止健康检查
            if self._health_check_task:
                self._health_check_task.cancel()
                try:
                    await self._health_check_task
                except asyncio.CancelledError:
                    # 健康检查任务被正常取消
                    if self._event_bus:
                        event = create_system_event(
                            SystemEventType.APPLICATION_INFO,
                            "service_container",
                            {"message": "健康检查任务已停止"}
                        )
                        self._event_bus.publish("APPLICATION_INFO", event)

            # 停止所有服务
            await self._stop_all_services()

            # 发布容器停止事件
            if self._event_bus:
                event = create_system_event(
                    SystemEventType.APPLICATION_STOPPING,
                    "service_container",
                    {"container_id": id(self)}
                )
                self._event_bus.publish("APPLICATION_STOPPING", event)
    
    def register_service(self, service_type: Type[T], implementation_type: Type[T],
                        lifetime: ServiceLifetime = ServiceLifetime.SINGLETON,
                        configuration: Optional[Dict[str, Any]] = None,
                        health_check: Optional[Callable[[], bool]] = None) -> 'EnhancedServiceContainer':
        """注册服务"""
        with self._sync_lock:
            # 分析依赖关系
            dependencies = self._analyze_dependencies(implementation_type)
            
            # 创建注册信息
            registration = ServiceRegistration(
                service_type=service_type,
                implementation_type=implementation_type,
                lifetime=lifetime,
                health_check=health_check,
                dependencies=dependencies,
                configuration=configuration or {}
            )
            
            self._registrations[service_type] = registration
            
            # 注册到基础容器
            self._base_container.register(service_type, implementation_type, lifetime)
            
            return self
    
    def register_instance(self, service_type: Type[T], instance: T) -> 'EnhancedServiceContainer':
        """注册服务实例"""
        with self._sync_lock:
            registration = ServiceRegistration(
                service_type=service_type,
                implementation_type=type(instance),
                lifetime=ServiceLifetime.SINGLETON,
                instance=instance
            )
            
            self._registrations[service_type] = registration
            self._base_container.register_instance(service_type, instance)
            
            return self
    
    def register_event_bus(self, event_bus: IEventBus) -> 'EnhancedServiceContainer':
        """注册事件总线"""
        self._event_bus = event_bus
        # 注册为多个类型以支持不同的解析方式
        self.register_instance(IEventBus, event_bus)
        self.register_instance(EventBus, event_bus)
        return self
    
    def resolve(self, service_type: Type[T]) -> T:
        """解析服务"""
        return self._base_container.resolve(service_type)
    
    async def resolve_async(self, service_type: Type[T]) -> T:
        """异步解析服务"""
        # 对于异步服务，确保它们已经启动
        service = self.resolve(service_type)
        
        if isinstance(service, IBaseService):
            if service.get_service_status() != ServiceStatus.RUNNING:
                await service.start_service()
        
        return service
    
    def get_service_health(self, service_type: Type) -> ServiceHealthStatus:
        """获取服务健康状态"""
        with self._sync_lock:
            if service_type not in self._registrations:
                return ServiceHealthStatus.UNKNOWN
            
            registration = self._registrations[service_type]
            
            # 如果有自定义健康检查
            if registration.health_check:
                try:
                    is_healthy = registration.health_check()
                    return ServiceHealthStatus.HEALTHY if is_healthy else ServiceHealthStatus.UNHEALTHY
                except Exception:
                    return ServiceHealthStatus.UNHEALTHY
            
            # 如果是IBaseService，检查服务状态
            if registration.instance and isinstance(registration.instance, IBaseService):
                status = registration.instance.get_service_status()
                if status == ServiceStatus.RUNNING:
                    return ServiceHealthStatus.HEALTHY
                elif status == ServiceStatus.ERROR:
                    return ServiceHealthStatus.UNHEALTHY
                else:
                    return ServiceHealthStatus.DEGRADED
            
            return ServiceHealthStatus.UNKNOWN
    
    def get_all_services_health(self) -> Dict[str, Dict[str, Any]]:
        """获取所有服务的健康状态"""
        health_report = {}
        
        for service_type, registration in self._registrations.items():
            service_name = service_type.__name__
            health_status = self.get_service_health(service_type)
            
            health_info = {
                "status": health_status.value,
                "service_type": service_name,
                "lifetime": registration.lifetime.value,
                "has_instance": registration.instance is not None
            }
            
            # 如果是IBaseService，添加详细信息
            if registration.instance and isinstance(registration.instance, IBaseService):
                service_health = registration.instance.get_service_health()
                health_info.update(service_health)
            
            health_report[service_name] = health_info
        
        return health_report
    
    def configure_service(self, service_type: Type, configuration: Dict[str, Any]) -> bool:
        """配置服务"""
        with self._sync_lock:
            if service_type not in self._registrations:
                return False

            registration = self._registrations[service_type]
            registration.configuration.update(configuration)

            # 如果服务还没有实例化，先解析它
            if registration.instance is None:
                try:
                    service = self.resolve(service_type)
                    registration.instance = service
                except Exception:
                    return False

            # 如果服务支持配置
            if isinstance(registration.instance, IConfigurableService):
                return registration.instance.update_configuration(configuration)

            return True
    
    def _analyze_dependencies(self, implementation_type: Type) -> List[Type]:
        """分析服务依赖关系"""
        dependencies = []
        
        try:
            signature = inspect.signature(implementation_type.__init__)
            for param_name, param in signature.parameters.items():
                if param_name == 'self':
                    continue
                
                if param.annotation != inspect.Parameter.empty:
                    dependencies.append(param.annotation)
        
        except Exception as e:
            # 依赖分析失败，记录错误但不影响注册
            if self._event_bus:
                error_info = ErrorInfo(
                    code="DEPENDENCY_ANALYSIS_FAILED",
                    message=f"依赖分析失败: {implementation_type.__name__}",
                    details=str(e)
                )
                event = create_system_event(
                    SystemEventType.APPLICATION_ERROR,
                    "service_container",
                    error_info.to_dict()
                )
                self._event_bus.publish("APPLICATION_ERROR", event)
        
        return dependencies
    
    async def _start_all_services(self) -> None:
        """启动所有服务"""
        for service_type, registration in self._registrations.items():
            try:
                if registration.lifetime == ServiceLifetime.SINGLETON:
                    service = self.resolve(service_type)
                    registration.instance = service
                    
                    if isinstance(service, IBaseService):
                        await service.start_service()
            
            except Exception as e:
                if self._event_bus:
                    error_info = ErrorInfo(
                        code="SERVICE_START_FAILED",
                        message=f"服务启动失败: {service_type.__name__}",
                        details=str(e)
                    )
                    
                    event = create_system_event(
                        SystemEventType.APPLICATION_ERROR,
                        "service_container",
                        error_info.to_dict(),
                        EventPriority.HIGH
                    )
                    self._event_bus.publish("APPLICATION_ERROR", event)
    
    async def _stop_all_services(self) -> None:
        """停止所有服务"""
        for registration in self._registrations.values():
            try:
                if (registration.instance and 
                    isinstance(registration.instance, IBaseService)):
                    await registration.instance.stop_service()
            
            except Exception as e:
                # 服务停止失败，记录错误但继续停止其他服务
                if self._event_bus:
                    error_info = ErrorInfo(
                        code="SERVICE_STOP_FAILED",
                        message=f"服务停止失败: {type(registration.instance).__name__}",
                        details=str(e)
                    )
                    event = create_system_event(
                        SystemEventType.APPLICATION_ERROR,
                        "service_container",
                        error_info.to_dict()
                    )
                    self._event_bus.publish("APPLICATION_ERROR", event)
    
    async def _health_check_loop(self) -> None:
        """健康检查循环"""
        while self._is_running:
            try:
                await asyncio.sleep(self._health_check_interval)
                
                if not self._is_running:
                    break
                
                # 执行健康检查
                unhealthy_services = []
                
                for service_type in self._registrations:
                    health = self.get_service_health(service_type)
                    if health == ServiceHealthStatus.UNHEALTHY:
                        unhealthy_services.append(service_type.__name__)
                
                # 如果有不健康的服务，发布警告事件
                if unhealthy_services and self._event_bus:
                    event = create_system_event(
                        SystemEventType.APPLICATION_ERROR,
                        "health_checker",
                        {
                            "unhealthy_services": unhealthy_services,
                            "check_time": time.time()
                        },
                        EventPriority.HIGH
                    )
                    self._event_bus.publish("APPLICATION_ERROR", event)
            
            except asyncio.CancelledError:
                break
            except Exception as e:
                # 健康检查异常，记录错误但继续运行
                if self._event_bus:
                    error_info = ErrorInfo(
                        code="HEALTH_CHECK_ERROR",
                        message="健康检查执行异常",
                        details=str(e)
                    )
                    event = create_system_event(
                        SystemEventType.APPLICATION_ERROR,
                        "health_checker",
                        error_info.to_dict()
                    )
                    self._event_bus.publish("APPLICATION_ERROR", event)
    
    @contextmanager
    def scope(self):
        """创建服务作用域"""
        with self._base_container.scope() as scope:
            yield scope
    
    def clear(self) -> None:
        """清空容器"""
        with self._sync_lock:
            self._registrations.clear()
            self._base_container.clear()


# 全局增强容器实例
_enhanced_container: Optional[EnhancedServiceContainer] = None


def get_service_container() -> EnhancedServiceContainer:
    """获取全局增强容器实例"""
    global _enhanced_container
    if _enhanced_container is None:
        _enhanced_container = EnhancedServiceContainer()
    return _enhanced_container


def configure_enhanced_services(event_bus: IEventBus) -> EnhancedServiceContainer:
    """配置增强服务容器"""
    container = get_service_container()
    
    # 注册事件总线
    container.register_event_bus(event_bus)
    
    return container
