#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
DTO和事件系统演示

展示新架构的DTO和事件系统如何协同工作
"""

import sys
import os
import time
import json

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from data.dto.scan_dto import ScanRequest, ScanType, ScanResult, ScanStatus, FileInfo
from data.dto.duplicate_dto import DuplicateCheckRequest, DuplicateCheckType
from data.dto.rename_dto import RenameRequest, RenameRuleType
from ui.events.event_definitions import (
    EventData, EventPriority, UIEventType, BusinessEventType,
    create_ui_event, create_business_event
)
from ui.events.event_bus import EventBus


def demo_dto_system():
    """演示DTO系统功能"""
    print("🔧 DTO系统演示")
    print("=" * 50)
    
    # 1. 创建扫描请求
    print("\n1. 创建扫描请求DTO:")
    scan_request = ScanRequest.new_full_scan([
        "/home/<USER>/documents",
        "/home/<USER>/downloads"
    ])
    print(f"   任务ID: {scan_request.task_id}")
    print(f"   扫描类型: {scan_request.scan_type.value}")
    print(f"   是否递归: {scan_request.recursive}")
    print(f"   计算哈希: {scan_request.calculate_hash}")
    print(f"   验证结果: {'✅ 通过' if scan_request.is_valid() else '❌ 失败'}")
    
    # 2. JSON序列化演示
    print("\n2. JSON序列化:")
    json_str = scan_request.to_json()
    print(f"   JSON长度: {len(json_str)} 字符")
    print(f"   JSON预览: {json_str[:100]}...")
    
    # 3. 从JSON反序列化
    print("\n3. JSON反序列化:")
    restored_request = ScanRequest.from_json(json_str)
    print(f"   恢复的任务ID: {restored_request.task_id}")
    print(f"   数据一致性: {'✅ 一致' if restored_request.task_id == scan_request.task_id else '❌ 不一致'}")
    
    # 4. 创建重复文件检查请求
    print("\n4. 创建重复文件检查请求:")
    dup_request = DuplicateCheckRequest.new_hash_check(["/home/<USER>/documents"])
    print(f"   检查类型: {dup_request.check_type.value}")
    print(f"   最小文件大小: {dup_request.min_file_size} 字节")
    
    # 5. 创建重命名请求
    print("\n5. 创建重命名请求:")
    rename_request = RenameRequest.new_simple_replace(
        files=["/path/file1.txt", "/path/file2.txt"],
        old_text="old",
        new_text="new"
    )
    print(f"   文件数量: {len(rename_request.files)}")
    print(f"   规则数量: {len(rename_request.rules)}")
    print(f"   仅预览: {rename_request.preview_only}")


def demo_event_system():
    """演示事件系统功能"""
    print("\n\n🚀 事件系统演示")
    print("=" * 50)
    
    # 创建事件总线
    event_bus = EventBus(max_workers=4, max_queue_size=100)
    event_bus.start()
    
    # 事件接收计数器
    received_events = {
        "ui_events": [],
        "business_events": [],
        "system_events": []
    }
    
    # 定义事件处理器
    def ui_event_handler(event_data: EventData):
        received_events["ui_events"].append(event_data)
        print(f"   📱 UI事件处理: {event_data.event_type} (来源: {event_data.source})")
    
    def business_event_handler(event_data: EventData):
        received_events["business_events"].append(event_data)
        print(f"   💼 业务事件处理: {event_data.event_type} (来源: {event_data.source})")
    
    def scan_progress_handler(event_data: EventData):
        progress = event_data.data.get("progress", 0)
        print(f"   📊 扫描进度: {progress}% - {event_data.data.get('status', '处理中...')}")
    
    try:
        # 1. 订阅事件
        print("\n1. 订阅事件:")
        ui_sub = event_bus.subscribe("SCAN_REQUESTED", ui_event_handler, "ui_module")
        business_sub = event_bus.subscribe("SCAN_STARTED", business_event_handler, "scan_service")
        progress_sub = event_bus.subscribe("SCAN_PROGRESS", scan_progress_handler, "progress_monitor")
        print(f"   ✅ 已订阅3个事件处理器")
        
        # 2. 发布UI事件
        print("\n2. 发布UI事件:")
        ui_event = create_ui_event(
            UIEventType.SCAN_REQUESTED,
            "main_window",
            {
                "directories": ["/home/<USER>/documents"],
                "scan_type": "full_scan"
            }
        )
        event_bus.publish("SCAN_REQUESTED", ui_event)
        
        # 3. 发布业务事件
        print("\n3. 发布业务事件:")
        business_event = create_business_event(
            BusinessEventType.SCAN_STARTED,
            "file_scanner",
            {
                "task_id": "scan_123",
                "total_files": 1000
            }
        )
        event_bus.publish("SCAN_STARTED", business_event)
        
        # 4. 模拟进度事件
        print("\n4. 模拟扫描进度:")
        for progress in [25, 50, 75, 100]:
            progress_event = create_business_event(
                BusinessEventType.SCAN_PROGRESS,
                "file_scanner",
                {
                    "task_id": "scan_123",
                    "progress": progress,
                    "status": f"已处理 {progress * 10}/1000 个文件"
                }
            )
            event_bus.publish("SCAN_PROGRESS", progress_event)
            time.sleep(0.1)  # 模拟处理时间
        
        # 等待事件处理完成
        time.sleep(0.5)
        
        # 5. 显示统计信息
        print("\n5. 事件总线统计:")
        stats = event_bus.get_stats()
        print(f"   📊 已发布事件: {stats['events_published']}")
        print(f"   ✅ 已处理事件: {stats['events_processed']}")
        print(f"   ❌ 失败事件: {stats['events_failed']}")
        print(f"   👥 订阅数量: {stats['subscriptions_count']}")
        print(f"   📋 队列大小: {stats['queue_size']}")
        
        # 6. 显示接收统计
        print("\n6. 事件接收统计:")
        print(f"   📱 UI事件: {len(received_events['ui_events'])} 个")
        print(f"   💼 业务事件: {len(received_events['business_events'])} 个")
        
    finally:
        # 清理资源
        event_bus.stop()
        print("\n✅ 事件总线已停止")


def demo_integration():
    """演示DTO和事件系统集成"""
    print("\n\n🔗 DTO与事件系统集成演示")
    print("=" * 50)
    
    # 创建事件总线
    event_bus = EventBus()
    event_bus.start()
    
    # 模拟扫描服务
    def simulate_scan_service(event_data: EventData):
        """模拟扫描服务处理扫描请求"""
        print(f"   🔍 扫描服务收到请求: {event_data.event_type}")
        
        # 从事件数据中提取DTO
        request_data = event_data.data
        scan_request = ScanRequest(
            task_id=request_data["task_id"],
            directories=request_data["directories"],
            scan_type=ScanType(request_data["scan_type"]),
            recursive=request_data.get("recursive", True),
            calculate_hash=request_data.get("calculate_hash", True),
            check_duplicates=request_data.get("check_duplicates", True)
        )
        
        print(f"   📋 解析的扫描请求: {scan_request.task_id}")
        print(f"   📁 扫描目录数: {len(scan_request.directories)}")
        
        # 发布扫描开始事件
        start_event = create_business_event(
            BusinessEventType.SCAN_STARTED,
            "scan_service",
            {
                "task_id": scan_request.task_id,
                "directories": scan_request.directories
            }
        )
        event_bus.publish("SCAN_STARTED", start_event)
        
        # 模拟扫描完成
        time.sleep(0.1)
        result = ScanResult(
            task_id=scan_request.task_id,
            status=ScanStatus.COMPLETED,
            start_time=time.time() - 1,
            end_time=time.time(),
            total_files=100,
            processed_files=100,
            failed_files=0
        )
        
        # 发布扫描完成事件
        complete_event = create_business_event(
            BusinessEventType.SCAN_COMPLETED,
            "scan_service",
            result.to_dict()
        )
        event_bus.publish("SCAN_COMPLETED", complete_event)
    
    def handle_scan_result(event_data: EventData):
        """处理扫描结果"""
        print(f"   📊 收到扫描结果: 成功率 {event_data.data.get('success_rate', 0):.1f}%")
    
    try:
        # 订阅事件
        event_bus.subscribe("SCAN_REQUESTED", simulate_scan_service, "scan_service")
        event_bus.subscribe("SCAN_COMPLETED", handle_scan_result, "ui_module")
        
        # 创建并发布扫描请求
        print("\n1. 创建扫描请求DTO:")
        scan_request = ScanRequest.new_full_scan(["/home/<USER>/documents"])
        print(f"   任务ID: {scan_request.task_id}")
        
        print("\n2. 通过事件系统发送请求:")
        request_event = create_ui_event(
            UIEventType.SCAN_REQUESTED,
            "main_window",
            scan_request.to_dict()
        )
        event_bus.publish("SCAN_REQUESTED", request_event)
        
        # 等待处理完成
        time.sleep(0.5)
        
        print("\n✅ 集成演示完成")
        
    finally:
        event_bus.stop()


def main():
    """主演示函数"""
    print("🎯 SmartFileManager 新架构演示")
    print("=" * 60)
    print("本演示展示了重构后的DTO和事件系统如何协同工作")
    
    try:
        # 演示DTO系统
        demo_dto_system()
        
        # 演示事件系统
        demo_event_system()
        
        # 演示集成
        demo_integration()
        
        print("\n\n🎉 演示完成!")
        print("=" * 60)
        print("新架构特性:")
        print("✅ 类型安全的DTO数据传输")
        print("✅ 异步事件驱动通信")
        print("✅ 模块间完全解耦")
        print("✅ 高性能并发处理")
        print("✅ 完整的错误处理")
        print("✅ 可扩展的架构设计")
        
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
