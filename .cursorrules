# Smart File Manager - Cursor Rules

## 项目概述
这是一个基于Python的智能文件管理器，采用异步架构和模块化设计，支持文件扫描、重复文件检测、批量重命名等功能。

## 技术栈
- **语言**: Python 3.8+
- **GUI框架**: Tkinter
- **异步框架**: asyncio
- **数据库**: MongoDB
- **依赖注入**: 自定义DI容器
- **测试框架**: pytest

## 项目结构
```
smartfileManger/
├── src/                    # 主要源代码
│   ├── core/              # 核心业务逻辑
│   ├── ui/                # 用户界面组件
│   ├── data/              # 数据访问层
│   ├── utils/             # 工具类和辅助函数
│   └── tests/             # 测试文件
├── config/                # 配置文件
├── reusable_widgets/      # 可复用UI组件
├── docs/                  # 文档
└── logs/                  # 日志文件
```

## 编码规范

### 1. 命名规范
- **类名**: 使用PascalCase (如 `FileScanner`, `MainWindow`)
- **函数/方法名**: 使用snake_case (如 `scan_directory`, `batch_rename_files`)
- **变量名**: 使用snake_case (如 `file_path`, `config_dir`)
- **常量**: 使用UPPER_SNAKE_CASE (如 `MAX_FILE_SIZE`, `DEFAULT_TIMEOUT`)
- **文件名**: 使用snake_case (如 `file_scanner.py`, `main_window.py`)

### 2. 导入规范
- 使用绝对导入: `from src.core.file_scanner import FileScanner`
- 避免循环导入，使用延迟导入或依赖注入
- 按以下顺序组织导入:
  1. 标准库导入
  2. 第三方库导入
  3. 本地模块导入

### 3. 异步编程规范
- 所有长时间运行的操作必须使用异步方法
- 异步方法名以 `_async` 结尾
- 必须支持中断机制 (`interrupt_event`)
- 必须捕获 `asyncio.CancelledError`
- 使用 `UnifiedTaskManager` 统一管理异步任务

### 4. 错误处理
- 使用自定义异常类继承自 `Exception`
- 异步方法必须捕获并处理异常
- 记录详细的错误日志
- 提供有意义的错误消息

### 5. 日志规范
- 使用结构化日志记录
- 不同级别使用不同日志方法: `logger.debug()`, `logger.info()`, `logger.warning()`, `logger.error()`
- 包含足够的上下文信息

## 架构模式

### 1. 依赖注入
- 使用 `DIContainer` 管理依赖
- 避免直接实例化，通过容器解析依赖
- 支持单例和工厂模式

### 2. 事件驱动
- 使用 `EventSystem` 进行组件间通信
- 定义明确的事件类型和数据结构
- 支持异步事件处理

### 3. 分层架构
- **UI层**: 负责用户界面和交互
- **业务层**: 核心业务逻辑
- **数据层**: 数据访问和持久化
- **工具层**: 通用工具和辅助功能

## 异步任务管理

### 1. 统一任务管理器
```python
# 使用 UnifiedTaskManager 管理所有异步任务
from src.utils.unified_task_manager import UnifiedTaskManager

# 提交任务
task_id = manager.submit_task(coroutine, task_name="file_scan")

# 检查状态
status = manager.get_task_status(task_id)

# 中断任务
manager.interrupt_task(task_id)
```

### 2. 异步方法模板
```python
async def batch_process_async(items, callback=None, interrupt_event=None):
    """异步批量处理模板"""
    results = {'success': 0, 'failed': 0, 'total': len(items)}
    try:
        for i, item in enumerate(items):
            # 检查中断信号
            if interrupt_event and interrupt_event.is_set():
                raise asyncio.CancelledError("任务被外部中断")
            
            # 处理逻辑
            result = await process_item(item)
            if result:
                results['success'] += 1
            else:
                results['failed'] += 1
                
            # 进度回调
            if callback:
                callback((i + 1) / len(items) * 100, f"处理中: {i+1}/{len(items)}")
                
    except asyncio.CancelledError:
        logger.warning("批量任务被取消")
        results['status'] = 'cancelled'
    except Exception as e:
        logger.error(f"批量任务发生错误: {e}", exc_info=True)
        results['status'] = 'failed'
    
    return results
```

## UI开发规范

### 1. 线程安全
- 所有UI操作必须在主线程执行
- 使用 `root.after()` 调度UI更新
- 避免在后台线程直接操作UI组件

### 2. 组件设计
- 继承自 `tk.Frame` 或 `tk.Toplevel`
- 实现统一的接口方法
- 支持主题切换和字体大小调整

### 3. 测试规范
```python
@pytest.mark.timeout(10)
def test_ui_component():
    """UI组件测试模板"""
    root = tk.Tk()
    try:
        # 创建组件
        component = Component(root)
        component.pack()
        
        # 测试功能
        # ...
        
        # 清理
        root.after(1000, lambda: (root.quit(), root.destroy()))
        root.mainloop()
    finally:
        if root.winfo_exists():
            root.destroy()
```

## 数据库操作

### 1. 连接管理
- 使用连接池管理数据库连接
- 实现健康检查和重连机制
- 支持事务和批量操作

### 2. 数据模型
- 使用数据类定义模型结构
- 实现序列化和反序列化方法
- 支持数据验证和转换

## 测试规范

### 1. 单元测试
- 每个模块都要有对应的测试文件
- 测试覆盖率不低于80%
- 使用 `pytest` 作为测试框架

### 2. 集成测试
- 测试模块间的交互
- 测试完整的业务流程
- 使用真实的数据库连接

### 3. 性能测试
- 测试大数据量处理性能
- 测试并发操作性能
- 监控内存和CPU使用情况

## 配置管理

### 1. 配置文件
- 使用YAML格式存储配置
- 支持环境变量覆盖
- 实现配置验证和默认值

### 2. 配置加载
```python
from src.utils.config_loader import ConfigLoader

config_loader = ConfigLoader(config_dir)
settings = config_loader.get_config('settings')
```

## 文档规范

### 1. 代码注释
- 所有公共方法必须有文档字符串
- 使用Google风格的文档字符串
- 包含参数、返回值和异常说明

### 2. 架构文档
- 维护架构图和设计文档
- 记录重要的设计决策
- 更新API文档

## 性能优化

### 1. 内存管理
- 及时释放不需要的资源
- 使用生成器处理大文件
- 避免内存泄漏

### 2. 并发优化
- 合理使用线程池和进程池
- 避免线程竞争和死锁
- 优化I/O密集型操作

## 安全考虑

### 1. 文件操作
- 验证文件路径安全性
- 限制文件操作权限
- 防止路径遍历攻击

### 2. 数据验证
- 验证所有用户输入
- 使用参数化查询防止SQL注入
- 加密敏感数据

## 部署和维护

### 1. 日志管理
- 配置日志轮转
- 设置合适的日志级别
- 监控错误和异常

### 2. 监控和告警
- 监控系统资源使用
- 设置性能告警
- 记录关键业务指标

## 代码审查要点

1. **异步代码**: 检查是否正确处理中断和异常
2. **线程安全**: 确保UI操作在主线程执行
3. **资源管理**: 检查是否正确释放资源
4. **错误处理**: 验证异常处理是否完整
5. **性能影响**: 评估代码对性能的影响
6. **可维护性**: 检查代码的可读性和可维护性

## 常见问题解决

### 1. 循环导入
- 使用延迟导入
- 重构模块依赖关系
- 使用依赖注入

### 2. 内存泄漏
- 检查事件监听器是否正确移除
- 验证异步任务是否正确清理
- 监控长时间运行的对象

### 3. 性能问题
- 使用性能分析工具
- 优化数据库查询
- 减少不必要的I/O操作

遵循这些规则将确保代码质量、可维护性和团队协作效率。 