#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
清理数据库中的测试数据
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.data.db_manager import MongoDBManager

def clean_test_data(dry_run=True):
    """
    清理数据库中的测试数据
    
    Args:
        dry_run (bool): 如果为True，只显示将要删除的数据，不实际删除
    """
    try:
        # 连接数据库
        db = MongoDBManager()
        db._connect_with_retry()
        
        print("正在搜索测试数据...")
        print("="*60)
        
        # 定义测试数据的匹配模式
        test_patterns = [
            {'path': {'$regex': 'test', '$options': 'i'}},
            {'file_path': {'$regex': 'test', '$options': 'i'}},
            {'path': {'$regex': 'temp', '$options': 'i'}},
            {'file_path': {'$regex': 'temp', '$options': 'i'}},
            {'path': {'$regex': 'tmp', '$options': 'i'}},
            {'file_path': {'$regex': 'tmp', '$options': 'i'}},
            {'path': {'$regex': 'demo', '$options': 'i'}},
            {'file_path': {'$regex': 'demo', '$options': 'i'}},
            {'path': {'$regex': 'sample', '$options': 'i'}},
            {'file_path': {'$regex': 'sample', '$options': 'i'}}
        ]
        
        # 查找所有匹配的文件
        test_files = list(db.collection.find({'$or': test_patterns}))
        test_folders = list(db.folders_collection.find({'$or': [
            {'path': {'$regex': 'test|temp|tmp|demo|sample', '$options': 'i'}}
        ]}))
        test_nodes = list(db.tree_nodes_collection.find({'$or': [
            {'path': {'$regex': 'test|temp|tmp|demo|sample', '$options': 'i'}}
        ]}))
        
        total_test_records = len(test_files) + len(test_folders) + len(test_nodes)
        
        if total_test_records == 0:
            print("✅ 没有找到测试数据，数据库很干净！")
            db.close()
            return
        
        print(f"找到 {total_test_records} 条测试数据:")
        print(f"- 测试文件: {len(test_files)} 条")
        print(f"- 测试文件夹: {len(test_folders)} 条")
        print(f"- 测试树节点: {len(test_nodes)} 条")
        print()
        
        # 显示详细信息
        if test_files:
            print("测试文件列表:")
            for i, file_info in enumerate(test_files[:10]):
                path = file_info.get('path') or file_info.get('file_path', '未知路径')
                size = file_info.get('size', 0)
                print(f"  {i+1:2d}. {path} (大小: {size} 字节)")
            if len(test_files) > 10:
                print(f"     ... 还有 {len(test_files) - 10} 个文件")
            print()
        
        if test_folders:
            print("测试文件夹列表:")
            for i, folder_info in enumerate(test_folders[:10]):
                path = folder_info.get('path', '未知路径')
                files_count = folder_info.get('files_count', 0)
                print(f"  {i+1:2d}. {path} ({files_count} 个文件)")
            if len(test_folders) > 10:
                print(f"     ... 还有 {len(test_folders) - 10} 个文件夹")
            print()
        
        if test_nodes:
            print("测试树节点列表:")
            for i, node_info in enumerate(test_nodes[:10]):
                path = node_info.get('path', '未知路径')
                node_type = node_info.get('type', '未知类型')
                print(f"  {i+1:2d}. {path} (类型: {node_type})")
            if len(test_nodes) > 10:
                print(f"     ... 还有 {len(test_nodes) - 10} 个节点")
            print()
        
        if dry_run:
            print("🔍 这是预览模式，没有实际删除数据")
            print("💡 如果要实际删除，请运行: python clean_test_data.py --delete")
        else:
            print("⚠️  即将删除以上测试数据，此操作不可撤销！")
            confirm = input("确认删除吗？(输入 'YES' 确认): ")
            
            if confirm == 'YES':
                # 执行删除操作
                deleted_files = db.collection.delete_many({'$or': test_patterns})
                deleted_folders = db.folders_collection.delete_many({'$or': [
                    {'path': {'$regex': 'test|temp|tmp|demo|sample', '$options': 'i'}}
                ]})
                deleted_nodes = db.tree_nodes_collection.delete_many({'$or': [
                    {'path': {'$regex': 'test|temp|tmp|demo|sample', '$options': 'i'}}
                ]})
                
                print(f"✅ 删除完成:")
                print(f"   - 删除文件记录: {deleted_files.deleted_count} 条")
                print(f"   - 删除文件夹记录: {deleted_folders.deleted_count} 条")
                print(f"   - 删除树节点记录: {deleted_nodes.deleted_count} 条")
                print(f"   - 总计删除: {deleted_files.deleted_count + deleted_folders.deleted_count + deleted_nodes.deleted_count} 条记录")
            else:
                print("❌ 取消删除操作")
        
        # 关闭数据库连接
        db.close()
        
    except Exception as e:
        print(f"操作失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='清理数据库中的测试数据')
    parser.add_argument('--delete', action='store_true', help='实际执行删除操作（默认只预览）')
    
    args = parser.parse_args()
    
    if args.delete:
        print("🚨 删除模式：将实际删除测试数据")
        clean_test_data(dry_run=False)
    else:
        print("🔍 预览模式：只显示将要删除的数据")
        clean_test_data(dry_run=True)

if __name__ == "__main__":
    main()