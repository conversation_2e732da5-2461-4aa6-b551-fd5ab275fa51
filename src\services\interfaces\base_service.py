#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
基础服务接口定义

定义所有业务服务的基础接口和通用功能
遵循RULE-001: 模块职责单一原则
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from enum import Enum
import uuid
import time

from src.data.dto.base_dto import BaseDTO, ErrorInfo


# 常量定义
DEFAULT_TASK_WAIT_INTERVAL = 0.1  # 任务等待间隔（秒）


class ServiceStatus(Enum):
    """服务状态枚举"""
    IDLE = "idle"
    RUNNING = "running"
    PAUSED = "paused"
    STOPPED = "stopped"
    ERROR = "error"


class IBaseService(ABC):
    """
    基础服务接口
    
    所有业务服务都应该继承此接口，提供统一的服务管理功能
    """
    
    @abstractmethod
    def get_service_name(self) -> str:
        """获取服务名称"""
        pass
    
    @abstractmethod
    def get_service_version(self) -> str:
        """获取服务版本"""
        pass
    
    @abstractmethod
    def get_service_status(self) -> ServiceStatus:
        """获取服务状态"""
        pass
    
    @abstractmethod
    async def start_service(self) -> bool:
        """启动服务"""
        pass
    
    @abstractmethod
    async def stop_service(self) -> bool:
        """停止服务"""
        pass
    
    @abstractmethod
    async def pause_service(self) -> bool:
        """暂停服务"""
        pass
    
    @abstractmethod
    async def resume_service(self) -> bool:
        """恢复服务"""
        pass
    
    @abstractmethod
    def get_service_health(self) -> Dict[str, Any]:
        """获取服务健康状态"""
        pass
    
    @abstractmethod
    def get_active_tasks(self) -> List[str]:
        """获取活动任务列表"""
        pass
    
    @abstractmethod
    async def cancel_task(self, task_id: str) -> bool:
        """取消指定任务"""
        pass
    
    @abstractmethod
    async def cancel_all_tasks(self) -> int:
        """取消所有任务，返回取消的任务数量"""
        pass


class ITaskService(ABC):
    """
    任务服务接口
    
    为需要执行长时间运行任务的服务提供任务管理功能
    """
    
    @abstractmethod
    def create_task_id(self) -> str:
        """创建新的任务ID"""
        pass
    
    @abstractmethod
    async def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        pass
    
    @abstractmethod
    async def get_task_result(self, task_id: str) -> Optional[BaseDTO]:
        """获取任务结果"""
        pass
    
    @abstractmethod
    async def is_task_running(self, task_id: str) -> bool:
        """检查任务是否正在运行"""
        pass
    
    @abstractmethod
    async def wait_for_task(self, task_id: str, timeout: Optional[float] = None) -> bool:
        """等待任务完成"""
        pass


class IValidationService(ABC):
    """
    验证服务接口
    
    为需要数据验证的服务提供验证功能
    """
    
    @abstractmethod
    def validate_request(self, request: BaseDTO) -> List[str]:
        """验证请求数据"""
        pass
    
    @abstractmethod
    def validate_file_path(self, file_path: str) -> List[str]:
        """验证文件路径"""
        pass
    
    @abstractmethod
    def validate_directory_path(self, directory_path: str) -> List[str]:
        """验证目录路径"""
        pass
    
    @abstractmethod
    def validate_file_operation(self, operation: str, file_paths: List[str]) -> List[str]:
        """验证文件操作"""
        pass


class IConfigurableService(ABC):
    """
    可配置服务接口
    
    为需要配置管理的服务提供配置功能
    """
    
    @abstractmethod
    def get_configuration(self) -> Dict[str, Any]:
        """获取服务配置"""
        pass
    
    @abstractmethod
    def update_configuration(self, config: Dict[str, Any]) -> bool:
        """更新服务配置"""
        pass
    
    @abstractmethod
    def reset_configuration(self) -> bool:
        """重置配置为默认值"""
        pass
    
    @abstractmethod
    def validate_configuration(self, config: Dict[str, Any]) -> List[str]:
        """验证配置"""
        pass


class BaseServiceImpl(IBaseService, ITaskService):
    """
    基础服务实现
    
    提供服务接口的默认实现，其他服务可以继承此类
    """
    
    def __init__(self, service_name: str, service_version: str = "1.0.0"):
        self._service_name = service_name
        self._service_version = service_version
        self._status = ServiceStatus.IDLE
        self._active_tasks: Dict[str, Dict[str, Any]] = {}
        self._start_time: Optional[float] = None
        self._last_error: Optional[ErrorInfo] = None
    
    def get_service_name(self) -> str:
        """获取服务名称"""
        return self._service_name
    
    def get_service_version(self) -> str:
        """获取服务版本"""
        return self._service_version
    
    def get_service_status(self) -> ServiceStatus:
        """获取服务状态"""
        return self._status
    
    async def start_service(self) -> bool:
        """启动服务"""
        if self._status == ServiceStatus.RUNNING:
            return True
        
        self._status = ServiceStatus.RUNNING
        self._start_time = time.time()
        return True
    
    async def stop_service(self) -> bool:
        """停止服务"""
        if self._status == ServiceStatus.STOPPED:
            return True
        
        # 取消所有活动任务
        await self.cancel_all_tasks()
        
        self._status = ServiceStatus.STOPPED
        return True
    
    async def pause_service(self) -> bool:
        """暂停服务"""
        if self._status == ServiceStatus.RUNNING:
            self._status = ServiceStatus.PAUSED
            return True
        return False
    
    async def resume_service(self) -> bool:
        """恢复服务"""
        if self._status == ServiceStatus.PAUSED:
            self._status = ServiceStatus.RUNNING
            return True
        return False
    
    def get_service_health(self) -> Dict[str, Any]:
        """获取服务健康状态"""
        uptime = time.time() - self._start_time if self._start_time else 0
        
        return {
            "service_name": self._service_name,
            "version": self._service_version,
            "status": self._status.value,
            "uptime_seconds": uptime,
            "active_tasks_count": len(self._active_tasks),
            "last_error": self._last_error.to_dict() if self._last_error else None,
            "memory_usage": self._get_memory_usage(),
            "timestamp": time.time()
        }
    
    def get_active_tasks(self) -> List[str]:
        """获取活动任务列表"""
        return list(self._active_tasks.keys())
    
    async def cancel_task(self, task_id: str) -> bool:
        """取消指定任务"""
        if task_id in self._active_tasks:
            # 子类应该重写此方法来实现具体的取消逻辑
            del self._active_tasks[task_id]
            return True
        return False
    
    async def cancel_all_tasks(self) -> int:
        """取消所有任务"""
        count = len(self._active_tasks)
        for task_id in list(self._active_tasks.keys()):
            await self.cancel_task(task_id)
        return count
    
    def create_task_id(self) -> str:
        """创建新的任务ID"""
        return str(uuid.uuid4())
    
    async def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        return self._active_tasks.get(task_id)
    
    async def get_task_result(self, task_id: str) -> Optional[BaseDTO]:
        """获取任务结果"""
        task_info = self._active_tasks.get(task_id)
        if task_info:
            return task_info.get("result")
        return None
    
    async def is_task_running(self, task_id: str) -> bool:
        """检查任务是否正在运行"""
        task_info = self._active_tasks.get(task_id)
        if task_info:
            return task_info.get("status") == "running"
        return False
    
    async def wait_for_task(self, task_id: str, timeout: Optional[float] = None) -> bool:
        """等待任务完成"""
        import asyncio
        
        start_time = time.time()
        while await self.is_task_running(task_id):
            if timeout and (time.time() - start_time) > timeout:
                return False
            await asyncio.sleep(DEFAULT_TASK_WAIT_INTERVAL)
        
        return True
    
    def _get_memory_usage(self) -> Dict[str, Any]:
        """获取内存使用情况"""
        try:
            import psutil
            import os
            
            process = psutil.Process(os.getpid())
            memory_info = process.memory_info()
            
            return {
                "rss": memory_info.rss,
                "vms": memory_info.vms,
                "percent": process.memory_percent()
            }
        except ImportError:
            return {"error": "psutil not available"}
    
    def _set_error(self, error: ErrorInfo):
        """设置错误信息"""
        self._last_error = error
        self._status = ServiceStatus.ERROR
    
    def _add_task(self, task_id: str, task_info: Dict[str, Any]):
        """添加任务"""
        self._active_tasks[task_id] = {
            **task_info,
            "created_time": time.time(),
            "status": "running"
        }
    
    def _complete_task(self, task_id: str, result: Optional[BaseDTO] = None):
        """完成任务"""
        if task_id in self._active_tasks:
            self._active_tasks[task_id].update({
                "status": "completed",
                "completed_time": time.time(),
                "result": result
            })

    def _fail_task(self, task_id: str, error: ErrorInfo):
        """任务失败"""
        if task_id in self._active_tasks:
            self._active_tasks[task_id].update({
                "status": "failed",
                "failed_time": time.time(),
                "error": error
            })
