# 智能文件管理器编程规则

## 🎯 强制性规则 (MANDATORY RULES)

### RULE-001: 模块化设计原则
**优先级**: 🔴 最高

**规则描述**:
- ✅ **必须**: 每个模块必须有单一职责，不得混合UI逻辑和业务逻辑
- ✅ **必须**: UI层只负责用户界面展示和用户交互
- ✅ **必须**: 业务服务层只负责业务逻辑处理
- ✅ **必须**: 数据访问层只负责数据存储和检索
- ❌ **禁止**: UI层直接调用数据库操作
- ❌ **禁止**: 业务层直接操作UI组件
- ❌ **禁止**: 数据层包含业务判定逻辑

**违规示例**:
```python
# ❌ 错误: UI层直接数据库操作
class FileTreePanel:
    def load_files(self):
        files = self.db.query("SELECT * FROM files")  # 禁止!

# ❌ 错误: 业务层操作UI
class FileScanService:
    def scan_complete(self):
        self.main_window.update_status("完成")  # 禁止!
```

**正确示例**:
```python
# ✅ 正确: UI层通过事件请求数据
class FileTreePanel:
    def load_files(self):
        event = UIEvent("FILE_LIST_REQUESTED", {"panel_id": self.id})
        self.event_bus.publish(event)

# ✅ 正确: 业务层通过事件通知UI
class FileScanService:
    def scan_complete(self):
        event = BusinessEvent("SCAN_COMPLETED", {"task_id": self.task_id})
        self.event_bus.publish(event)
```

### RULE-002: DTO使用规范
**优先级**: 🔴 最高

**规则描述**:
- ✅ **必须**: 模块间数据传递必须使用DTO对象
- ✅ **必须**: DTO类必须使用@dataclass装饰器
- ✅ **必须**: 所有字段必须有类型注解
- ✅ **必须**: DTO对象必须是不可变的(frozen=True)
- ✅ **必须**: DTO必须提供验证方法
- ❌ **禁止**: 传递原始字典或多个独立参数
- ❌ **禁止**: 在传递过程中修改DTO内容

**标准DTO模板**:
```python
@dataclass(frozen=True)
class ScanRequest:
    task_id: str
    directories: List[str]
    recursive: bool = True
    update_database: bool = True
    
    def validate(self) -> List[str]:
        errors = []
        if not self.task_id:
            errors.append("task_id不能为空")
        if not self.directories:
            errors.append("directories不能为空")
        return errors
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ScanRequest':
        return cls(**data)
```

### RULE-003: 事件驱动通信规范
**优先级**: 🔴 最高

**规则描述**:
- ✅ **必须**: 所有模块间通信必须通过事件总线进行
- ✅ **必须**: 事件名称必须遵循 "动词_名词_状态" 格式
- ✅ **必须**: 事件处理器必须是异步的
- ✅ **必须**: 事件处理必须具有幂等性
- ✅ **必须**: 事件处理器必须有超时控制(默认30秒)
- ❌ **禁止**: 模块间直接方法调用
- ❌ **禁止**: 在事件处理中执行阻塞操作

**事件命名规范**:
```python
# ✅ 正确的事件命名
SCAN_REQUESTED = "SCAN_REQUESTED"
SCAN_STARTED = "SCAN_STARTED"
SCAN_PROGRESS = "SCAN_PROGRESS"
SCAN_COMPLETED = "SCAN_COMPLETED"
SCAN_FAILED = "SCAN_FAILED"
SCAN_CANCELLED = "SCAN_CANCELLED"

FILE_PROCESSED = "FILE_PROCESSED"
HASH_CALCULATION_STARTED = "HASH_CALCULATION_STARTED"
WHITELIST_CHECK_COMPLETED = "WHITELIST_CHECK_COMPLETED"
```

### RULE-004: 数据库操作规范
**优先级**: 🔴 最高

**规则描述**:
- ✅ **必须**: 所有数据库操作必须通过DatabaseService
- ✅ **必须**: 数据库操作必须使用批量操作
- ✅ **必须**: 所有数据库操作必须包含在事务中
- ✅ **必须**: 必须使用参数化查询
- ✅ **必须**: 数据库连接必须使用连接池
- ❌ **禁止**: UI层直接进行数据库操作
- ❌ **禁止**: 在循环中进行单独的数据库操作
- ❌ **禁止**: 字符串拼接构建SQL语句

**批量操作限制**:
- 插入操作: 最多1000条
- 更新操作: 最多500条
- 删除操作: 最多200条
- 查询操作: 最多2000条

**正确示例**:
```python
# ✅ 正确: 批量更新操作
def batch_update_files(self, updates: List[Dict[str, Any]]) -> int:
    batch_size = 500
    total_updated = 0
    
    for i in range(0, len(updates), batch_size):
        batch = updates[i:i + batch_size]
        with self.db.transaction():
            result = self.db.bulk_update('files', batch)
            total_updated += result.modified_count
    
    return total_updated
```

### RULE-005: 异步任务管理规范
**优先级**: 🟡 高

**规则描述**:
- ✅ **必须**: 所有长时间运行的操作必须实现为异步任务
- ✅ **必须**: 异步任务必须支持暂停、恢复、取消操作
- ✅ **必须**: 异步任务必须提供进度报告
- ✅ **必须**: 异步任务必须实现超时控制和错误恢复
- ✅ **必须**: 任务状态变更必须是原子性的
- ❌ **禁止**: 在主线程中执行长时间运行的操作
- ❌ **禁止**: 忽略任务的生命周期管理

**任务状态定义**:
```python
class TaskStatus(Enum):
    NOT_STARTED = "not_started"
    QUEUED = "queued"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
```

## 🔍 检查方法

### 自动化检查
```bash
# 运行规则检查器
python tools/code_checker.py --check-rules

# 运行完整质量检查
python tools/code_checker.py --full-check

# 生成质量报告
python tools/code_checker.py --generate-report
```

### 手动检查清单
- [ ] 模块职责单一，无混合职责
- [ ] 使用DTO进行模块间数据传递
- [ ] 通过事件总线进行模块间通信
- [ ] 数据库操作集中在DatabaseService
- [ ] UI层无业务逻辑，业务层无UI操作
- [ ] 异步任务正确实现生命周期管理

## ⚠️ 违规处理

**严重违规 (RULE-001, RULE-002, RULE-003)**:
- 代码审查不通过
- 必须立即修复
- 不允许合并到主分支

**一般违规 (RULE-004, RULE-005)**:
- 警告提示
- 建议在下次迭代中修复
- 可以合并但需要创建技术债务任务

## 📚 相关文档
- [架构规则](ARCHITECTURE_RULES.md)
- [性能规则](PERFORMANCE_RULES.md)
- [代码审查清单](CODE_REVIEW_CHECKLIST.md)
- [重构指南](../guidelines/REFACTORING_GUIDE.md)
