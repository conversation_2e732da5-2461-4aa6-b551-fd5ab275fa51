#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库管理模块

该模块实现了数据库管理接口，提供MongoDB数据库连接和操作功能:
- MongoDBManager: MongoDB数据库管理类，实现DatabaseManagerInterface接口

作者: AI助手
日期: 2023-06-01
版本: 1.0.0
"""

import asyncio
import motor.motor_asyncio
import pymongo
import os
import re
import time
import uuid
from pymongo import MongoClient, InsertOne, UpdateOne, DeleteOne
from pymongo.errors import PyMongoError, ConnectionFailure, OperationFailure
from bson import ObjectId
from typing import List, Dict, Any, Optional, Union, Tuple, Callable
from datetime import datetime
import json
import threading

from src.data.interfaces import DatabaseManagerInterface
from src.data.query_pipeline_manager import get_query_pipeline_manager, QueryPipelineManager
from ..utils.logger import get_logger
from src.utils.format_utils import _normalize_path
from src.utils.interrupt_manager import get_interrupt_manager, InterruptReason
from src.core.progress_tracker import ProgressTracker

# 创建MongoDB管理器日志记录器
logger = get_logger("MongoDBManager")


def build_duplicate_files_pipeline(min_size=None, exists=True):
    """
    构建重复文件查找聚合管道 - 重构版本

    使用统一的查询管道管理器
    """
    manager = get_query_pipeline_manager()
    return manager.build_duplicate_files_pipeline(min_size=min_size, exists=exists)

class MongoDBManager(DatabaseManagerInterface):
    """
    MongoDB 数据库管理类，实现DatabaseManagerInterface接口
    
    支持同步和异步操作
    """
    def __init__(self, db_name: str = "fileinfodb", collection_name: str = "files", 
                 max_pool_size: int = 100, min_pool_size: int = 10,
                 host: str = "localhost", port: int = 27017,
                 max_retries: int = 3, retry_delay: float = 1.0,
                 connect_timeout: int = 2000, server_timeout: int = 3000,
                 progress_callback=None, event_system=None):
        """
        初始化MongoDB连接
        
        参数:
            db_name: 数据库名称
            collection_name: 集合名称
            max_pool_size: 连接池最大连接数
            min_pool_size: 连接池最小连接数
            host: MongoDB服务器主机名
            port: MongoDB服务器端口
            max_retries: 最大重试次数
            retry_delay: 重试间隔（秒）
            connect_timeout: 连接超时时间（毫秒）
            server_timeout: 服务器选择超时时间（毫秒）
        """
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.connect_timeout = connect_timeout
        self.server_timeout = server_timeout
        
        # 构建连接URI
        connection_uri = f"mongodb://{host}:{port}/"
        
        # 初始化连接参数
        self.db_name = db_name
        self.collection_name = collection_name
        self.connection_uri = connection_uri
        self.pool_config = {
            "maxPoolSize": max_pool_size,
            # "minPoolSize": min_pool_size,  # MongoClient 不支持 minPoolSize
            "waitQueueTimeoutMS": self.connect_timeout,
            "connectTimeoutMS": self.connect_timeout,
            "serverSelectionTimeoutMS": self.server_timeout
        }
        
        # 设置进度回调和事件系统
        self.progress_callback = progress_callback
        self.event_system = event_system

        # 初始化logger
        self.logger = get_logger("MongoDBManager")

        # 查询管道管理器
        self.query_manager = get_query_pipeline_manager()

        # 尝试建立连接
        self._connect_with_retry()

    def _connect_with_retry(self):
        """
        带重试机制的数据库连接
        """
        last_error = None
        self.logger.info(f"开始连接MongoDB数据库，连接URI: {self.connection_uri}")
        self.logger.info(f"连接参数: 数据库={self.db_name}, 集合={self.collection_name}")
        self.logger.info(f"连接池配置: {self.pool_config}")

        for attempt in range(self.max_retries):
            try:
                self.logger.info(f"尝试第{attempt + 1}次连接MongoDB...")

                # 同步客户端配置
                self.client = MongoClient(self.connection_uri,
                                         maxPoolSize=self.pool_config["maxPoolSize"],
                                         waitQueueTimeoutMS=self.pool_config["waitQueueTimeoutMS"],
                                         connectTimeoutMS=self.pool_config["connectTimeoutMS"],
                                         serverSelectionTimeoutMS=self.pool_config["serverSelectionTimeoutMS"])
                self.db = self.client[self.db_name]
                self.collection = self.db[self.collection_name]

                # 验证连接
                self.logger.info("验证数据库连接...")
                self.client.admin.command('ping')
                self.logger.info("数据库连接验证成功")

                # 创建索引以提升查询性能
                self._create_indexes()

                # 异步客户端配置
                self.logger.info("创建异步客户端...")
                self.async_client = motor.motor_asyncio.AsyncIOMotorClient(
                    self.connection_uri,
                    maxPoolSize=self.pool_config["maxPoolSize"],
                    waitQueueTimeoutMS=self.pool_config["waitQueueTimeoutMS"],
                    connectTimeoutMS=self.pool_config["connectTimeoutMS"],
                    serverSelectionTimeoutMS=self.pool_config["serverSelectionTimeoutMS"]
                )
                self.async_db = self.async_client[self.db_name]
                self.async_collection = self.async_db[self.collection_name]
                
                self.logger.info(f"成功连接到MongoDB数据库 {self.db_name}.{self.collection_name}")

                # 发布连接成功事件
                self._publish_event("db_connected", {
                    "database": self.db_name,
                    "collection": self.collection_name,
                    "status": "connected"
                })
                return

            except Exception as e:
                last_error = e
                self.logger.warning(f"连接MongoDB失败，尝试第{attempt + 1}次重试: {e}")
                self.logger.warning(f"错误类型: {type(e).__name__}")
                if attempt < self.max_retries - 1:
                    self.logger.info(f"等待 {self.retry_delay} 秒后重试...")
                    time.sleep(self.retry_delay)

        self.logger.error(f"连接MongoDB数据库失败，已达到最大重试次数: {last_error}")
        self.logger.error(f"最终错误类型: {type(last_error).__name__}")
        self.logger.error(f"最终错误详情: {str(last_error)}")
        
        # 发布连接失败事件
        self._publish_event("db_disconnected", {
            "database": self.db_name,
            "collection": self.collection_name,
            "status": "disconnected",
            "error": str(last_error)
        })
        
        if last_error is not None:
            raise last_error
        else:
            raise Exception("未知的数据库连接错误")
    
    def _create_indexes(self):
        """
        创建数据库索引，保证 path、file_id 唯一
        """
        self.logger.info("开始创建数据库索引")
        indexes = [
            (["file_id"], {"unique": True, "sparse": True}),  # 文件ID唯一索引
            (["path"], {"unique": True}),  # 路径唯一索引
            (["hash"], {}),
            (["size"], {}),
            (["name"], {}),
            (["extension"], {}),
            (["modified_time"], {}),
            (["is_video"], {}),
            (["is_junk"], {}),
            (["is_whitelist"], {})
        ]
        for i, (keys, options) in enumerate(indexes):
            try:
                if options:
                    self.collection.create_index(keys, **options)
                else:
                    self.collection.create_index(keys)
                self.logger.debug(f"创建索引: {keys}")
                self._report_progress("index_creation", i + 1, len(indexes), f"创建索引: {keys}")
            except Exception as e:
                self.logger.warning(f"创建索引失败 {keys}: {e}")
        self.logger.info("数据库索引创建完成")
    
    def generate_file_id(self) -> str:
        """
        生成唯一的文件ID
        
        返回:
            唯一的文件ID字符串
        """
        return str(uuid.uuid4())
    
    def check_connection_health(self) -> bool:
        """
        检查数据库连接健康状态，如果连接断开会尝试重新连接
        
        返回:
            bool: 连接是否健康
        """
        try:
            self.logger.info("开始检查数据库连接健康状态...")
            
            # 检查客户端是否存在
            if not hasattr(self, 'client') or self.client is None:
                self.logger.warning("MongoDB客户端未初始化")
                return False
            
            # 尝试ping数据库
            self.client.admin.command('ping')
            self.logger.info("数据库连接健康状态检查通过")
            return True
            
        except Exception as e:
            self.logger.warning(f"数据库连接健康状态检查失败: {e}")
            self.logger.info("尝试重新连接数据库...")
            
            try:
                # 尝试重新连接
                self._connect_with_retry()
                self.logger.info("数据库重新连接成功")
                return True
            except Exception as reconnect_error:
                self.logger.error(f"数据库重新连接失败: {reconnect_error}")
                return False

    def is_connected(self) -> bool:
        """
        检查数据库是否已连接
        
        返回:
            bool: 是否已连接
        """
        try:
            # 检查客户端是否存在
            if not hasattr(self, 'client') or self.client is None:
                return False
            
            # 尝试ping数据库
            self.client.admin.command('ping')
            return True
            
        except Exception as e:
            self.logger.debug(f"数据库连接检查失败: {e}")
            return False
    
    def _ensure_connected(self, operation_name: str = "操作") -> bool:
        """
        确保数据库连接正常，如果断开则尝试重连
        
        参数:
            operation_name: 操作名称，用于日志记录
            
        返回:
            bool: 连接是否正常
        """
        if not self.check_connection_health():
            self.logger.error(f"执行{operation_name}失败：数据库连接异常且无法重连")
            return False
        return True

    def insert_file_info(self, file_info: Dict[str, Any]) -> str:
        if not self._ensure_connected("插入文件信息"):
            raise ConnectionError("数据库连接失败")
        try:
            file_info = self._process_datetime_fields(file_info)
            file_info["path"] = _normalize_path(file_info.get("path"))
            if not file_info.get("file_id"):
                file_info["file_id"] = self.generate_file_id()
            result = self.collection.update_one(
                {"path": file_info["path"]},
                {"$set": file_info},
                upsert=True
            )
            if result.upserted_id:
                self.logger.debug(f"成功插入文件信息: {file_info.get('path', 'unknown')}, file_id: {file_info.get('file_id')} (仅存储文件信息，不进行自动检查)")
                return str(result.upserted_id)
            else:
                self.logger.debug(f"成功更新文件信息: {file_info.get('path', 'unknown')}, file_id: {file_info.get('file_id')} (仅存储文件信息，不进行自动检查)")
                doc = self.collection.find_one({"path": file_info["path"]})
                if doc is not None:
                    return str(doc["_id"])
                else:
                    self.logger.error(f"未找到插入/更新后的文档: {file_info.get('path', 'unknown')}")
                    return ""
        except Exception as e:
            self.logger.error(f"插入文件信息失败: {e}")
            raise

    def find_files(self, query: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        if not self._ensure_connected("查询文件信息"):
            raise ConnectionError("数据库连接失败")
        try:
            if query is None:
                query = {}
            # 如果按path查找，做标准化（但不处理MongoDB查询操作符）
            if "path" in query and isinstance(query["path"], str):
                query["path"] = _normalize_path(query["path"])
            results = list(self.collection.find(query))
            self.logger.info(f"查询文件信息成功，找到 {len(results)} 条记录")
            return results
        except Exception as e:
            self.logger.error(f"查询文件信息失败: {e}")
            raise
     
    def _report_progress(self, operation, current, total, message=""):
        """
        报告操作进度
        
        参数:
            operation: 操作类型
            current: 当前进度
            total: 总数
            message: 进度消息
        """
        if self.progress_callback:
            try:
                progress_data = {
                    "operation": operation,
                    "current": current,
                    "total": total,
                    "percentage": (current / total * 100) if total > 0 else 0,
                    "message": message
                }
                self.progress_callback(progress_data)
            except Exception as e:
                self.logger.warning(f"进度回调执行失败: {e}")
    
    def _publish_event(self, event_type, data):
        """
        发布事件
        
        参数:
            event_type: 事件类型
            data: 事件数据
        """
        if self.event_system:
            try:
                self.event_system.publish(event_type, data)
            except Exception as e:
                self.logger.warning(f"事件发布失败: {e}")
    
    def update_file_info(self, file_id: str, update_data: Dict[str, Any]) -> int:
        """
        更新文件信息
        
        参数:
            file_id: 文件ID
            update_data: 更新数据
            
        返回:
            更新的文档数量
        """
        if not self._ensure_connected("更新文件信息"):
            raise ConnectionError("数据库连接失败")
            
        try:
            # 处理日期时间字段
            update_data = self._process_datetime_fields(update_data)
            
            result = self.collection.update_one(
                {"_id": ObjectId(file_id)},
                {"$set": update_data}
            )
            self.logger.info(f"更新文件信息成功，文件ID: {file_id}, 更新数量: {result.modified_count}")
            return result.modified_count
        except Exception as e:
            self.logger.error(f"更新文件信息失败，文件ID: {file_id}, 错误: {e}")
            raise
    
    def delete_file_info(self, file_id: str) -> int:
        """
        删除文件信息

        参数:
            file_id: 文件ID

        返回:
            删除的文档数量
        """
        if not self._ensure_connected("删除文件信息"):
            raise ConnectionError("数据库连接失败")

        try:
            result = self.collection.delete_one({"_id": ObjectId(file_id)})
            self.logger.info(f"删除文件信息成功，文件ID: {file_id}, 删除数量: {result.deleted_count}")
            return result.deleted_count
        except Exception as e:
            self.logger.error(f"删除文件信息失败，文件ID: {file_id}, 错误: {e}")
            raise

    def delete_file_by_path(self, file_path: str) -> bool:
        """
        根据路径删除文件信息

        参数:
            file_path: 文件路径

        返回:
            是否删除成功
        """
        if not self._ensure_connected("根据路径删除文件信息"):
            raise ConnectionError("数据库连接失败")

        try:
            # 标准化路径
            normalized_path = _normalize_path(file_path)

            # 删除文件记录
            result = self.collection.delete_one({
                "$or": [
                    {"file_path": normalized_path},
                    {"path": normalized_path}
                ]
            })

            success = result.deleted_count > 0
            if success:
                self.logger.info(f"根据路径删除文件信息成功: {normalized_path}")
            else:
                self.logger.warning(f"根据路径删除文件信息失败，未找到记录: {normalized_path}")

            return success
        except Exception as e:
            self.logger.error(f"根据路径删除文件信息失败，路径: {file_path}, 错误: {e}")
            raise
    
    def cleanup_nonexistent_files(self, batch_size: int = 100) -> Dict[str, int]:
        """
        清理数据库中不存在的文件记录
        
        参数:
            batch_size: 批处理大小
            
        返回:
            清理结果统计
        """
        if not self._ensure_connected("清理不存在文件"):
            raise ConnectionError("数据库连接失败")
            
        try:
            import os
            
            self.logger.info("开始清理数据库中不存在的文件记录...")
            
            # 统计变量
            total_checked = 0
            total_deleted = 0
            total_exist = 0
            
            # 分批处理，避免内存占用过大
            offset = 0
            while True:
                # 获取一批文件记录
                files_batch = list(self.collection.find(
                    {}, 
                    {"_id": 1, "path": 1}
                ).skip(offset).limit(batch_size))
                
                if not files_batch:
                    break
                
                # 检查文件是否存在
                files_to_delete = []
                for file_doc in files_batch:
                    file_path = file_doc.get('path', '')
                    if file_path:
                        if os.path.exists(file_path):
                            total_exist += 1
                        else:
                            files_to_delete.append(file_doc['_id'])
                            total_deleted += 1
                    total_checked += 1
                
                # 批量删除不存在的文件记录
                if files_to_delete:
                    delete_result = self.collection.delete_many({"_id": {"$in": files_to_delete}})
                    self.logger.info(f"删除 {len(files_to_delete)} 个不存在文件的记录")
                
                offset += batch_size
                
                # 报告进度
                if total_checked % (batch_size * 10) == 0:
                    self.logger.info(f"已检查 {total_checked} 个文件，删除 {total_deleted} 个不存在文件的记录")
            
            # 最终统计
            result = {
                "total_checked": total_checked,
                "total_deleted": total_deleted,
                "total_exist": total_exist
            }
            
            self.logger.info(f"数据库清理完成: 检查 {total_checked} 个文件，删除 {total_deleted} 个不存在文件的记录，{total_exist} 个文件存在")
            
            return result
            
        except Exception as e:
            self.logger.error(f"清理不存在文件失败: {e}")
            raise
    
    def batch_insert_file_info(self, file_info_list: List[Dict[str, Any]]) -> List[str]:
        if not self._ensure_connected("批量插入文件信息"):
            raise ConnectionError("数据库连接失败")
        try:
            if not file_info_list:
                return []
            processed_list = [self._process_datetime_fields(info) for info in file_info_list]
            # 路径标准化和生成file_id
            for info in processed_list:
                info["path"] = _normalize_path(info["path"])
                # 如果没有file_id，则生成一个
                if not info.get("file_id"):
                    info["file_id"] = self.generate_file_id()
            operations = []
            for info in processed_list:
                operations.append(
                    UpdateOne(
                        {"path": info["path"]},
                        {"$set": info},
                        upsert=True
                    )
                )
            result = self.collection.bulk_write(operations, ordered=False)
            inserted_ids = []
            for info in processed_list:
                doc = self.collection.find_one({"path": info["path"]})
                if doc:
                    inserted_ids.append(str(doc["_id"]))
            self.logger.info(f"批量插入文件信息成功，插入/更新数量: {len(inserted_ids)}")
            return inserted_ids
        except Exception as e:
            self.logger.error(f"批量插入文件信息失败: {e}")
            raise
    
    def batch_update_file_info(self, update_data_list: List[Dict[str, Any]]) -> int:
        """
        批量更新文件信息
        
        参数:
            update_data_list: 更新数据列表，每个元素必须包含_id字段
            
        返回:
            更新的文档数量
        """
        if not self._ensure_connected("批量更新文件信息"):
            raise ConnectionError("数据库连接失败")
            
        try:
            if not update_data_list:
                return 0
            
            operations = []
            for item in update_data_list:
                if "file_id" not in item or "update_data" not in item:
                    continue
                
                file_id = item["file_id"]
                update_data = self._process_datetime_fields(item["update_data"])
                
                operations.append(
                    UpdateOne(
                        {"_id": ObjectId(file_id)},
                        {"$set": update_data}
                    )
                )
            
            if not operations:
                return 0
            
            result = self.collection.bulk_write(operations, ordered=False)
            self.logger.info(f"批量更新文件信息成功，更新数量: {result.modified_count}")
            return result.modified_count
        except Exception as e:
            self.logger.error(f"批量更新文件信息失败: {e}")
            raise
    
    def batch_delete_file_info(self, file_ids: List[str]) -> int:
        """
        批量删除文件信息
        
        参数:
            file_ids: 文件ID列表
            
        返回:
            删除的文档数量
        """
        if not self._ensure_connected("批量删除文件信息"):
            raise ConnectionError("数据库连接失败")
            
        try:
            if not file_ids:
                return 0
            
            operations = []
            for file_id in file_ids:
                operations.append(
                    DeleteOne({"_id": ObjectId(file_id)})
                )
            
            result = self.collection.bulk_write(operations, ordered=False)
            self.logger.info(f"批量删除文件信息成功，删除数量: {result.deleted_count}")
            return result.deleted_count
        except Exception as e:
            self.logger.error(f"批量删除文件信息失败: {e}")
            raise
    
    def batch_upsert_folder_files(self, folder_file_path: str, file_infos: List[Dict[str, Any]], 
                                   show_progress: bool = True) -> Dict[str, int]:
        """
        批量更新文件夹中的文件信息，实现增量更新：
        1. 删除数据库中存在但当前扫描中不存在的文件（已删除的文件）
        2. 更新或插入当前扫描到的文件
        
        参数:
            folder_file_path: 文件夹路径
            file_infos: 文件信息列表
            
        返回:
            操作结果统计字典
        """
        if not self._ensure_connected("批量更新文件夹文件信息"):
            raise ConnectionError("数据库连接失败")
            
        try:
            import re
            import os
            
            folder_file_path = _normalize_path(folder_file_path)
            
            # 构建文件夹路径匹配模式 - 精确匹配该文件夹下的所有文件
            # 匹配模式：文件夹路径 + / + 任意字符（文件名或子路径）
            if folder_file_path == '' or folder_file_path == '.':
                # 根目录情况
                path_pattern = "^[^/]+$|^.+/.+$"  # 匹配根目录文件或任何子目录文件
            else:
                # 具体文件夹情况
                path_pattern = f"^{re.escape(folder_file_path)}/"
            
            # 查询该文件夹下现有的文件记录
            existing_query = {"path": {"$regex": path_pattern}}
            

            
            existing_docs = list(self.collection.find(existing_query))
            existing_count = len(existing_docs)
            
            # 创建现有文件路径集合（用于快速查找）
            existing_paths = {doc['path'] for doc in existing_docs}
            
            self.logger.info(f"开始增量更新文件夹 {folder_file_path} 的文件信息")
            self.logger.info(f"  - 现有记录: {existing_count}条")
            self.logger.info(f"  - 新记录: {len(file_infos)}条")
            
            # 发布开始事件
            self._publish_event("db_operation_start", {
                "operation": "batch_upsert_folder_files",
                "folder_path": folder_file_path,
                "existing_count": existing_count,
                "new_count": len(file_infos)
            })
            
            # 处理新文件信息的日期时间字段并去重
            processed_file_infos = []
            current_paths = set()
            unique_files = {}
            
            total_files = len(file_infos)
            for i, file_info in enumerate(file_infos):
                try:
                    processed_info = self._process_datetime_fields(file_info.copy())
                    # 确保路径格式一致（统一使用正斜杠，移除末尾斜杠）
                    normalized_path = _normalize_path(processed_info['path'])
                    processed_info['path'] = normalized_path
                    
                    # 使用路径作为键来去重
                    unique_files[normalized_path] = processed_info
                    current_paths.add(normalized_path)
                    
                    # 报告处理进度
                    if show_progress and i % 100 == 0:  # 每100个文件报告一次进度
                        self._report_progress("processing_files", i + 1, total_files, 
                                            f"处理文件信息: {i + 1}/{total_files}")
                        
                except Exception as e:
                    self.logger.error(f"处理文件信息失败: {file_info.get('path', 'unknown')}, 错误: {e}")
                    raise
            
            # 转换回列表
            processed_file_infos = list(unique_files.values())
            
            # 报告处理完成
            if show_progress:
                self._report_progress("processing_files", total_files, total_files, 
                                    f"文件信息处理完成: {len(processed_file_infos)}个唯一文件")
            
            # 找出需要删除的文件（在数据库中存在但当前扫描中不存在）
            paths_to_delete = existing_paths - current_paths
            
            # 统计变量
            deleted_count = 0
            updated_count = 0
            inserted_count = 0
            
            try:
                # 删除已不存在的文件记录
                if paths_to_delete:
                    delete_query = {"path": {"$in": list(paths_to_delete)}}
                    
                    # 报告删除进度
                    if show_progress:
                        self._report_progress("deleting_files", 0, len(paths_to_delete), 
                                            f"删除已不存在的文件记录: {len(paths_to_delete)}条")
                    
                    delete_result = self.collection.delete_many(delete_query)
                    deleted_count = delete_result.deleted_count
                    self.logger.info(f"删除已不存在的文件记录: {deleted_count}条")
                    
                    # 报告删除完成
                    if show_progress:
                        self._report_progress("deleting_files", deleted_count, len(paths_to_delete), 
                                            f"删除完成: {deleted_count}条记录")
                
                # 批量更新或插入当前文件记录
                if processed_file_infos:
                    bulk_operations = []
                    total_operations = len(processed_file_infos)
                    
                    # 报告开始批量操作
                    if show_progress:
                        self._report_progress("bulk_operations", 0, total_operations, 
                                            f"准备批量操作: {total_operations}条记录")
                    
                    for i, file_info in enumerate(processed_file_infos):
                        file_path = file_info['path']
                        
                        # 使用 upsert 操作：如果存在则更新，不存在则插入
                        operation = UpdateOne(
                            {"path": file_path},
                            {"$set": file_info},
                            upsert=True
                        )
                        bulk_operations.append(operation)
                        
                        # 报告操作准备进度
                        if show_progress and i % 500 == 0:  # 每500个操作报告一次进度
                            self._report_progress("bulk_operations", i + 1, total_operations, 
                                                f"准备操作: {i + 1}/{total_operations}")
                    
                    # 执行批量操作
                    if show_progress:
                        self._report_progress("bulk_operations", total_operations, total_operations, 
                                            "执行批量数据库操作...")
                    
                    bulk_result = self.collection.bulk_write(bulk_operations)
                    updated_count = bulk_result.modified_count
                    inserted_count = bulk_result.upserted_count
                    
                    self.logger.info(f"批量操作完成: 更新 {updated_count}条，插入 {inserted_count}条")
                    
                    # 报告批量操作完成
                    if show_progress:
                        self._report_progress("bulk_operations", total_operations, total_operations, 
                                            f"批量操作完成: 插入 {inserted_count}条，更新 {updated_count}条")
                
            except Exception as e:
                self.logger.error(f"增量更新文件夹文件信息失败: {e}")
                raise
            
            result_stats = {
                "deleted_count": deleted_count,
                "updated_count": updated_count,
                "inserted_count": inserted_count,
                "folder_path": folder_file_path
            }
            
            # 发布完成事件
            self._publish_event("db_operation_complete", {
                "operation_type": "batch_upsert",
                "folder_path": folder_file_path,
                "result": result_stats,
                "success": True
            })
            
            self.logger.info(f"成功增量更新文件夹 {folder_file_path}")
            self.logger.info(f"  - 删除记录: {deleted_count}条")
            self.logger.info(f"  - 更新记录: {updated_count}条")
            self.logger.info(f"  - 插入记录: {inserted_count}条")
            self.logger.info(f"  - 总操作: {deleted_count + updated_count + inserted_count}条")
            return result_stats
            
        except Exception as e:
            self.logger.error(f"批量更新文件夹文件信息失败: {folder_file_path}, 错误: {e}")
            raise
    
    def count_folder_files(self, folder_file_path: str) -> int:
        """
        统计文件夹下的文件数量
        
        参数:
            folder_file_path: 文件夹路径
            
        返回:
            文件数量
        """
        if not self._ensure_connected("统计文件夹文件数量"):
            raise ConnectionError("数据库连接失败")
            
        try:
            import re
            import os
            
            folder_file_path = _normalize_path(folder_file_path)
            
            # 规范化文件夹路径（统一使用正斜杠，移除末尾斜杠）
            if folder_file_path == '' or folder_file_path == '.':
                # 根目录情况
                path_pattern = "^[^/]+$|^.+/.+$"  # 匹配根目录文件或任何子目录文件
            else:
                # 具体文件夹情况
                path_pattern = f"^{re.escape(folder_file_path)}/"
            
            # 查询该文件夹下的文件数量
            query = {"path": {"$regex": path_pattern}}
            count = self.collection.count_documents(query)
            

            return count
            
        except Exception as e:
            self.logger.error(f"统计文件夹文件数量失败: {folder_file_path}, 错误: {e}")
            return 0
    
    def find_duplicate_files(self, min_size: Optional[int] = None, exists: bool = True) -> Dict[str, List[Dict[str, Any]]]:
        """统一查找重复文件（同步） - 重构版本"""
        start_time = time.time()
        try:
            # 使用统一的查询管道管理器
            pipeline = self.query_manager.build_duplicate_files_pipeline(min_size=min_size, exists=exists)

            # 执行查询
            result = self.db.command("aggregate", self.collection.name, pipeline=pipeline, cursor={})
            duplicates: Dict[str, Any] = {}

            for group in result["cursor"]["firstBatch"]:
                hash_value = group["_id"]
                duplicates[hash_value] = group["files"]

            # 记录性能指标
            execution_time = time.time() - start_time
            self.query_manager.record_query_metrics(
                query_type="find_duplicate_files",
                execution_time=execution_time,
                result_count=len(duplicates)
            )

            return duplicates
        except Exception as e:
            execution_time = time.time() - start_time
            self.query_manager.record_query_metrics(
                query_type="find_duplicate_files",
                execution_time=execution_time,
                result_count=0,
                error=str(e)
            )
            self.logger.error(f"查找重复文件失败: {e}")
            raise
    
    def find_files_by_size_range(self, min_size: int, max_size: int) -> List[Dict[str, Any]]:
        """
        按大小范围查找文件
        
        参数:
            min_size: 最小文件大小（字节）
            max_size: 最大文件大小（字节）
            
        返回:
            匹配的文件信息列表
        """
        try:
            query = {"size": {"$gte": min_size, "$lte": max_size}}
            results = list(self.collection.find(query))
            self.logger.info(f"按大小范围查找文件成功，找到 {len(results)} 条记录")
            return results
        except Exception as e:
            self.logger.error(f"按大小范围查找文件失败: {e}")
            raise
    
    def find_files_by_extension(self, extensions: List[str]) -> List[Dict[str, Any]]:
        """
        按扩展名查找文件
        
        参数:
            extensions: 扩展名列表
            
        返回:
            匹配的文件信息列表
        """
        try:
            # 确保扩展名格式一致（小写，带点）
            normalized_extensions = []
            for ext in extensions:
                if not ext.startswith("."):
                    ext = "." + ext
                normalized_extensions.append(ext.lower())
            
            query = {"extension": {"$in": normalized_extensions}}
            results = list(self.collection.find(query))
            self.logger.info(f"按扩展名查找文件成功，找到 {len(results)} 条记录")
            return results
        except Exception as e:
            self.logger.error(f"按扩展名查找文件失败: {e}")
            raise
    
    def find_files_by_date_range(self, start_date: str, end_date: str, date_field: str = "modified_time") -> List[Dict[str, Any]]:
        """
        按日期范围查找文件
        
        参数:
            start_date: 开始日期（ISO格式）
            end_date: 结束日期（ISO格式）
            date_field: 日期字段名称
            
        返回:
            匹配的文件信息列表
        """
        try:
            # 转换日期字符串为datetime对象
            start_datetime = datetime.fromisoformat(start_date)
            end_datetime = datetime.fromisoformat(end_date)
            
            # 构建查询条件
            query = {date_field: {"$gte": start_datetime, "$lte": end_datetime}}
            results = list(self.collection.find(query))
            self.logger.info(f"按日期范围查找文件成功，找到 {len(results)} 条记录")
            return results
        except Exception as e:
            self.logger.error(f"按日期范围查找文件失败: {e}")
            raise
    
    def clear_database(self, show_progress: bool = True) -> Dict[str, Any]:
        """
        清空数据库中的所有文件记录
        
        参数:
            show_progress: 是否显示进度
            
        返回:
            操作结果字典
        """
        if not self._ensure_connected("清空数据库"):
            raise ConnectionError("数据库连接失败")
        
        try:
            self.logger.info("开始清空数据库")
            
            # 发布开始事件
            self._publish_event("db_operation_start", {
                "operation": "clear_database",
                "message": "开始清空数据库"
            })
            
            # 获取当前记录数
            if show_progress:
                self._report_progress("clear_database", 0, 100, "统计数据库记录数...")
            
            total_count = self.collection.count_documents({})
            self.logger.info(f"数据库中共有 {total_count} 条记录")
            
            if total_count == 0:
                self.logger.info("数据库已为空，无需清空")
                result = {"deleted_count": 0, "message": "数据库已为空"}
                
                # 发布完成事件
                self._publish_event("db_operation_complete", {
                    "operation": "clear_database",
                    "result": result,
                    "success": True
                })
                
                return result
            
            # 报告开始删除
            if show_progress:
                self._report_progress("clear_database", 25, 100, f"开始删除 {total_count} 条记录...")
            
            # 执行删除操作
            delete_result = self.collection.delete_many({})
            deleted_count = delete_result.deleted_count
            
            # 报告删除完成
            if show_progress:
                self._report_progress("clear_database", 75, 100, f"删除完成: {deleted_count} 条记录")
            
            # 重建索引
            if show_progress:
                self._report_progress("clear_database", 90, 100, "重建数据库索引...")
            
            self._create_indexes()
            
            # 完成
            if show_progress:
                self._report_progress("clear_database", 100, 100, "数据库清空完成")
            
            result = {
                "deleted_count": deleted_count,
                "message": f"成功清空数据库，删除了 {deleted_count} 条记录"
            }
            
            self.logger.info(f"数据库清空完成，删除了 {deleted_count} 条记录")
            
            # 发布完成事件
            self._publish_event("db_operation_complete", {
                "operation_type": "clear",
                "result": result,
                "success": True
            })
            
            return result
            
        except Exception as e:
            error_msg = f"清空数据库失败: {e}"
            self.logger.error(error_msg)
            
            # 发布错误事件
            self._publish_event("db_operation_error", {
                "operation": "clear_database",
                "error": str(e),
                "message": error_msg
            })
            
            raise
    
    def refresh_database_stats(self, show_progress: bool = True) -> Dict[str, Any]:
        """
        刷新数据库统计信息
        
        参数:
            show_progress: 是否显示进度
            
        返回:
            数据库统计信息
        """
        if not self._ensure_connected("刷新数据库统计"):
            raise ConnectionError("数据库连接失败")
        
        try:
            self.logger.info("开始刷新数据库统计信息")
            
            # 发布开始事件
            self._publish_event("db_operation_start", {
                "operation": "refresh_database_stats",
                "message": "开始刷新数据库统计信息"
            })
            
            stats: Dict[str, Any] = {}
            
            # 统计总文件数
            if show_progress:
                self._report_progress("refresh_stats", 10, 100, "统计总文件数...")
            stats["total_files"] = self.collection.count_documents({})
            
            # 统计视频文件数
            if show_progress:
                self._report_progress("refresh_stats", 25, 100, "统计视频文件数...")
            stats["video_files"] = self.collection.count_documents({"is_video": True})
            
            # 统计垃圾文件数
            if show_progress:
                self._report_progress("refresh_stats", 40, 100, "统计垃圾文件数...")
            stats["junk_files"] = self.collection.count_documents({"is_junk": True})
            
            # 统计白名单文件数
            if show_progress:
                self._report_progress("refresh_stats", 55, 100, "统计白名单文件数...")
            stats["whitelist_files"] = self.collection.count_documents({"is_whitelist": True})
            
            # 统计文件大小分布
            if show_progress:
                self._report_progress("refresh_stats", 70, 100, "统计文件大小分布...")
            
            size_ranges = [
                ("small", 0, 1024 * 1024),  # < 1MB
                ("medium", 1024 * 1024, 100 * 1024 * 1024),  # 1MB - 100MB
                ("large", 100 * 1024 * 1024, 1024 * 1024 * 1024),  # 100MB - 1GB
                ("huge", 1024 * 1024 * 1024, float('inf'))  # > 1GB
            ]
            
            stats["size_distribution"] = {}
            for size_name, min_size, max_size in size_ranges:
                if max_size == float('inf'):
                    query = {"size": {"$gte": min_size}}
                else:
                    query = {"size": {"$gte": min_size, "$lt": max_size}}
                stats["size_distribution"][size_name] = self.collection.count_documents(query)
            
            # 统计文件扩展名分布
            if show_progress:
                self._report_progress("refresh_stats", 85, 100, "统计文件扩展名分布...")
            
            pipeline = [
                {"$group": {"_id": "$extension", "count": {"$sum": 1}}},
                {"$sort": {"count": -1}},
                {"$limit": 10}  # 只取前10个最常见的扩展名
            ]
            
            extension_stats = list(self.collection.aggregate(pipeline))
            stats["top_extensions"] = {item["_id"]: item["count"] for item in extension_stats}
            
            # 完成
            if show_progress:
                self._report_progress("refresh_stats", 100, 100, "统计信息刷新完成")
            
            self.logger.info(f"数据库统计信息刷新完成: {stats}")
            
            # 发布完成事件
            self._publish_event("db_operation_complete", {
                "operation_type": "refresh_stats",
                "result": stats,
                "success": True
            })
            
            # 发布统计更新事件
            self._publish_event("db_stats_updated", stats)
            
            return stats
            
        except Exception as e:
            error_msg = f"刷新数据库统计信息失败: {e}"
            self.logger.error(error_msg)
            
            # 发布错误事件
            self._publish_event("db_operation_error", {
                "operation": "refresh_database_stats",
                "error": str(e),
                "message": error_msg
            })
            
            raise
    
    def close(self) -> None:
        """
        关闭数据库连接
        """
        try:
            self.client.close()
            if self.async_client:
                self.async_client.close()
            self.logger.info("已关闭所有数据库连接")
            
            # 发布断开连接事件
            self._publish_event("db_disconnected", {
                "message": "数据库连接已关闭"
            })
            
        except Exception as e:
            self.logger.error(f"关闭数据库连接失败: {e}")
            raise
    
    # 异步方法
    async def insert_file_info_async(self, file_info: Dict[str, Any]) -> str:
        """
        异步插入文件信息到数据库

        参数:
            file_info: 文件信息字典

        返回:
            插入文档的ID
        """
        if not self._ensure_connected("异步插入文件信息"):
            raise ConnectionError("数据库连接失败")

        try:
            # 记录开始日志
            file_path = file_info.get('path', 'unknown')
            self.logger.debug(f"开始异步插入文件信息: {file_path}")

            # 处理日期时间字段
            file_info = self._process_datetime_fields(file_info)
            
            # 使用路径作为唯一标识，如果存在则更新
            result = await self.async_collection.update_one(
                {"path": file_info["path"]},
                {"$set": file_info},
                upsert=True
            )
            
            if result.upserted_id:
                self.logger.info(f"异步插入文件信息成功: {file_info.get('path', 'unknown')}")
                return str(result.upserted_id)
            else:
                self.logger.info(f"异步更新文件信息成功: {file_info.get('path', 'unknown')}")
                # 获取更新的文档ID
                doc = await self.async_collection.find_one({"path": file_info["path"]})
                if doc is not None:
                    return str(doc["_id"])
                else:
                    self.logger.error(f"未找到插入/更新后的文档: {file_info.get('path', 'unknown')}")
                    return ""
        except Exception as e:
            self.logger.error(f"异步插入文件信息失败: {e}")
            raise
    
    async def find_files_async(self, query: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        异步查询文件信息
        
        参数:
            query: 查询条件
            
        返回:
            匹配的文件信息列表
        """
        try:
            if query is None:
                query = {}
            # 如果按path查找，做标准化（但不处理MongoDB查询操作符）
            if "path" in query and isinstance(query["path"], str):
                query["path"] = _normalize_path(query["path"])
            cursor = self.async_collection.find(query)
            results = await cursor.to_list(length=None)
            self.logger.info(f"异步查询文件信息成功，找到 {len(results)} 条记录")
            return results
        except Exception as e:
            self.logger.error(f"异步查询文件信息失败: {e}")
            raise
    
    async def update_file_info_async(self, file_id: str, update_data: Dict[str, Any]) -> int:
        """
        异步更新文件信息
        
        参数:
            file_id: 文件ID
            update_data: 更新数据
            
        返回:
            更新的文档数量
        """
        try:
            # 处理日期时间字段
            update_data = self._process_datetime_fields(update_data)
            
            result = await self.async_collection.update_one(
                {"_id": ObjectId(file_id)},
                {"$set": update_data}
            )
            self.logger.info(f"异步更新文件信息成功，文件ID: {file_id}, 更新数量: {result.modified_count}")
            return result.modified_count
        except Exception as e:
            self.logger.error(f"异步更新文件信息失败，文件ID: {file_id}, 错误: {e}")
            raise
    
    async def delete_file_info_async(self, file_id: str) -> int:
        """
        异步删除文件信息
        
        参数:
            file_id: 文件ID
            
        返回:
            删除的文档数量
        """
        try:
            result = await self.async_collection.delete_one({"_id": ObjectId(file_id)})
            self.logger.info(f"异步删除文件信息成功，文件ID: {file_id}, 删除数量: {result.deleted_count}")
            return result.deleted_count
        except Exception as e:
            self.logger.error(f"异步删除文件信息失败，文件ID: {file_id}, 错误: {e}")
            raise
    
    async def batch_insert_file_info_async(self, file_info_list: List[Dict[str, Any]], progress_callback: Optional[Callable] = None, interrupt_event: Optional[asyncio.Event] = None) -> List[str]:
        """
        增强的异步批量插入文件信息到数据库

        参数:
            file_info_list: 文件信息字典列表
            progress_callback: 进度回调函数
            interrupt_event: 中断事件，用于支持任务中断

        返回:
            List[str]: 插入文档的ID列表
        """
        if not self._ensure_connected("异步批量插入文件信息"):
            raise ConnectionError("数据库连接失败")

        # 生成任务ID用于中断管理
        task_id = f"db_batch_insert_{int(time.time() * 1000)}"
        interrupt_manager = get_interrupt_manager()

        # 如果传入了外部中断事件，检查状态
        if interrupt_event and interrupt_event.is_set():
            self.logger.info("异步批量插入任务在开始前被中断")
            raise asyncio.CancelledError("异步批量插入任务被外部中断")

        try:
            if not file_info_list:
                return []

            # 记录开始日志
            self.logger.info(f"开始异步批量插入 {len(file_info_list)} 个文件信息")
            start_time = time.time()

            # 高性能批量处理（优化版本）
            processed_list = []
            batch_size = 50  # 批量处理大小

            for batch_start in range(0, len(file_info_list), batch_size):
                # 检查中断信号
                if interrupt_event and interrupt_event.is_set():
                    self.logger.info(f"异步批量插入任务在处理批次 {batch_start//batch_size + 1} 时被中断")
                    raise asyncio.CancelledError("异步批量插入任务被外部中断")

                batch_end = min(batch_start + batch_size, len(file_info_list))
                batch = file_info_list[batch_start:batch_end]

                # 批量处理当前批次
                for i, info in enumerate(batch):
                    try:
                        # 快速验证和修复
                        validated_info = await self._validate_and_fix_file_info_async(info)
                        processed_list.append(self._process_datetime_fields(validated_info))
                    except Exception as e:
                        # 减少日志频率，只记录严重错误
                        if i % 10 == 0:  # 每10个错误记录一次
                            self.logger.warning(f"跳过无效的文件信息 (批次 {batch_start//batch_size + 1}, 索引 {i}): {e}")
                        continue

                # 每个批次后让出控制权
                await asyncio.sleep(0)

            # 检查中断信号
            if interrupt_event and interrupt_event.is_set():
                self.logger.info("异步批量插入任务在准备操作时被中断")
                raise asyncio.CancelledError("异步批量插入任务被外部中断")

            # 使用批量操作
            operations = []
            for i, info in enumerate(processed_list):
                # 每处理100个操作检查一次中断
                if i % 100 == 0 and interrupt_event and interrupt_event.is_set():
                    self.logger.info(f"异步批量插入任务在准备第{i}个操作时被中断")
                    raise asyncio.CancelledError("异步批量插入任务被外部中断")

                # 使用路径作为唯一标识，如果存在则更新
                operations.append(
                    UpdateOne(
                        {"path": info["path"]},
                        {"$set": info},
                        upsert=True
                    )
                )

            # 检查中断信号
            if interrupt_event and interrupt_event.is_set():
                self.logger.info("异步批量插入任务在执行数据库操作前被中断")
                raise asyncio.CancelledError("异步批量插入任务被外部中断")

            result = await self.async_collection.bulk_write(operations, ordered=False)

            # 检查中断信号
            if interrupt_event and interrupt_event.is_set():
                self.logger.info("异步批量插入任务在获取文档ID前被中断")
                raise asyncio.CancelledError("异步批量插入任务被外部中断")

            # 获取插入的文档ID
            inserted_ids = []
            for i, info in enumerate(processed_list):
                # 每查询100个文档检查一次中断
                if i % 100 == 0 and interrupt_event and interrupt_event.is_set():
                    self.logger.info(f"异步批量插入任务在查询第{i}个文档ID时被中断")
                    raise asyncio.CancelledError("异步批量插入任务被外部中断")

                doc = await self.async_collection.find_one({"path": info["path"]})
                if doc:
                    inserted_ids.append(str(doc["_id"]))

            # 计算耗时
            elapsed_time = time.time() - start_time
            self.logger.info(f"异步批量插入文件信息成功，插入/更新数量: {len(inserted_ids)}，耗时: {elapsed_time:.2f}秒")
            return inserted_ids
        except asyncio.CancelledError:
            # 重新抛出取消异常，不记录为错误
            raise
        except Exception as e:
            self.logger.error(f"异步批量插入文件信息失败: {e}")
            raise

    def _validate_and_fix_file_info(self, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证和修复文件信息字典，确保字段映射正确

        参数:
            file_info: 原始文件信息字典

        返回:
            修复后的文件信息字典

        异常:
            ValueError: 当必需字段缺失时
        """
        # 创建修复后的字典
        fixed_info = file_info.copy()

        # 修复字段映射问题：file_path -> path
        if 'file_path' in fixed_info and 'path' not in fixed_info:
            fixed_info['path'] = fixed_info.pop('file_path')
            self.logger.debug("修复字段映射: file_path -> path")

        # 验证必需字段
        required_fields = ['path', 'name', 'size']
        for field in required_fields:
            if field not in fixed_info or fixed_info[field] is None:
                raise ValueError(f"缺少必需字段: {field}")

        # 验证路径字段
        path = fixed_info['path']
        if not isinstance(path, str) or not path.strip():
            raise ValueError(f"无效的路径字段: {path}")

        # 标准化路径
        try:
            fixed_info['path'] = _normalize_path(path)
        except Exception as e:
            self.logger.warning(f"路径标准化失败 {path}: {e}")
            # 如果标准化失败，使用原始路径但进行基本清理
            fixed_info['path'] = path.replace('\\', '/').strip()

        # 确保必需字段有默认值
        if 'created_time' not in fixed_info:
            fixed_info['created_time'] = fixed_info.get('modified_time', time.time())
        if 'modified_time' not in fixed_info:
            fixed_info['modified_time'] = fixed_info.get('created_time', time.time())
        if 'extension' not in fixed_info:
            fixed_info['extension'] = os.path.splitext(fixed_info['path'])[1].lower()
        if 'is_video' not in fixed_info:
            fixed_info['is_video'] = fixed_info['extension'] in ['.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm']
        if 'hash' not in fixed_info:
            fixed_info['hash'] = None

        # 生成file_id如果不存在
        if not fixed_info.get("file_id"):
            fixed_info["file_id"] = self.generate_file_id()

        return fixed_info

    async def _validate_and_fix_file_info_async(self, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        异步版本的数据验证和修复方法，优化性能

        参数:
            file_info: 原始文件信息字典

        返回:
            修复后的文件信息字典

        异常:
            ValueError: 当必需字段缺失时
        """
        # 每次处理前让出控制权
        await asyncio.sleep(0)

        # 创建修复后的字典（浅拷贝，提高性能）
        fixed_info = file_info.copy()

        # 修复字段映射问题：file_path -> path（快速检查）
        if 'file_path' in fixed_info and 'path' not in fixed_info:
            fixed_info['path'] = fixed_info.pop('file_path')

        # 快速验证必需字段
        path = fixed_info.get('path')
        if not path or not isinstance(path, str) or not path.strip():
            raise ValueError(f"无效的路径字段: {path}")

        name = fixed_info.get('name')
        if not name:
            # 从路径推导文件名
            fixed_info['name'] = os.path.basename(path)

        size = fixed_info.get('size')
        if size is None:
            fixed_info['size'] = 0  # 默认大小

        # 优化的路径标准化（减少异常处理开销）
        if '\\' in path:
            fixed_info['path'] = path.replace('\\', '/').strip()

        # 批量设置默认值（减少字典查找次数）
        current_time = time.time()
        defaults = {
            'created_time': fixed_info.get('modified_time', current_time),
            'modified_time': fixed_info.get('created_time', current_time),
            'hash': None
        }

        # 只设置缺失的字段
        for key, default_value in defaults.items():
            if key not in fixed_info:
                fixed_info[key] = default_value

        # 优化的扩展名和视频检测（缓存结果）
        if 'extension' not in fixed_info:
            extension = os.path.splitext(path)[1].lower()
            fixed_info['extension'] = extension
            # 同时设置is_video，避免重复计算
            if 'is_video' not in fixed_info:
                fixed_info['is_video'] = extension in {'.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm'}
        elif 'is_video' not in fixed_info:
            extension = fixed_info['extension']
            fixed_info['is_video'] = extension in {'.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm'}

        # 生成file_id如果不存在
        if not fixed_info.get("file_id"):
            fixed_info["file_id"] = self.generate_file_id()

        return fixed_info

    async def batch_update_file_info_async(self, update_data_list: List[Dict[str, Any]], progress_callback: Optional[Callable] = None) -> int:
        """
        异步批量更新文件信息
        
        参数:
            update_data_list: 更新数据列表，每个元素包含file_id和update_data
            
        返回:
            更新的文档数量
        """
        try:
            if not update_data_list:
                return 0
            
            operations = []
            for item in update_data_list:
                if "file_id" not in item or "update_data" not in item:
                    continue
                
                file_id = item["file_id"]
                update_data = self._process_datetime_fields(item["update_data"])
                
                operations.append(
                    UpdateOne(
                        {"_id": ObjectId(file_id)},
                        {"$set": update_data}
                    )
                )
            
            if not operations:
                return 0
            
            result = await self.async_collection.bulk_write(operations, ordered=False)
            self.logger.info(f"异步批量更新文件信息成功，更新数量: {result.modified_count}")
            return result.modified_count
        except Exception as e:
            self.logger.error(f"异步批量更新文件信息失败: {e}")
            raise
    
    async def batch_delete_file_info_async(self, file_ids: List[str], progress_callback: Optional[Callable] = None) -> int:
        """
        异步批量删除文件信息
        
        参数:
            file_ids: 文件ID列表
            
        返回:
            删除的文档数量
        """
        try:
            if not file_ids:
                return 0
            
            operations = []
            for file_id in file_ids:
                operations.append(
                    DeleteOne({"_id": ObjectId(file_id)})
                )
            
            result = await self.async_collection.bulk_write(operations, ordered=False)
            self.logger.info(f"异步批量删除文件信息成功，删除数量: {result.deleted_count}")
            return result.deleted_count
        except Exception as e:
            self.logger.error(f"异步批量删除文件信息失败: {e}")
            raise
    
    async def find_duplicate_files_async(self, min_size: Optional[int] = None, exists: bool = True) -> Dict[str, List[Dict[str, Any]]]:
        """
        异步查找重复文件 - 重构版本

        参数:
            min_size: 最小文件大小（字节）
            exists: 是否只查找存在的文件

        返回:
            重复文件组字典，键为文件哈希值，值为文件信息列表
        """
        start_time = time.time()
        try:
            # 使用统一的查询管道管理器
            pipeline = self.query_manager.build_duplicate_files_pipeline(min_size=min_size, exists=exists)

            # 执行异步查询
            cursor = self.async_db[self.async_collection.name].aggregate(pipeline)
            result = await cursor.to_list(length=None)

            duplicates: Dict[str, Any] = {}
            for group in result:
                hash_value = group["_id"]
                duplicates[hash_value] = group["files"]

            # 记录性能指标
            execution_time = time.time() - start_time
            self.query_manager.record_query_metrics(
                query_type="find_duplicate_files_async",
                execution_time=execution_time,
                result_count=len(duplicates)
            )

            return duplicates
        except Exception as e:
            execution_time = time.time() - start_time
            self.query_manager.record_query_metrics(
                query_type="find_duplicate_files_async",
                execution_time=execution_time,
                result_count=0,
                error=str(e)
            )
            self.logger.error(f"异步查找重复文件失败: {e}")
            raise
    
    async def find_files_by_size_range_async(self, min_size: int, max_size: int) -> List[Dict[str, Any]]:
        """
        异步按大小范围查找文件
        
        参数:
            min_size: 最小文件大小（字节）
            max_size: 最大文件大小（字节）
            
        返回:
            匹配的文件信息列表
        """
        try:
            query = {"size": {"$gte": min_size, "$lte": max_size}}
            cursor = self.async_collection.find(query)
            results = await cursor.to_list(length=None)
            self.logger.info(f"异步按大小范围查找文件成功，找到 {len(results)} 条记录")
            return results
        except Exception as e:
            self.logger.error(f"异步按大小范围查找文件失败: {e}")
            raise
    
    async def find_files_by_extension_async(self, extensions: List[str]) -> List[Dict[str, Any]]:
        """
        异步按扩展名查找文件
        
        参数:
            extensions: 扩展名列表
            
        返回:
            匹配的文件信息列表
        """
        try:
            # 确保扩展名格式一致（小写，带点）
            normalized_extensions = []
            for ext in extensions:
                if not ext.startswith("."):
                    ext = "." + ext
                normalized_extensions.append(ext.lower())
            
            query = {"extension": {"$in": normalized_extensions}}
            cursor = self.async_collection.find(query)
            results = await cursor.to_list(length=None)
            self.logger.info(f"异步按扩展名查找文件成功，找到 {len(results)} 条记录")
            return results
        except Exception as e:
            self.logger.error(f"异步按扩展名查找文件失败: {e}")
            raise
    
    async def find_files_by_date_range_async(self, start_date: str, end_date: str, date_field: str = "modified_time") -> List[Dict[str, Any]]:
        """
        异步按日期范围查找文件
        
        参数:
            start_date: 开始日期（ISO格式）
            end_date: 结束日期（ISO格式）
            date_field: 日期字段名称
            
        返回:
            匹配的文件信息列表
        """
        try:
            # 转换日期字符串为datetime对象
            start_datetime = datetime.fromisoformat(start_date)
            end_datetime = datetime.fromisoformat(end_date)
            
            # 构建查询条件
            query = {date_field: {"$gte": start_datetime, "$lte": end_datetime}}
            cursor = self.async_collection.find(query)
            results = await cursor.to_list(length=None)
            self.logger.info(f"异步按日期范围查找文件成功，找到 {len(results)} 条记录")
            return results
        except Exception as e:
            self.logger.error(f"异步按日期范围查找文件失败: {e}")
            raise
    
    async def get_all_files_async(self, interrupt_event: Optional[asyncio.Event] = None) -> List[Dict[str, Any]]:
        """
        异步获取数据库中的所有文件信息

        参数:
            interrupt_event: 中断事件

        返回:
            文件信息列表
        """
        if not self._ensure_connected("异步获取所有文件信息"):
            raise ConnectionError("数据库连接失败")
        
        try:
            self.logger.info("开始异步获取所有文件信息...")
            cursor = self.async_collection.find({})
            files = []
            async for doc in cursor:
                if interrupt_event and interrupt_event.is_set():
                    self.logger.warning("获取所有文件信息任务被中断")
                    raise asyncio.CancelledError("任务被中断")
                files.append(doc)
            
            self.logger.info(f"异步获取所有文件信息成功，共 {len(files)} 条记录")
            return files
        except asyncio.CancelledError:
            self.logger.info("异步获取所有文件任务被取消")
            return []
        except Exception as e:
            self.logger.error(f"异步获取所有文件信息失败: {e}")
            return []

    async def close_async(self) -> None:
        """
        异步关闭数据库连接
        """
        try:
            self.async_client.close()
            self.logger.info("已关闭异步数据库连接")
        except Exception as e:
            self.logger.error(f"关闭异步数据库连接失败: {e}")
            raise
    
    # 辅助方法
    def _process_datetime_fields(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理字典中的日期时间字段
        
        参数:
            data: 包含日期时间字段的字典
            
        返回:
            处理后的字典
        """
        result = data.copy()
        
        # 处理常见的日期时间字段
        datetime_fields = ["modified_time", "created_time", "accessed_time", "scan_time"]
        
        for field in datetime_fields:
            if field in result and isinstance(result[field], str):
                try:
                    result[field] = datetime.fromisoformat(result[field])
                except (ValueError, TypeError):
                    # 如果无法解析，保留原始值
                    pass
        
        return result

    def get_files_from_multiple_directories(self, directories: List[str]) -> List[Dict[str, Any]]:
        """
        从多个目录获取文件信息
        
        参数:
            directories: 目录路径列表
            
        返回:
            文件信息列表
        """
        if not self._ensure_connected("从多个目录获取文件信息"):
            raise ConnectionError("数据库连接失败")
            
        try:
            import re
            import os
            
            all_files = []
            
            for directory in directories:
                # 规范化文件夹路径（统一使用正斜杠，移除末尾斜杠）
                normalized_folder_path = _normalize_path(directory)
                
                # 构建文件夹路径匹配模式
                if normalized_folder_path == '' or normalized_folder_path == '.':
                    # 根目录情况
                    path_pattern = "^[^/]+$|^.+/.+$"  # 匹配根目录文件或任何子目录文件
                else:
                    # 具体文件夹情况
                    path_pattern = f"^{re.escape(normalized_folder_path)}/"
                
                # 查询该文件夹下的文件
                query = {"path": {"$regex": path_pattern}}
                files = list(self.collection.find(query))
                
                self.logger.info(f"从目录 {directory} 获取到 {len(files)} 个文件")
                all_files.extend(files)
            
            self.logger.info(f"从 {len(directories)} 个目录共获取到 {len(all_files)} 个文件")
            return all_files
            
        except Exception as e:
            self.logger.error(f"从多个目录获取文件信息失败: {directories}, 错误: {e}")
            return []

    def get_all_files(self) -> List[Dict[str, Any]]:
        """
        获取数据库中的所有文件信息
        
        返回:
            文件信息列表
        """
        if not self._ensure_connected("获取所有文件信息"):
            raise ConnectionError("数据库连接失败")
            
        try:
            files = list(self.collection.find({}))
            self.logger.info(f"获取所有文件信息成功，共 {len(files)} 条记录")
            return files
        except Exception as e:
            self.logger.error(f"获取所有文件信息失败: {e}")
            return []

    def get_global_stats(self) -> dict:
        """
        统计文件总数、文件总大小、视频文件数、视频文件总大小、白名单文件数、垃圾文件数
        返回：
            dict: {
                'file_count': int,
                'total_size': int,
                'video_count': int,
                'video_total_size': int,
                'whitelist_count': int,
                'junk_count': int
            }
        """
        try:
            pipeline = [
                {
                    "$facet": {
                        "all_files": [
                            {"$group": {
                                "_id": None,
                                "file_count": {"$sum": 1},
                                "total_size": {"$sum": "$size"}
                            }}
                        ],
                        "video_files": [
                            {"$match": {"extension": {"$in": [".mp4", ".avi", ".mkv", ".mov", ".wmv", ".flv", ".webm"]}}},
                            {"$group": {
                                "_id": None,
                                "video_count": {"$sum": 1},
                                "video_total_size": {"$sum": "$size"}
                            }}
                        ],
                        "whitelist_files": [
                            {"$match": {"is_whitelist": True}},
                            {"$count": "whitelist_count"}
                        ],
                        "junk_files": [
                            {"$match": {"is_junk": True}},
                            {"$count": "junk_count"}
                        ]
                    }
                }
            ]
            result = list(self.collection.aggregate(pipeline))[0]
            stats = {
                "file_count": result["all_files"][0]["file_count"] if result["all_files"] else 0,
                "total_size": result["all_files"][0]["total_size"] if result["all_files"] else 0,
                "video_count": result["video_files"][0]["video_count"] if result["video_files"] else 0,
                "video_total_size": result["video_files"][0]["video_total_size"] if result["video_files"] else 0,
                "whitelist_count": result["whitelist_files"][0]["whitelist_count"] if result["whitelist_files"] else 0,
                "junk_count": result["junk_files"][0]["junk_count"] if result["junk_files"] else 0,
            }
            return stats
        except Exception as e:
            return {
                "file_count": 0,
                "total_size": 0,
                "video_count": 0,
                "video_total_size": 0,
                "whitelist_count": 0,
                "junk_count": 0
            }

    # ==================== 批量操作功能 (合并自DatabaseOperations) ====================

    def get_files_without_hash(self, limit: int = 500) -> List[Dict[str, Any]]:
        """
        获取没有哈希值的文件（合并自DatabaseOperations）

        Args:
            limit: 限制数量

        Returns:
            List[Dict[str, Any]]: 文件列表
        """
        try:
            query = {"$or": [{"hash": {"$exists": False}}, {"hash": None}, {"hash": ""}]}
            files = list(self.collection.find(query).limit(limit))
            logger.info(f"获取到 {len(files)} 个没有哈希值的文件")
            return files
        except Exception as e:
            logger.error(f"获取没有哈希值的文件失败: {e}")
            return []

    def update_file_hashes_batch(self, file_hash_pairs: List[tuple]) -> None:
        """
        批量更新文件哈希值（合并自DatabaseOperations）

        Args:
            file_hash_pairs: (文件路径, 哈希值) 元组列表
        """
        try:
            if not file_hash_pairs:
                logger.warning("没有提供文件哈希对")
                return

            bulk_operations = []
            for file_path, hash_value in file_hash_pairs:
                if not hash_value:
                    continue

                # 使用文件路径更新，支持多种路径字段
                bulk_operations.append(
                    UpdateOne(
                        {"$or": [{"file_path": file_path}, {"path": file_path}]},
                        {"$set": {"hash": hash_value}}
                    )
                )

            if bulk_operations:
                result = self.collection.bulk_write(bulk_operations)
                logger.info(f"批量更新哈希值结果: matched={result.matched_count}, modified={result.modified_count}")
            else:
                logger.warning("没有有效的批量操作")
        except Exception as e:
            logger.error(f"批量更新文件哈希值失败: {e}")
            import traceback
            traceback.print_exc()

    def find_duplicates_with_timeout(self, timeout_ms: int = 30000) -> List[Dict[str, Any]]:
        """
        带超时的查找重复文件（合并自DatabaseOperations） - 重构版本

        Args:
            timeout_ms: 超时时间（毫秒）

        Returns:
            List[Dict[str, Any]]: 重复文件列表
        """
        start_time = time.time()
        try:
            # 使用统一的查询管道管理器
            pipeline = self.query_manager.build_duplicate_files_pipeline()

            # 设置查询超时
            cursor = self.collection.aggregate(pipeline, maxTimeMS=timeout_ms)
            duplicates = list(cursor)

            # 记录性能指标
            execution_time = time.time() - start_time
            self.query_manager.record_query_metrics(
                query_type="find_duplicates_with_timeout",
                execution_time=execution_time,
                result_count=len(duplicates)
            )

            logger.info(f"找到 {len(duplicates)} 组重复文件")
            return duplicates
        except Exception as e:
            execution_time = time.time() - start_time
            self.query_manager.record_query_metrics(
                query_type="find_duplicates_with_timeout",
                execution_time=execution_time,
                result_count=0,
                error=str(e)
            )
            logger.error(f"查找重复文件失败: {e}")
            return []

    def ensure_connected(self) -> bool:
        """
        确保数据库连接有效（合并自DatabaseOperations）

        Returns:
            bool: 是否连接成功
        """
        try:
            if self.client is None:
                logger.warning("数据库客户端未初始化，尝试重新连接")
                self._connect_with_retry()
                return self.client is not None

            # 测试连接
            self.client.admin.command('ping')
            return True
        except ConnectionFailure:
            logger.warning("数据库连接失败，尝试重新连接")
            try:
                self._connect_with_retry()
                return self.client is not None
            except Exception as e:
                logger.error(f"重新连接失败: {e}")
                return False
        except Exception as e:
            logger.error(f"数据库连接检查异常: {e}")
            return False

    def batch_insert_files(self, file_infos: List[Dict[str, Any]]) -> bool:
        """
        批量插入文件信息（合并自DatabaseOperations）

        Args:
            file_infos: 文件信息列表

        Returns:
            bool: 是否成功
        """
        try:
            if not file_infos:
                logger.warning("没有提供文件信息")
                return False

            if not self.ensure_connected():
                logger.error("数据库连接失败")
                return False

            # 准备批量插入操作
            bulk_operations = [InsertOne(file_info) for file_info in file_infos]

            result = self.collection.bulk_write(bulk_operations)
            logger.info(f"批量插入文件信息成功: inserted={result.inserted_count}")
            return True

        except Exception as e:
            logger.error(f"批量插入文件信息失败: {e}")
            return False

    def batch_update_files(self, updates: List[Dict[str, Any]]) -> bool:
        """
        批量更新文件信息（合并自DatabaseOperations）

        Args:
            updates: 更新操作列表，每个元素包含filter和update字段

        Returns:
            bool: 是否成功
        """
        try:
            if not updates:
                logger.warning("没有提供更新操作")
                return False

            if not self.ensure_connected():
                logger.error("数据库连接失败")
                return False

            # 准备批量更新操作
            bulk_operations = []
            for update_op in updates:
                if 'filter' in update_op and 'update' in update_op:
                    bulk_operations.append(
                        UpdateOne(update_op['filter'], update_op['update'])
                    )

            if bulk_operations:
                result = self.collection.bulk_write(bulk_operations)
                logger.info(f"批量更新文件信息成功: matched={result.matched_count}, modified={result.modified_count}")
                return True
            else:
                logger.warning("没有有效的更新操作")
                return False

        except Exception as e:
            logger.error(f"批量更新文件信息失败: {e}")
            return False

    def find_files_by_size_range(self, min_size: Optional[int] = None,
                                max_size: Optional[int] = None,
                                sort_order: str = "desc") -> List[Dict[str, Any]]:
        """
        按大小范围查找文件 - 使用统一查询管道

        Args:
            min_size: 最小文件大小
            max_size: 最大文件大小
            sort_order: 排序顺序 ("asc" 或 "desc")

        Returns:
            List[Dict[str, Any]]: 文件列表
        """
        start_time = time.time()
        try:
            pipeline = self.query_manager.build_files_by_size_pipeline(
                min_size=min_size,
                max_size=max_size,
                sort_order=sort_order
            )

            result = list(self.collection.aggregate(pipeline))

            # 记录性能指标
            execution_time = time.time() - start_time
            self.query_manager.record_query_metrics(
                query_type="find_files_by_size_range",
                execution_time=execution_time,
                result_count=len(result)
            )

            return result
        except Exception as e:
            execution_time = time.time() - start_time
            self.query_manager.record_query_metrics(
                query_type="find_files_by_size_range",
                execution_time=execution_time,
                result_count=0,
                error=str(e)
            )
            self.logger.error(f"按大小范围查找文件失败: {e}")
            return []

    def get_file_statistics(self, group_by: str = "extension") -> List[Dict[str, Any]]:
        """
        获取文件统计信息 - 使用统一查询管道

        Args:
            group_by: 分组字段 ("extension", "size_range", "date_range")

        Returns:
            List[Dict[str, Any]]: 统计信息列表
        """
        start_time = time.time()
        try:
            pipeline = self.query_manager.build_statistics_pipeline(group_by=group_by)

            result = list(self.collection.aggregate(pipeline))

            # 记录性能指标
            execution_time = time.time() - start_time
            self.query_manager.record_query_metrics(
                query_type="get_file_statistics",
                execution_time=execution_time,
                result_count=len(result)
            )

            return result
        except Exception as e:
            execution_time = time.time() - start_time
            self.query_manager.record_query_metrics(
                query_type="get_file_statistics",
                execution_time=execution_time,
                result_count=0,
                error=str(e)
            )
            self.logger.error(f"获取文件统计信息失败: {e}")
            return []

    def get_query_performance_stats(self) -> Dict[str, Any]:
        """
        获取查询性能统计信息

        Returns:
            Dict[str, Any]: 性能统计信息
        """
        return self.query_manager.get_query_statistics()


class DatabaseConnectionPool:
    """
    数据库连接池管理器（合并自DatabaseOperations）
    """

    def __init__(self, connection_string: str, database_name: str, max_connections: int = 10):
        """
        初始化连接池

        Args:
            connection_string: 连接字符串
            database_name: 数据库名称
            max_connections: 最大连接数
        """
        self.connection_string = connection_string
        self.database_name = database_name
        self.max_connections = max_connections
        self.connections = []
        self.in_use = set()
        self.lock = threading.Lock()

        logger.info(f"初始化数据库连接池，最大连接数: {max_connections}")

    def get_connection(self) -> Optional['MongoDBManager']:
        """
        获取数据库连接

        Returns:
            Optional[MongoDBManager]: 数据库管理器对象
        """
        with self.lock:
            # 尝试复用现有连接
            for conn in self.connections:
                if conn not in self.in_use:
                    self.in_use.add(conn)
                    return conn

            # 创建新连接（如果未达到最大连接数）
            if len(self.connections) < self.max_connections:
                try:
                    # 解析连接字符串获取主机和端口
                    if "mongodb://" in self.connection_string:
                        host_port = self.connection_string.replace("mongodb://", "").rstrip("/")
                        if ":" in host_port:
                            host, port = host_port.split(":")
                            port = int(port)
                        else:
                            host = host_port
                            port = 27017
                    else:
                        host = "localhost"
                        port = 27017

                    conn = MongoDBManager(
                        db_name=self.database_name,
                        collection_name="files",
                        host=host,
                        port=port
                    )
                    self.connections.append(conn)
                    self.in_use.add(conn)
                    logger.info(f"创建新的数据库连接，当前连接数: {len(self.connections)}")
                    return conn
                except Exception as e:
                    logger.error(f"创建数据库连接失败: {e}")
                    return None

            # 等待可用连接
            logger.warning("连接池已满，无法创建新连接")
            return None

    def release_connection(self, conn: 'MongoDBManager') -> None:
        """
        释放数据库连接

        Args:
            conn: 数据库管理器对象
        """
        with self.lock:
            if conn in self.in_use:
                self.in_use.remove(conn)
                logger.debug(f"释放数据库连接，当前使用中连接数: {len(self.in_use)}")

    def close_all_connections(self) -> None:
        """
        关闭所有连接
        """
        with self.lock:
            for conn in self.connections:
                try:
                    conn.close()
                except Exception as e:
                    logger.error(f"关闭数据库连接失败: {e}")

            self.connections.clear()
            self.in_use.clear()
            logger.info("已关闭所有数据库连接")
