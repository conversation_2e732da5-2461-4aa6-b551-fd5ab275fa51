#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
重复文件查找模块 - 重构版本

该模块基于高性能检测器重构，统一了重复文件查找的实现：
1. 使用HighPerformanceDuplicateDetector作为核心引擎
2. 支持数据库和本地两种模式
3. 统一的异步接口和进度回调
4. 优化的内存使用和性能

重构特性：
- 消除了重复的哈希计算逻辑
- 统一了文件扫描和重复检测流程
- 保持向后兼容性
- 提升了性能和可维护性

作者: SmartFileManger开发团队
日期: 2025-07-27
版本: 2.0.0 (重构版)
"""

import os
import asyncio
from typing import Dict, Any, List, Optional, Callable

from .high_performance_duplicate_detector import HighPerformanceDuplicateDetector, DuplicateGroup
from .intelligent_hash_calculator import IntelligentHashCalculator
from .progress_tracker import MultiStageProgressTracker
from src.data.db_manager import MongoDBManager
from src.utils.format_utils import _normalize_path
from src.utils.async_manager import get_async_manager
from src.utils.logger import get_logger

logger = get_logger(__name__)


class DuplicateFinder:
    """
    统一的重复文件查找器 - 重构版本
    
    基于HighPerformanceDuplicateDetector重构，提供统一的重复文件查找接口
    支持数据库和本地两种模式，优化了性能和内存使用
    """
    
    def __init__(self, db_manager: Optional[MongoDBManager] = None, use_database: bool = True):
        """
        初始化重复文件查找器
        
        参数:
            db_manager: 数据库管理器
            use_database: 是否使用数据库模式
        """
        self.logger = logger
        self.use_database = use_database and db_manager is not None
        self.db_manager = db_manager
        
        # 使用高性能检测器作为核心引擎
        self.detector = HighPerformanceDuplicateDetector(
            enable_bloom_filter=True,
            max_workers=4
        )
        
        # 使用智能哈希计算器
        self.hash_calculator = IntelligentHashCalculator()
        
        # 进度跟踪器
        self.progress_tracker = MultiStageProgressTracker([
            {"name": "扫描文件", "weight": 1.0},
            {"name": "检测重复文件", "weight": 4.0}
        ])
        
        self._interrupted_event = asyncio.Event()
        self.async_manager = get_async_manager()
        
        self.logger.info(f"重复文件查找器初始化完成，use_database={self.use_database}")

    def interrupt(self):
        """中断查找过程"""
        self._interrupted_event.set()
        self.logger.info("重复文件查找被中断")

    def find_duplicates(self, directories: List[str], min_size: int = 1024,
                       callback: Optional[Callable[[Dict[str, Any]], None]] = None) -> str:
        """
        同步查找重复文件（返回任务ID）

        参数:
            directories: 要扫描的目录列表
            min_size: 最小文件大小（字节）
            callback: 进度回调函数

        返回:
            str: 任务ID
        """
        import asyncio
        import time

        # 创建任务ID
        task_id = f"duplicate_find_{int(time.time())}"

        # 在后台运行异步任务
        def run_async():
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                result = loop.run_until_complete(
                    self.find_duplicates_async(directories, min_size, callback)
                )
                loop.close()
                return result
            except Exception as e:
                self.logger.error(f"同步查找重复文件失败: {e}")
                return {}

        # 启动后台线程
        import threading
        thread = threading.Thread(target=run_async)
        thread.daemon = True
        thread.start()

        return task_id

    async def find_duplicates_async(self, directories: List[str], min_size: int = 1024,
                                  callback: Optional[Callable[[Dict[str, Any]], None]] = None) -> Dict[str, Any]:
        """
        异步查找重复文件 - 重构版本
        
        参数:
            directories: 要扫描的目录列表
            min_size: 最小文件大小（字节）
            callback: 进度回调函数
            
        返回:
            重复文件查找结果
        """
        self._interrupted_event.clear()
        
        try:
            self.logger.info(f"开始重复文件查找，目录数: {len(directories)}, 最小大小: {min_size}")
            
            # 第一阶段：扫描文件
            await self.progress_tracker.update_stage_async(0, 0)
            if callback:
                callback(self.progress_tracker.get_progress_info())
            
            files = await self._scan_directories_optimized(directories, min_size)
            if not files:
                self.logger.warning("未找到符合条件的文件")
                return {"status": "no_files", "message": "未找到符合条件的文件"}
            
            self.logger.info(f"文件扫描完成，找到 {len(files)} 个文件")
            await self.progress_tracker.update_stage_async(0, 100)
            if callback:
                callback(self.progress_tracker.get_progress_info())

            # 第二阶段：使用高性能检测器查找重复文件
            self.logger.info("开始高性能重复文件检测")
            
            # 创建同步进度回调包装器（修复协程兼容性问题）
            def progress_wrapper(progress: float, message: str = ""):
                # 使用同步方式更新进度跟踪器
                try:
                    # 直接更新进度，避免异步调用
                    self.progress_tracker.update_stage_sync(1, progress)
                    if callback:
                        progress_info = self.progress_tracker.get_progress_info_sync()
                        progress_info["message"] = message
                        callback(progress_info)
                except Exception as e:
                    self.logger.warning(f"进度回调执行失败: {e}")

            # 使用高性能检测器
            duplicate_groups = await self.detector.detect_duplicates(
                files,
                progress_callback=progress_wrapper,
                interrupt_event=self._interrupted_event
            )
            
            # 如果使用数据库模式，更新数据库中的哈希值
            if self.use_database and self.db_manager and duplicate_groups:
                await self._update_database_hashes(duplicate_groups)
            
            # 转换结果格式以保持向后兼容性
            result_duplicates = self._convert_to_legacy_format(duplicate_groups)
            
            self.logger.info(f"重复文件查找完成，找到 {len(result_duplicates)} 组重复文件")
            
            final_result = {
                "status": "completed",
                "duplicates": result_duplicates,
                "total_files": len(files),
                "duplicate_groups": len(result_duplicates),
                "progress": 100.0
            }
            
            if callback:
                callback(final_result)
                
            return final_result
            
        except asyncio.CancelledError:
            self.logger.warning("重复文件查找任务被取消")
            return {"status": "cancelled", "message": "任务被用户取消"}
            
        except Exception as e:
            self.logger.error(f"重复文件查找发生错误: {e}", exc_info=True)
            return {"status": "error", "error": str(e), "message": f"查找过程中发生错误: {e}"}

    async def _scan_directories_optimized(self, directories: List[str], min_size: int) -> List[str]:
        """
        优化的目录扫描方法
        
        参数:
            directories: 目录列表
            min_size: 最小文件大小
            
        返回:
            文件路径列表
        """
        files = []
        total_dirs = len(directories)
        
        for i, directory in enumerate(directories):
            if self._interrupted_event.is_set():
                break
                
            try:
                self.logger.debug(f"扫描目录 {i+1}/{total_dirs}: {directory}")
                
                # 使用os.walk扫描目录
                for root, _, filenames in os.walk(directory):
                    if self._interrupted_event.is_set():
                        break
                        
                    for filename in filenames:
                        if self._interrupted_event.is_set():
                            break
                            
                        file_path = _normalize_path(os.path.join(root, filename))
                        
                        try:
                            file_size = os.path.getsize(file_path)
                            if file_size >= min_size:
                                files.append(file_path)
                                
                            # 定期让出控制权
                            if len(files) % 1000 == 0:
                                await asyncio.sleep(0)
                                
                        except (OSError, IOError):
                            # 忽略无法访问的文件
                            continue
                            
            except Exception as e:
                self.logger.warning(f"扫描目录 {directory} 时出错: {e}")
                continue
        
        return files

    async def _update_database_hashes(self, duplicate_groups: Dict[str, DuplicateGroup]):
        """
        更新数据库中的哈希值
        
        参数:
            duplicate_groups: 重复文件组
        """
        if not self.db_manager:
            return
            
        try:
            self.logger.info("开始更新数据库中的哈希值")
            
            # 收集所有文件的哈希值
            hash_pairs = []
            for hash_value, group in duplicate_groups.items():
                for file_path in group.files:
                    hash_pairs.append((file_path, hash_value))
            
            if hash_pairs:
                # 批量更新数据库
                self.db_manager.update_file_hashes_batch(hash_pairs)
                self.logger.info(f"数据库哈希值更新完成，更新了 {len(hash_pairs)} 个文件")
                
        except Exception as e:
            self.logger.error(f"更新数据库哈希值失败: {e}")

    def _convert_to_legacy_format(self, duplicate_groups: Dict[str, DuplicateGroup]) -> Dict[str, List[str]]:
        """
        转换为旧格式以保持向后兼容性
        
        参数:
            duplicate_groups: 新格式的重复文件组
            
        返回:
            旧格式的重复文件组
        """
        legacy_format = {}
        
        for hash_value, group in duplicate_groups.items():
            if len(group.files) > 1:  # 只包含真正的重复文件
                legacy_format[hash_value] = group.files
                
        return legacy_format

    async def cleanup(self):
        """清理资源"""
        try:
            if hasattr(self.detector, 'shutdown'):
                await self.detector.shutdown()
            self.logger.info("重复文件查找器资源清理完成")
        except Exception as e:
            self.logger.error(f"清理资源时出错: {e}")


# 向后兼容性函数
async def find_duplicates_intelligent(directories: List[str], min_size: int = 1024,
                                    progress_callback: Optional[Callable] = None) -> Dict[str, List[str]]:
    """
    向后兼容的智能重复文件查找函数
    
    参数:
        directories: 目录列表
        min_size: 最小文件大小
        progress_callback: 进度回调函数
        
    返回:
        重复文件组字典（哈希值 -> 文件路径列表）
    """
    finder = DuplicateFinder(use_database=False)
    
    try:
        result = await finder.find_duplicates_async(
            directories=directories,
            min_size=min_size,
            callback=progress_callback
        )
        
        if result.get("status") == "completed":
            return result.get("duplicates", {})
        else:
            return {}
            
    finally:
        await finder.cleanup()
