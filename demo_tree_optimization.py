#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
树优化功能演示脚本

该脚本演示如何使用树优化管理器来分析和优化目录结构，
提供简单易懂的使用示例。

作者: SmartFileManger开发团队
日期: 2024-01-20
版本: 1.0.0
"""

import asyncio
import logging
import os
import sys
from pathlib import Path
from typing import List, Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入必要的模块
from src.core.tree_optimization_manager import TreeOptimizationManager
from src.data.db_manager import MongoDBManager
from src.utils.logger import get_logger

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = get_logger(__name__)


class TreeOptimizationDemo:
    """
    树优化功能演示类
    """
    
    def __init__(self):
        """初始化演示环境"""
        self.db_manager = None
        self.tree_optimizer = None
        
    async def initialize(self):
        """
        初始化组件
        """
        try:
            logger.info("初始化树优化演示环境...")
            
            # 初始化数据库管理器
            self.db_manager = MongoDBManager()
            await self.db_manager.connect()
            logger.info("✅ 数据库连接成功")
            
            # 初始化树优化管理器
            self.tree_optimizer = TreeOptimizationManager(self.db_manager)
            logger.info("✅ 树优化管理器初始化完成")
            
        except Exception as e:
            logger.error(f"❌ 初始化失败: {e}")
            raise
    
    async def demo_analyze_directory(self, directory_path: str):
        """
        演示目录分析功能
        
        参数:
            directory_path: 要分析的目录路径
        """
        try:
            logger.info(f"\n📁 开始分析目录: {directory_path}")
            
            # 检查目录是否存在
            if not os.path.exists(directory_path):
                logger.warning(f"⚠️ 目录不存在: {directory_path}")
                return
            
            # 执行目录树分析
            analysis_result = await self.tree_optimizer.analyze_directory_tree(directory_path)
            
            if analysis_result:
                logger.info("✅ 目录分析完成")
                self._display_analysis_result(analysis_result)
            else:
                logger.warning("⚠️ 分析结果为空")
                
        except Exception as e:
            logger.error(f"❌ 目录分析失败: {e}")
    
    def _display_analysis_result(self, result: Dict[str, Any]):
        """
        显示分析结果
        
        参数:
            result: 分析结果字典
        """
        print("\n" + "="*60)
        print("📊 目录树分析结果")
        print("="*60)
        
        # 显示基本信息
        if 'root_path' in result:
            print(f"🏠 根目录: {result['root_path']}")
        
        if 'total_files' in result:
            print(f"📄 总文件数: {result['total_files']}")
        
        if 'total_directories' in result:
            print(f"📁 总目录数: {result['total_directories']}")
        
        if 'total_size' in result:
            size_mb = result['total_size'] / (1024 * 1024)
            print(f"💾 总大小: {size_mb:.2f} MB")
        
        if 'optimization_score' in result:
            print(f"⭐ 优化评分: {result['optimization_score']:.2f}/100")
        
        # 显示优化建议
        if 'optimization_suggestions' in result and result['optimization_suggestions']:
            print("\n💡 优化建议:")
            for i, suggestion in enumerate(result['optimization_suggestions'], 1):
                print(f"  {i}. {suggestion}")
        
        # 显示目录结构概览
        if 'tree_structure' in result:
            print("\n🌳 目录结构概览:")
            self._display_tree_structure(result['tree_structure'], indent=0)
        
        print("="*60)
    
    def _display_tree_structure(self, node: Dict[str, Any], indent: int = 0):
        """
        递归显示目录树结构
        
        参数:
            node: 树节点
            indent: 缩进级别
        """
        prefix = "  " * indent
        
        # 显示当前节点
        if node.get('is_directory', False):
            icon = "📁"
            name = node.get('name', 'Unknown')
            file_count = node.get('file_count', 0)
            print(f"{prefix}{icon} {name}/ ({file_count} 文件)")
        else:
            icon = "📄"
            name = node.get('name', 'Unknown')
            size = node.get('size', 0)
            size_kb = size / 1024 if size > 0 else 0
            print(f"{prefix}{icon} {name} ({size_kb:.1f} KB)")
        
        # 递归显示子节点（限制显示深度）
        if indent < 3 and 'children' in node:
            for child in node['children'][:5]:  # 只显示前5个子项
                self._display_tree_structure(child, indent + 1)
            
            # 如果有更多子项，显示省略号
            if len(node['children']) > 5:
                print(f"{prefix}  ... 还有 {len(node['children']) - 5} 个项目")
    
    async def demo_batch_analysis(self, directories: List[str]):
        """
        演示批量目录分析
        
        参数:
            directories: 目录路径列表
        """
        logger.info(f"\n🔄 开始批量分析 {len(directories)} 个目录...")
        
        results = []
        for i, directory in enumerate(directories, 1):
            logger.info(f"\n进度: {i}/{len(directories)} - 分析目录: {directory}")
            
            if os.path.exists(directory):
                try:
                    result = await self.tree_optimizer.analyze_directory_tree(directory)
                    if result:
                        results.append(result)
                        logger.info(f"✅ 目录 {directory} 分析完成")
                    else:
                        logger.warning(f"⚠️ 目录 {directory} 分析结果为空")
                except Exception as e:
                    logger.error(f"❌ 目录 {directory} 分析失败: {e}")
            else:
                logger.warning(f"⚠️ 目录不存在: {directory}")
        
        # 显示批量分析汇总
        self._display_batch_summary(results)
        
        return results
    
    def _display_batch_summary(self, results: List[Dict[str, Any]]):
        """
        显示批量分析汇总
        
        参数:
            results: 分析结果列表
        """
        if not results:
            logger.warning("⚠️ 没有有效的分析结果")
            return
        
        print("\n" + "="*60)
        print("📈 批量分析汇总报告")
        print("="*60)
        
        total_files = sum(r.get('total_files', 0) for r in results)
        total_directories = sum(r.get('total_directories', 0) for r in results)
        total_size = sum(r.get('total_size', 0) for r in results)
        avg_score = sum(r.get('optimization_score', 0) for r in results) / len(results)
        
        print(f"📊 分析目录数: {len(results)}")
        print(f"📄 总文件数: {total_files:,}")
        print(f"📁 总目录数: {total_directories:,}")
        print(f"💾 总大小: {total_size / (1024 * 1024 * 1024):.2f} GB")
        print(f"⭐ 平均优化评分: {avg_score:.2f}/100")
        
        # 找出评分最高和最低的目录
        best_dir = max(results, key=lambda x: x.get('optimization_score', 0))
        worst_dir = min(results, key=lambda x: x.get('optimization_score', 0))
        
        print(f"\n🏆 最优目录: {best_dir.get('root_path', 'Unknown')} (评分: {best_dir.get('optimization_score', 0):.2f})")
        print(f"⚠️ 待优化目录: {worst_dir.get('root_path', 'Unknown')} (评分: {worst_dir.get('optimization_score', 0):.2f})")
        
        print("="*60)
    
    async def cleanup(self):
        """
        清理资源
        """
        try:
            if self.db_manager:
                await self.db_manager.close()
                logger.info("✅ 数据库连接已关闭")
        except Exception as e:
            logger.error(f"❌ 清理资源失败: {e}")


async def main():
    """
    主演示函数
    """
    demo = TreeOptimizationDemo()
    
    try:
        # 初始化
        await demo.initialize()
        
        print("\n🎯 树优化功能演示开始")
        print("="*60)
        
        # 演示1: 分析单个目录
        print("\n📋 演示1: 单目录分析")
        test_directories = [
            "C:/Users",  # Windows用户目录
            "D:/",       # D盘根目录
            "./src",     # 项目源码目录
            "./docs"     # 项目文档目录
        ]
        
        # 选择一个存在的目录进行演示
        demo_dir = None
        for directory in test_directories:
            if os.path.exists(directory):
                demo_dir = directory
                break
        
        if demo_dir:
            await demo.demo_analyze_directory(demo_dir)
        else:
            logger.warning("⚠️ 未找到可用的演示目录")
        
        # 演示2: 批量分析
        print("\n📋 演示2: 批量目录分析")
        existing_dirs = [d for d in test_directories if os.path.exists(d)]
        
        if existing_dirs:
            await demo.demo_batch_analysis(existing_dirs[:3])  # 限制分析前3个目录
        else:
            logger.warning("⚠️ 未找到可用的目录进行批量分析")
        
        print("\n🎉 演示完成！")
        
    except Exception as e:
        logger.error(f"💥 演示过程中发生错误: {e}")
    finally:
        await demo.cleanup()


if __name__ == "__main__":
    print("🚀 启动树优化功能演示...")
    asyncio.run(main())