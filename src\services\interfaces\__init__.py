#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
服务接口包

导出所有业务服务接口，提供统一的接口访问点
遵循RULE-001: 模块职责单一原则
"""

# 基础服务接口
from .base_service import (
    IBaseService, ITaskService, IValidationService, IConfigurableService,
    BaseServiceImpl, ServiceStatus
)

# 文件扫描服务接口
from .file_scan_service import (
    IFileScanService, IFileIndexService, IFileHashService,
    ScanPriority
)

# 重复文件服务接口
from .duplicate_service import (
    IDuplicateDetectionService, IDuplicateActionService, IDuplicateAnalysisService,
    DuplicateResolutionStrategy
)

# 重命名服务接口
from .rename_service import (
    IRenameService, IRenameRuleService, IRenamePatternService,
    RenameConflictResolution
)

# 文件操作服务接口
from .file_operations_service import (
    IFileOperationsService,
)

__all__ = [
    # 基础服务接口
    "IBaseService",
    "ITaskService",
    "IValidationService",
    "IConfigurableService",
    "BaseServiceImpl",
    "ServiceStatus",

    # 文件扫描服务接口
    "IFileScanService",
    "IFileIndexService",
    "IFileHashService",
    "ScanPriority",

    # 重复文件服务接口
    "IDuplicateDetectionService",
    "IDuplicateActionService",
    "IDuplicateAnalysisService",
    "DuplicateResolutionStrategy",

    # 重命名服务接口
    "IRenameService",
    "IRenameRuleService",
    "IRenamePatternService",
    "RenameConflictResolution",

    # 文件操作服务接口
    "IFileOperationsService",
]
