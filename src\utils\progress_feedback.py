#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
增强的进度反馈系统
提供更详细的任务状态和中断反馈
"""

import time
import threading
from typing import Optional, Callable, Dict, Any
from dataclasses import dataclass, field
from enum import Enum
from src.utils.logger import get_logger

logger = get_logger(__name__)

class TaskState(Enum):
    """任务状态枚举"""
    NOT_STARTED = "not_started"
    RUNNING = "running"
    PAUSED = "paused"
    INTERRUPTING = "interrupting"
    INTERRUPTED = "interrupted"
    COMPLETED = "completed"
    FAILED = "failed"

@dataclass
class ProgressInfo:
    """进度信息"""
    current: int = 0
    total: int = 0
    percentage: float = 0.0
    rate: float = 0.0  # 处理速率（项目/秒）
    eta: float = 0.0   # 预计剩余时间（秒）
    elapsed: float = 0.0
    status_message: str = ""
    details: str = ""
    state: TaskState = TaskState.NOT_STARTED
    last_update: float = field(default_factory=time.time)

class ProgressTracker:
    """增强的进度跟踪器"""
    
    def __init__(self, task_id: str, total_items: int = 0, update_callback: Optional[Callable] = None):
        self.task_id = task_id
        self.total_items = total_items
        self.update_callback = update_callback
        self.logger = get_logger(f"{self.__class__.__name__}_{task_id}")
        
        # 进度信息
        self.progress = ProgressInfo(total=total_items)
        self.start_time = time.time()
        self.last_update_time = self.start_time
        
        # 速率计算
        self._rate_samples = []
        self._max_samples = 10
        
        # 线程安全
        self._lock = threading.Lock()
        
        # 中断状态
        self._is_interrupting = False
        
    def start(self, status_message: str = "开始处理..."):
        """开始任务"""
        with self._lock:
            self.progress.state = TaskState.RUNNING
            self.progress.status_message = status_message
            self.progress.last_update = time.time()
            self.start_time = self.progress.last_update
            
        self._notify_update()
        self.logger.info(f"任务开始: {status_message}")
    
    def update(self, current: int, status_message: str = "", details: str = ""):
        """更新进度"""
        with self._lock:
            current_time = time.time()
            
            # 更新基本信息
            self.progress.current = current
            self.progress.elapsed = current_time - self.start_time
            self.progress.last_update = current_time
            
            if status_message:
                self.progress.status_message = status_message
            if details:
                self.progress.details = details
            
            # 计算百分比
            if self.progress.total > 0:
                self.progress.percentage = min(100.0, (current / self.progress.total) * 100)
            
            # 计算处理速率
            self._update_rate(current, current_time)
            
            # 计算预计剩余时间
            self._update_eta()
            
        self._notify_update()
    
    def _update_rate(self, current: int, current_time: float):
        """更新处理速率"""
        if current > 0:
            time_diff = current_time - self.last_update_time
            if time_diff > 0:
                rate_sample = current / (current_time - self.start_time)
                self._rate_samples.append(rate_sample)
                
                # 保持样本数量限制
                if len(self._rate_samples) > self._max_samples:
                    self._rate_samples.pop(0)
                
                # 计算平均速率
                self.progress.rate = sum(self._rate_samples) / len(self._rate_samples)
        
        self.last_update_time = current_time
    
    def _update_eta(self):
        """更新预计剩余时间"""
        if self.progress.rate > 0 and self.progress.total > 0:
            remaining = self.progress.total - self.progress.current
            self.progress.eta = remaining / self.progress.rate
        else:
            self.progress.eta = 0.0
    
    def set_interrupting(self, message: str = "正在中断任务..."):
        """设置中断状态"""
        with self._lock:
            self._is_interrupting = True
            self.progress.state = TaskState.INTERRUPTING
            self.progress.status_message = message
            self.progress.details = "任务正在安全停止，请稍候..."
            
        self._notify_update()
        self.logger.info(f"任务进入中断状态: {message}")
    
    def set_interrupted(self, message: str = "任务已中断"):
        """设置已中断状态"""
        with self._lock:
            self.progress.state = TaskState.INTERRUPTED
            self.progress.status_message = message
            self.progress.details = "任务已被用户中止"
            
        self._notify_update()
        self.logger.info(f"任务已中断: {message}")
    
    def set_completed(self, message: str = "任务完成"):
        """设置完成状态"""
        with self._lock:
            self.progress.state = TaskState.COMPLETED
            self.progress.status_message = message
            self.progress.current = self.progress.total
            self.progress.percentage = 100.0
            self.progress.details = f"总耗时: {self.progress.elapsed:.2f}秒"
            
        self._notify_update()
        self.logger.info(f"任务完成: {message}")
    
    def set_failed(self, error_message: str):
        """设置失败状态"""
        with self._lock:
            self.progress.state = TaskState.FAILED
            self.progress.status_message = "任务失败"
            self.progress.details = error_message
            
        self._notify_update()
        self.logger.error(f"任务失败: {error_message}")
    
    def _notify_update(self):
        """通知更新"""
        if self.update_callback:
            try:
                # 创建进度信息的副本以避免线程安全问题
                progress_copy = ProgressInfo(
                    current=self.progress.current,
                    total=self.progress.total,
                    percentage=self.progress.percentage,
                    rate=self.progress.rate,
                    eta=self.progress.eta,
                    elapsed=self.progress.elapsed,
                    status_message=self.progress.status_message,
                    details=self.progress.details,
                    state=self.progress.state,
                    last_update=self.progress.last_update
                )
                
                # 调用回调函数
                self.update_callback(
                    progress=progress_copy.percentage,
                    status=progress_copy.status_message,
                    details=self._format_details(progress_copy),
                    current=progress_copy.current,
                    total=progress_copy.total,
                    elapsed=progress_copy.elapsed
                )
            except Exception as e:
                self.logger.error(f"进度回调失败: {e}")
    
    def _format_details(self, progress: ProgressInfo) -> str:
        """格式化详细信息"""
        details_parts = []
        
        if progress.details:
            details_parts.append(progress.details)
        
        # 添加速率信息
        if progress.rate > 0:
            if progress.rate >= 1:
                details_parts.append(f"速率: {progress.rate:.1f} 项/秒")
            else:
                details_parts.append(f"速率: {1/progress.rate:.1f} 秒/项")
        
        # 添加预计剩余时间
        if progress.eta > 0:
            if progress.eta < 60:
                details_parts.append(f"预计剩余: {progress.eta:.0f}秒")
            elif progress.eta < 3600:
                minutes = int(progress.eta // 60)
                seconds = int(progress.eta % 60)
                details_parts.append(f"预计剩余: {minutes}分{seconds}秒")
            else:
                hours = int(progress.eta // 3600)
                minutes = int((progress.eta % 3600) // 60)
                details_parts.append(f"预计剩余: {hours}小时{minutes}分")
        
        # 添加状态信息
        if progress.state == TaskState.INTERRUPTING:
            details_parts.append("⚠️ 正在中断...")
        elif progress.state == TaskState.INTERRUPTED:
            details_parts.append("❌ 已中断")
        elif progress.state == TaskState.COMPLETED:
            details_parts.append("✅ 已完成")
        elif progress.state == TaskState.FAILED:
            details_parts.append("❌ 失败")
        
        return " | ".join(details_parts)
    
    def get_progress_info(self) -> ProgressInfo:
        """获取当前进度信息"""
        with self._lock:
            return ProgressInfo(
                current=self.progress.current,
                total=self.progress.total,
                percentage=self.progress.percentage,
                rate=self.progress.rate,
                eta=self.progress.eta,
                elapsed=self.progress.elapsed,
                status_message=self.progress.status_message,
                details=self.progress.details,
                state=self.progress.state,
                last_update=self.progress.last_update
            )
    
    def is_interrupting(self) -> bool:
        """检查是否正在中断"""
        with self._lock:
            return self._is_interrupting
