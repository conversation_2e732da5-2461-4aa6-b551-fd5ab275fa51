#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
文件扫描服务接口定义

定义文件扫描相关的业务服务接口
遵循RULE-001: 模块职责单一原则
遵循RULE-003: 事件驱动通信规范
"""

from abc import ABC, abstractmethod
from typing import AsyncGenerator, Optional, List, Dict, Any
from enum import Enum

from src.data.dto.scan_dto import ScanRequest, ScanResult, FileInfo, ScanStatus
from src.data.dto.base_dto import ProgressUpdate, ErrorInfo
from .base_service import IBaseService, ITaskService, IValidationService


class ScanPriority(Enum):
    """扫描优先级枚举"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4


class IFileScanService(IBaseService, ITaskService, IValidationService):
    """
    文件扫描服务接口
    
    负责文件和目录的扫描、索引和信息收集
    """
    
    @abstractmethod
    async def scan_directories(self, request: ScanRequest) -> AsyncGenerator[ProgressUpdate, None]:
        """
        异步扫描目录
        
        Args:
            request: 扫描请求DTO
            
        Yields:
            ProgressUpdate: 扫描进度更新
            
        Raises:
            ValueError: 当请求数据无效时
            PermissionError: 当没有访问权限时
            FileNotFoundError: 当目录不存在时
        """
        pass
    
    @abstractmethod
    async def get_scan_result(self, task_id: str) -> Optional[ScanResult]:
        """
        获取扫描结果
        
        Args:
            task_id: 任务ID
            
        Returns:
            ScanResult: 扫描结果DTO，如果任务不存在返回None
        """
        pass
    
    @abstractmethod
    async def pause_scan(self, task_id: str) -> bool:
        """
        暂停扫描任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功暂停
        """
        pass
    
    @abstractmethod
    async def resume_scan(self, task_id: str) -> bool:
        """
        恢复扫描任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功恢复
        """
        pass
    
    @abstractmethod
    async def get_scan_progress(self, task_id: str) -> Optional[ProgressUpdate]:
        """
        获取扫描进度
        
        Args:
            task_id: 任务ID
            
        Returns:
            ProgressUpdate: 进度信息，如果任务不存在返回None
        """
        pass
    
    @abstractmethod
    async def estimate_scan_time(self, request: ScanRequest) -> float:
        """
        估算扫描时间
        
        Args:
            request: 扫描请求DTO
            
        Returns:
            float: 预估时间（秒）
        """
        pass
    
    @abstractmethod
    async def get_directory_info(self, directory_path: str) -> Optional[Dict[str, Any]]:
        """
        获取目录信息
        
        Args:
            directory_path: 目录路径
            
        Returns:
            Dict[str, Any]: 目录信息，包含文件数量、总大小等
        """
        pass
    
    @abstractmethod
    async def validate_scan_request(self, request: ScanRequest) -> List[str]:
        """
        验证扫描请求
        
        Args:
            request: 扫描请求DTO
            
        Returns:
            List[str]: 验证错误列表，空列表表示验证通过
        """
        pass
    
    @abstractmethod
    async def get_supported_file_types(self) -> List[str]:
        """
        获取支持的文件类型
        
        Returns:
            List[str]: 支持的文件扩展名列表
        """
        pass
    
    @abstractmethod
    async def set_scan_priority(self, task_id: str, priority: ScanPriority) -> bool:
        """
        设置扫描优先级
        
        Args:
            task_id: 任务ID
            priority: 优先级
            
        Returns:
            bool: 是否设置成功
        """
        pass


class IFileIndexService(IBaseService):
    """
    文件索引服务接口
    
    负责文件索引的创建、更新和查询
    """
    
    @abstractmethod
    async def create_index(self, files: List[FileInfo]) -> bool:
        """
        创建文件索引
        
        Args:
            files: 文件信息列表
            
        Returns:
            bool: 是否创建成功
        """
        pass
    
    @abstractmethod
    async def update_index(self, files: List[FileInfo]) -> bool:
        """
        更新文件索引
        
        Args:
            files: 文件信息列表
            
        Returns:
            bool: 是否更新成功
        """
        pass
    
    @abstractmethod
    async def remove_from_index(self, file_paths: List[str]) -> bool:
        """
        从索引中移除文件
        
        Args:
            file_paths: 文件路径列表
            
        Returns:
            bool: 是否移除成功
        """
        pass
    
    @abstractmethod
    async def search_files(self, query: str, filters: Optional[Dict[str, Any]] = None) -> List[FileInfo]:
        """
        搜索文件
        
        Args:
            query: 搜索查询
            filters: 搜索过滤器
            
        Returns:
            List[FileInfo]: 匹配的文件列表
        """
        pass
    
    @abstractmethod
    async def get_index_statistics(self) -> Dict[str, Any]:
        """
        获取索引统计信息
        
        Returns:
            Dict[str, Any]: 索引统计信息
        """
        pass
    
    @abstractmethod
    async def rebuild_index(self) -> AsyncGenerator[ProgressUpdate, None]:
        """
        重建索引
        
        Yields:
            ProgressUpdate: 重建进度
        """
        pass
    
    @abstractmethod
    async def optimize_index(self) -> bool:
        """
        优化索引
        
        Returns:
            bool: 是否优化成功
        """
        pass


class IFileHashService(IBaseService):
    """
    文件哈希服务接口
    
    负责文件哈希值的计算和管理
    """
    
    @abstractmethod
    async def calculate_file_hash(self, file_path: str, algorithm: str = "md5") -> Optional[str]:
        """
        计算文件哈希值
        
        Args:
            file_path: 文件路径
            algorithm: 哈希算法 (md5, sha1, sha256)
            
        Returns:
            str: 哈希值，如果计算失败返回None
        """
        pass
    
    @abstractmethod
    async def calculate_batch_hashes(self, file_paths: List[str], 
                                   algorithm: str = "md5") -> AsyncGenerator[Dict[str, str], None]:
        """
        批量计算文件哈希值
        
        Args:
            file_paths: 文件路径列表
            algorithm: 哈希算法
            
        Yields:
            Dict[str, str]: 文件路径到哈希值的映射
        """
        pass
    
    @abstractmethod
    async def verify_file_integrity(self, file_path: str, expected_hash: str, 
                                  algorithm: str = "md5") -> bool:
        """
        验证文件完整性
        
        Args:
            file_path: 文件路径
            expected_hash: 期望的哈希值
            algorithm: 哈希算法
            
        Returns:
            bool: 文件是否完整
        """
        pass
    
    @abstractmethod
    async def get_supported_algorithms(self) -> List[str]:
        """
        获取支持的哈希算法
        
        Returns:
            List[str]: 支持的算法列表
        """
        pass
