#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
文件操作模块 - 高性能异步版本

该模块负责执行文件系统操作，包括：
1. 重命名文件
2. 移动文件
3. 删除文件
4. 创建目录
5. 备份文件

重构特性：
- 纯异步实现，支持高并发
- 智能文件大小处理策略
- 资源池管理和并发控制
- 性能监控和指标收集

作者: SmartFileManger开发团队
日期: 2023-06-01
版本: 2.0.0 (性能重构版)
"""

import os
import shutil
import datetime
import asyncio
import aiofiles
import aiofiles.os
from concurrent.futures import ThreadPoolExecutor
from typing import List, Dict, Any, Optional, Callable
import time
import weakref
from dataclasses import dataclass, field

# 移除循环导入
# from src.core.dependency_injection import resolve
from src.utils.logger import get_logger
from .interfaces import FileOperationsInterface
from src.utils.format_utils import _normalize_path
from .progress_tracker import ProgressTracker

# 创建文件操作日志记录器
# logger = resolve(Logger) # 延迟导入
logger = get_logger("FileOperations") # 直接实例化

@dataclass
class FileOperationMetrics:
    """文件操作性能指标"""
    operation_count: Dict[str, int] = field(default_factory=lambda: {
        'rename': 0, 'move': 0, 'delete': 0, 'backup': 0
    })
    total_bytes_processed: int = 0
    total_time_spent: float = 0.0
    concurrent_operations: int = 0
    max_concurrent_operations: int = 0

    def record_operation(self, operation_type: str, bytes_processed: int = 0, time_spent: float = 0.0):
        """记录操作指标"""
        self.operation_count[operation_type] = self.operation_count.get(operation_type, 0) + 1
        self.total_bytes_processed += bytes_processed
        self.total_time_spent += time_spent

    def start_concurrent_operation(self):
        """开始并发操作"""
        self.concurrent_operations += 1
        self.max_concurrent_operations = max(self.max_concurrent_operations, self.concurrent_operations)

    def end_concurrent_operation(self):
        """结束并发操作"""
        self.concurrent_operations = max(0, self.concurrent_operations - 1)


class OptimizedFileOperations(FileOperationsInterface):
    """
    高性能文件操作类

    特性：
    - 纯异步实现，支持高并发
    - 智能文件大小处理策略
    - 资源池管理和并发控制
    - 性能监控和指标收集
    """

    # 文件大小阈值常量
    SMALL_FILE_THRESHOLD = 1024 * 1024  # 1MB
    MEDIUM_FILE_THRESHOLD = 100 * 1024 * 1024  # 100MB
    LARGE_FILE_THRESHOLD = 1024 * 1024 * 1024  # 1GB

    # 块大小配置
    SMALL_FILE_CHUNK_SIZE = 64 * 1024  # 64KB
    MEDIUM_FILE_CHUNK_SIZE = 1024 * 1024  # 1MB
    LARGE_FILE_CHUNK_SIZE = 8 * 1024 * 1024  # 8MB

    def __init__(self, backup_dir: Optional[str] = None, max_workers: Optional[int] = None,
                 max_concurrent_operations: int = 10):
        """
        初始化高性能文件操作

        参数:
            backup_dir: 备份目录路径，如果为None则不进行备份
            max_workers: 线程池最大工作线程数，如果为None则使用默认值
            max_concurrent_operations: 最大并发操作数
        """
        self.backup_dir = backup_dir
        self.metrics = FileOperationMetrics()
        self.executor = ThreadPoolExecutor(max_workers=max_workers)

        # 并发控制
        self.semaphore = asyncio.Semaphore(max_concurrent_operations)
        self.max_concurrent_operations = max_concurrent_operations

        # 性能监控
        self._operation_times = {}
        self._active_operations = weakref.WeakSet()

        # 如果指定了备份目录，确保它存在
        if self.backup_dir and not os.path.exists(self.backup_dir):
            try:
                os.makedirs(self.backup_dir)
                logger.info(f"创建备份目录: {self.backup_dir}")
            except Exception as e:
                logger.error(f"创建备份目录失败: {e}")
                self.backup_dir = None

    def _get_optimal_chunk_size(self, file_size: int) -> int:
        """根据文件大小获取最优块大小"""
        if file_size <= self.SMALL_FILE_THRESHOLD:
            return self.SMALL_FILE_CHUNK_SIZE
        elif file_size <= self.MEDIUM_FILE_THRESHOLD:
            return self.MEDIUM_FILE_CHUNK_SIZE
        else:
            return self.LARGE_FILE_CHUNK_SIZE

    async def _get_file_size_async(self, file_path: str) -> int:
        """异步获取文件大小"""
        try:
            stat_result = await aiofiles.os.stat(file_path)
            return stat_result.st_size
        except Exception as e:
            logger.warning(f"获取文件大小失败: {file_path}, 错误: {e}")
            return 0
    
    async def backup_file_async(self, file_path: str) -> Optional[str]:
        """
        高性能异步备份文件

        特性：
        - 智能块大小选择
        - 并发控制
        - 性能监控
        - 原子操作保证

        参数:
            file_path: 要备份的文件路径

        返回:
            备份文件路径或None（如果备份失败）
        """
        if not self.backup_dir:
            logger.warning(f"备份目录未设置，无法备份文件: {file_path}")
            return None

        # 并发控制
        async with self.semaphore:
            self.metrics.start_concurrent_operation()
            start_time = time.time()
            bytes_processed = 0
            backup_path = None

            try:
                # 检查文件存在性
                if not await aiofiles.os.path.exists(file_path):
                    logger.error(f"要备份的文件不存在: {file_path}")
                    return None

                if not await aiofiles.os.path.isfile(file_path):
                    logger.error(f"备份路径不是文件: {file_path}")
                    return None

                # 获取文件大小并选择最优块大小
                file_size = await self._get_file_size_async(file_path)
                chunk_size = self._get_optimal_chunk_size(file_size)

                # 创建备份文件名（添加时间戳）
                file_name = os.path.basename(file_path)
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_name = f"{os.path.splitext(file_name)[0]}_{timestamp}{os.path.splitext(file_name)[1]}"
                backup_path = os.path.join(self.backup_dir, backup_name)

                # 高性能异步复制文件
                async with aiofiles.open(file_path, 'rb') as src, \
                           aiofiles.open(backup_path, 'wb') as dst:
                    while True:
                        chunk = await src.read(chunk_size)
                        if not chunk:
                            break
                        await dst.write(chunk)
                        bytes_processed += len(chunk)

                # 异步复制文件元数据
                await asyncio.get_event_loop().run_in_executor(
                    self.executor,
                    lambda: shutil.copystat(file_path, backup_path)
                )

                # 记录性能指标
                operation_time = time.time() - start_time
                self.metrics.record_operation('backup', bytes_processed, operation_time)

                logger.info(f"备份文件完成: {file_path} -> {backup_path} "
                           f"({bytes_processed} bytes, {operation_time:.3f}s)")
                return backup_path

            except asyncio.CancelledError:
                logger.warning(f"备份操作被取消: {file_path}")
                # 清理不完整的备份文件
                if backup_path and await aiofiles.os.path.exists(backup_path):
                    try:
                        await aiofiles.os.remove(backup_path)
                        logger.info(f"已删除不完整的备份文件: {backup_path}")
                    except Exception as cleanup_err:
                        logger.error(f"清理不完整备份文件失败: {backup_path}, 错误: {cleanup_err}")
                return None
            except Exception as e:
                logger.error(f"备份文件失败: {file_path}, 错误: {e}")
                return None
            finally:
                self.metrics.end_concurrent_operation()
    
    def backup_file(self, file_path: str, backup_dir: Optional[str] = None) -> str:
        """
        同步备份文件（兼容性接口）

        注意：此方法为向后兼容而保留，建议使用异步版本以获得更好的性能

        参数:
            file_path: 要备份的文件路径
            backup_dir: 备份目录路径，如果为None则使用默认备份目录

        返回:
            备份文件路径或空字符串（如果备份失败）
        """
        # 如果提供了备份目录，暂时更改默认备份目录
        original_backup_dir = self.backup_dir
        if backup_dir:
            self.backup_dir = backup_dir
            # 确保备份目录存在
            if not os.path.exists(self.backup_dir):
                try:
                    os.makedirs(self.backup_dir)
                except Exception as e:
                    logger.error(f"创建备份目录失败: {e}")
                    self.backup_dir = original_backup_dir
                    return ""

        try:
            # 检查是否在事件循环中运行
            try:
                loop = asyncio.get_running_loop()
                # 如果在事件循环中，创建任务而不是使用 asyncio.run()
                task = loop.create_task(self.backup_file_async(file_path))
                result = asyncio.run_coroutine_threadsafe(task, loop).result()
            except RuntimeError:
                # 没有运行中的事件循环，使用 asyncio.run()
                result = asyncio.run(self.backup_file_async(file_path))

            # 恢复原始备份目录
            if backup_dir:
                self.backup_dir = original_backup_dir
            return result if result else ""
        except Exception as e:
            logger.error(f"备份文件失败: {file_path}, 错误: {e}")
            # 恢复原始备份目录
            if backup_dir:
                self.backup_dir = original_backup_dir
            return ""

    async def backup_files_batch_async(self, file_paths: List[str],
                                      progress_callback: Optional[Callable] = None) -> List[Optional[str]]:
        """
        批量异步备份文件

        参数:
            file_paths: 要备份的文件路径列表
            progress_callback: 进度回调函数

        返回:
            备份文件路径列表，失败的项目为None
        """
        if not file_paths:
            return []

        logger.info(f"开始批量备份 {len(file_paths)} 个文件")
        start_time = time.time()

        # 创建并发任务
        tasks = []
        for i, file_path in enumerate(file_paths):
            task = self.backup_file_async(file_path)
            tasks.append(task)

        # 执行批量备份
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理结果
        backup_paths = []
        success_count = 0
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"备份文件失败: {file_paths[i]}, 错误: {result}")
                backup_paths.append(None)
            else:
                backup_paths.append(result)
                if result:
                    success_count += 1

            # 调用进度回调
            if progress_callback:
                progress = (i + 1) / len(file_paths) * 100
                progress_callback(progress, f"已备份 {i + 1}/{len(file_paths)} 个文件")

        total_time = time.time() - start_time
        logger.info(f"批量备份完成: {success_count}/{len(file_paths)} 成功, 耗时 {total_time:.3f}s")

        return backup_paths
    
    async def rename_file_async(self, file_path: str, new_name: str, backup: bool = True) -> Optional[str]:
        """
        高性能异步重命名文件

        特性：
        - 并发控制
        - 原子操作
        - 性能监控
        - 智能冲突处理

        参数:
            file_path: 要重命名的文件路径
            new_name: 新文件名（不包含路径）
            backup: 是否备份原文件

        返回:
            新文件路径或None（如果重命名失败）
        """
        file_path = _normalize_path(file_path)

        # 并发控制
        async with self.semaphore:
            self.metrics.start_concurrent_operation()
            start_time = time.time()

            try:
                # 检查文件存在性
                if not await aiofiles.os.path.exists(file_path):
                    logger.error(f"文件不存在: {file_path}")
                    return None

                # 获取文件目录和新文件路径
                file_dir = os.path.dirname(file_path)
                new_path = os.path.join(file_dir, new_name)

                # 检查新文件名是否已存在
                if (await aiofiles.os.path.exists(new_path) and
                    os.path.normpath(file_path) != os.path.normpath(new_path)):
                    logger.warning(f"目标文件已存在: {new_path}")
                    return None

                # 获取文件大小用于性能监控
                file_size = await self._get_file_size_async(file_path)

                # 备份原文件
                if backup:
                    await self.backup_file_async(file_path)

                # 异步重命名文件
                await asyncio.get_event_loop().run_in_executor(
                    self.executor,
                    os.rename,
                    file_path,
                    new_path
                )

                # 记录性能指标
                operation_time = time.time() - start_time
                self.metrics.record_operation('rename', file_size, operation_time)

                logger.info(f"重命名文件完成: {file_path} -> {new_path} "
                           f"({file_size} bytes, {operation_time:.3f}s)")
                return new_path

            except Exception as e:
                logger.error(f"重命名文件失败: {file_path}, 错误: {e}")
                return None
            finally:
                self.metrics.end_concurrent_operation()
    
    def rename_file(self, file_path: str, new_file_path: str, backup: bool = True) -> bool:
        """
        重命名文件（兼容性接口）

        注意：此方法为向后兼容而保留，建议使用异步版本以获得更好的性能

        参数:
            file_path: 源文件路径
            new_file_path: 目标文件名（不包含路径）
            backup: 是否备份原文件

        返回:
            操作是否成功
        """
        file_path = _normalize_path(file_path)
        new_file_path = _normalize_path(new_file_path)
        try:
            # 获取目标文件名（不包含路径）
            target_name = os.path.basename(new_file_path)

            # 检查是否在事件循环中运行
            try:
                loop = asyncio.get_running_loop()
                # 如果在事件循环中，创建任务而不是使用 asyncio.run()
                task = loop.create_task(self.rename_file_async(file_path, target_name, backup))
                result = asyncio.run_coroutine_threadsafe(task, loop).result()
            except RuntimeError:
                # 没有运行中的事件循环，使用 asyncio.run()
                result = asyncio.run(self.rename_file_async(file_path, target_name, backup))

            return result is not None
        except Exception as e:
            logger.error(f"重命名文件失败: {file_path}, 错误: {e}")
            return False

    def get_performance_metrics(self) -> Dict[str, Any]:
        """
        获取性能指标

        返回:
            包含性能指标的字典
        """
        return {
            'operation_count': dict(self.metrics.operation_count),
            'total_bytes_processed': self.metrics.total_bytes_processed,
            'total_time_spent': self.metrics.total_time_spent,
            'current_concurrent_operations': self.metrics.concurrent_operations,
            'max_concurrent_operations': self.metrics.max_concurrent_operations,
            'average_throughput_mbps': (
                (self.metrics.total_bytes_processed / (1024 * 1024)) / self.metrics.total_time_spent
                if self.metrics.total_time_spent > 0 else 0
            ),
            'operations_per_second': (
                sum(self.metrics.operation_count.values()) / self.metrics.total_time_spent
                if self.metrics.total_time_spent > 0 else 0
            )
        }

    def reset_metrics(self):
        """重置性能指标"""
        self.metrics = FileOperationMetrics()

    async def shutdown(self):
        """关闭文件操作器，清理资源"""
        try:
            # 等待所有活动操作完成
            while self.metrics.concurrent_operations > 0:
                await asyncio.sleep(0.1)

            # 关闭线程池
            self.executor.shutdown(wait=True)
            logger.info("文件操作器已关闭")
        except Exception as e:
            logger.error(f"关闭文件操作器时出错: {e}")


# 向后兼容性类
class FileOperations(OptimizedFileOperations):
    """
    向后兼容的文件操作类

    这个类继承自 OptimizedFileOperations，提供与原始 FileOperations 类相同的接口，
    同时享受新的高性能实现带来的性能提升。
    """

    def __init__(self, backup_dir: Optional[str] = None, max_workers: Optional[int] = None):
        """
        初始化文件操作（向后兼容）

        参数:
            backup_dir: 备份目录路径，如果为None则不进行备份
            max_workers: 线程池最大工作线程数，如果为None则使用默认值
        """
        # 调用优化版本的初始化，使用默认的并发控制设置
        super().__init__(backup_dir=backup_dir, max_workers=max_workers, max_concurrent_operations=10)

        # 为了向后兼容，保留 operation_count 属性
        self.operation_count = self.metrics.operation_count

    def get_operation_count(self) -> Dict[str, int]:
        """获取操作计数（向后兼容）"""
        return dict(self.metrics.operation_count)

    def get_metrics(self) -> FileOperationMetrics:
        """获取操作指标（向后兼容）"""
        return self.metrics
    
    async def move_file_async(self, file_path: str, target_file_path: str, new_name: Optional[str] = None, backup: bool = True) -> Optional[str]:
        """
        高性能异步移动文件

        特性：
        - 智能块大小选择
        - 并发控制
        - 性能监控
        - 原子操作保证

        参数:
            file_path: 要移动的文件路径
            target_file_path: 目标目录路径
            new_name: 新文件名（不包含路径），如果为None则保持原文件名
            backup: 是否备份原文件

        返回:
            新文件路径或None（如果移动失败）
        """
        file_path = _normalize_path(file_path)
        target_file_path = _normalize_path(target_file_path)

        # 并发控制
        async with self.semaphore:
            self.metrics.start_concurrent_operation()
            start_time = time.time()
            bytes_processed = 0

            try:
                # 检查源文件存在性
                if not await aiofiles.os.path.exists(file_path):
                    logger.error(f"文件不存在: {file_path}")
                    return None

                # 创建目标目录（如果不存在）
                if not await aiofiles.os.path.exists(target_file_path):
                    await aiofiles.os.makedirs(target_file_path)
                    logger.info(f"创建目标目录: {target_file_path}")

                # 确定目标文件名和路径
                file_name = new_name if new_name else os.path.basename(file_path)
                target_path = os.path.join(target_file_path, file_name)

                # 检查目标文件是否已存在
                if (await aiofiles.os.path.exists(target_path) and
                    os.path.normpath(file_path) != os.path.normpath(target_path)):
                    logger.warning(f"目标文件已存在: {target_path}")
                    return None

                # 获取文件大小并选择最优块大小
                file_size = await self._get_file_size_async(file_path)
                chunk_size = self._get_optimal_chunk_size(file_size)

                # 备份原文件
                if backup:
                    await self.backup_file_async(file_path)

                # 高性能异步移动文件
                async with aiofiles.open(file_path, 'rb') as src, \
                           aiofiles.open(target_path, 'wb') as dst:
                    while True:
                        chunk = await src.read(chunk_size)
                        if not chunk:
                            break
                        await dst.write(chunk)
                        bytes_processed += len(chunk)

                # 复制文件元数据并删除源文件
                await asyncio.get_event_loop().run_in_executor(
                    self.executor,
                    lambda: (
                        shutil.copystat(file_path, target_path),
                        os.remove(file_path)
                    )
                )

                # 记录性能指标
                operation_time = time.time() - start_time
                self.metrics.record_operation('move', bytes_processed, operation_time)

                logger.info(f"移动文件完成: {file_path} -> {target_path} "
                           f"({bytes_processed} bytes, {operation_time:.3f}s)")
                return target_path

            except Exception as e:
                logger.error(f"移动文件失败: {file_path}, 错误: {e}")
                return None
            finally:
                self.metrics.end_concurrent_operation()
    
    def move_file(self, source: str, target: str, backup: bool = True) -> bool:
        """
        移动文件（兼容性接口）

        注意：此方法为向后兼容而保留，建议使用异步版本以获得更好的性能

        参数:
            source: 源文件路径
            target: 目标文件路径
            backup: 是否备份原文件

        返回:
            操作是否成功
        """
        try:
            # 获取目标目录和文件名
            target_dir = os.path.dirname(target)
            target_name = os.path.basename(target)

            # 检查是否在事件循环中运行
            try:
                loop = asyncio.get_running_loop()
                # 如果在事件循环中，创建任务而不是使用 asyncio.run()
                task = loop.create_task(self.move_file_async(source, target_dir, target_name, backup))
                result = asyncio.run_coroutine_threadsafe(task, loop).result()
            except RuntimeError:
                # 没有运行中的事件循环，使用 asyncio.run()
                result = asyncio.run(self.move_file_async(source, target_dir, target_name, backup))

            return result is not None
        except Exception as e:
            logger.error(f"移动文件失败: {source}, 错误: {e}")
            return False

    async def delete_file_async(self, file_path: str, backup: bool = True) -> bool:
        """
        高性能异步删除文件

        特性：
        - 并发控制
        - 性能监控
        - 原子操作保证

        参数:
            file_path: 要删除的文件路径
            backup: 是否备份原文件

        返回:
            删除成功返回True，否则返回False
        """
        file_path = _normalize_path(file_path)

        # 并发控制
        async with self.semaphore:
            self.metrics.start_concurrent_operation()
            start_time = time.time()

            try:
                # 检查文件存在性
                if not await aiofiles.os.path.exists(file_path):
                    logger.error(f"文件不存在: {file_path}")
                    return False

                # 获取文件大小用于性能监控
                file_size = await self._get_file_size_async(file_path)

                # 备份原文件
                if backup:
                    await self.backup_file_async(file_path)

                # 异步删除文件
                await aiofiles.os.remove(file_path)

                # 记录性能指标
                operation_time = time.time() - start_time
                self.metrics.record_operation('delete', file_size, operation_time)

                logger.info(f"删除文件完成: {file_path} "
                           f"({file_size} bytes, {operation_time:.3f}s)")
                return True

            except Exception as e:
                logger.error(f"删除文件失败: {file_path}, 错误: {e}")
                return False
            finally:
                self.metrics.end_concurrent_operation()

    def delete_file(self, file_path: str, backup: bool = True) -> bool:
        """
        删除文件（兼容性接口）

        注意：此方法为向后兼容而保留，建议使用异步版本以获得更好的性能

        参数:
            file_path: 文件路径
            backup: 是否备份原文件

        返回:
            操作是否成功
        """
        try:
            file_path = _normalize_path(file_path)

            # 检查是否在事件循环中运行
            try:
                loop = asyncio.get_running_loop()
                # 如果在事件循环中，创建任务而不是使用 asyncio.run()
                task = loop.create_task(self.delete_file_async(file_path, backup))
                result = asyncio.run_coroutine_threadsafe(task, loop).result()
            except RuntimeError:
                # 没有运行中的事件循环，使用 asyncio.run()
                result = asyncio.run(self.delete_file_async(file_path, backup))

            return result
        except Exception as e:
            logger.error(f"删除文件失败: {file_path}, 错误: {e}")
            return False

    async def create_directory_async(self, directory_path: str) -> bool:
        """
        异步创建目录

        参数:
            directory_path: 目录路径

        返回:
            创建成功返回True，否则返回False
        """
        try:
            directory_path = _normalize_path(directory_path)

            if await aiofiles.os.path.exists(directory_path):
                logger.info(f"目录已存在: {directory_path}")
                return True

            await aiofiles.os.makedirs(directory_path)
            logger.info(f"创建目录: {directory_path}")
            return True

        except Exception as e:
            logger.error(f"创建目录失败: {directory_path}, 错误: {e}")
            return False

    def create_directory(self, directory_path: str) -> bool:
        """
        创建目录（兼容性接口）

        参数:
            directory_path: 目录路径

        返回:
            操作是否成功
        """
        try:
            # 检查是否在事件循环中运行
            try:
                loop = asyncio.get_running_loop()
                # 如果在事件循环中，创建任务而不是使用 asyncio.run()
                task = loop.create_task(self.create_directory_async(directory_path))
                result = asyncio.run_coroutine_threadsafe(task, loop).result()
            except RuntimeError:
                # 没有运行中的事件循环，使用 asyncio.run()
                result = asyncio.run(self.create_directory_async(directory_path))

            return result
        except Exception as e:
            logger.error(f"创建目录失败: {directory_path}, 错误: {e}")
            return False

    async def batch_rename_files_async(self, file_paths: List[str], new_names: List[str],
                                      backup: bool = True) -> Dict[str, bool]:
        """
        批量异步重命名文件

        参数:
            file_paths: 文件路径列表
            new_names: 新文件名列表
            backup: 是否备份原文件

        返回:
            操作结果字典, 键为文件路径, 值为操作是否成功
        """
        if len(file_paths) != len(new_names):
            logger.error("文件路径列表和新文件名列表长度不一致")
            return {path: False for path in file_paths}

        logger.info(f"开始批量重命名 {len(file_paths)} 个文件")
        start_time = time.time()

        # 创建并发任务
        tasks = []
        for file_path, new_name in zip(file_paths, new_names):
            task = self.rename_file_async(file_path, new_name, backup)
            tasks.append((file_path, task))

        # 执行批量重命名
        results = {}
        completed_tasks = await asyncio.gather(*[task for _, task in tasks], return_exceptions=True)

        for (file_path, _), result in zip(tasks, completed_tasks):
            if isinstance(result, Exception):
                logger.error(f"重命名文件失败: {file_path}, 错误: {result}")
                results[file_path] = False
            else:
                results[file_path] = result is not None

        total_time = time.time() - start_time
        success_count = sum(results.values())
        logger.info(f"批量重命名完成: {success_count}/{len(file_paths)} 成功, 耗时 {total_time:.3f}s")

        return results

    def batch_rename_files(self, file_paths: List[str], new_names: List[str],
                          backup: bool = True) -> Dict[str, bool]:
        """
        批量重命名文件（兼容性接口）

        参数:
            file_paths: 文件路径列表
            new_names: 新文件名列表
            backup: 是否备份原文件

        返回:
            操作结果字典, 键为文件路径, 值为操作是否成功
        """
        try:
            # 检查是否在事件循环中运行
            try:
                loop = asyncio.get_running_loop()
                # 如果在事件循环中，创建任务而不是使用 asyncio.run()
                task = loop.create_task(self.batch_rename_files_async(file_paths, new_names, backup))
                result = asyncio.run_coroutine_threadsafe(task, loop).result()
            except RuntimeError:
                # 没有运行中的事件循环，使用 asyncio.run()
                result = asyncio.run(self.batch_rename_files_async(file_paths, new_names, backup))

            return result
        except Exception as e:
            logger.error(f"批量重命名文件失败: {e}")
            return {path: False for path in file_paths}

    async def batch_move_files_async(self, file_paths: List[str], target_dir: str,
                                    backup: bool = True) -> Dict[str, bool]:
        """
        批量异步移动文件

        参数:
            file_paths: 文件路径列表
            target_dir: 目标目录路径
            backup: 是否备份原文件

        返回:
            操作结果字典, 键为文件路径, 值为操作是否成功
        """
        logger.info(f"开始批量移动 {len(file_paths)} 个文件到 {target_dir}")
        start_time = time.time()

        # 确保目标目录存在
        await self.create_directory_async(target_dir)

        # 创建并发任务
        tasks = []
        for file_path in file_paths:
            task = self.move_file_async(file_path, target_dir, backup=backup)
            tasks.append((file_path, task))

        # 执行批量移动
        results = {}
        completed_tasks = await asyncio.gather(*[task for _, task in tasks], return_exceptions=True)

        for (file_path, _), result in zip(tasks, completed_tasks):
            if isinstance(result, Exception):
                logger.error(f"移动文件失败: {file_path}, 错误: {result}")
                results[file_path] = False
            else:
                results[file_path] = result is not None

        total_time = time.time() - start_time
        success_count = sum(results.values())
        logger.info(f"批量移动完成: {success_count}/{len(file_paths)} 成功, 耗时 {total_time:.3f}s")

        return results

    def batch_move_files(self, file_paths: List[str], target_dir: str,
                        backup: bool = True) -> Dict[str, bool]:
        """
        批量移动文件（兼容性接口）

        参数:
            file_paths: 文件路径列表
            target_dir: 目标目录路径
            backup: 是否备份原文件

        返回:
            操作结果字典, 键为文件路径, 值为操作是否成功
        """
        try:
            # 检查是否在事件循环中运行
            try:
                loop = asyncio.get_running_loop()
                # 如果在事件循环中，创建任务而不是使用 asyncio.run()
                task = loop.create_task(self.batch_move_files_async(file_paths, target_dir, backup))
                result = asyncio.run_coroutine_threadsafe(task, loop).result()
            except RuntimeError:
                # 没有运行中的事件循环，使用 asyncio.run()
                result = asyncio.run(self.batch_move_files_async(file_paths, target_dir, backup))

            return result
        except Exception as e:
            logger.error(f"批量移动文件失败: {e}")
            return {path: False for path in file_paths}

    async def batch_delete_files_async(self, file_paths: List[str],
                                      backup: bool = True) -> Dict[str, bool]:
        """
        批量异步删除文件

        参数:
            file_paths: 文件路径列表
            backup: 是否备份原文件

        返回:
            操作结果字典, 键为文件路径, 值为操作是否成功
        """
        logger.info(f"开始批量删除 {len(file_paths)} 个文件")
        start_time = time.time()

        # 创建并发任务
        tasks = []
        for file_path in file_paths:
            task = self.delete_file_async(file_path, backup)
            tasks.append((file_path, task))

        # 执行批量删除
        results = {}
        completed_tasks = await asyncio.gather(*[task for _, task in tasks], return_exceptions=True)

        for (file_path, _), result in zip(tasks, completed_tasks):
            if isinstance(result, Exception):
                logger.error(f"删除文件失败: {file_path}, 错误: {result}")
                results[file_path] = False
            else:
                results[file_path] = result

        total_time = time.time() - start_time
        success_count = sum(results.values())
        logger.info(f"批量删除完成: {success_count}/{len(file_paths)} 成功, 耗时 {total_time:.3f}s")

        return results

    def batch_delete_files(self, file_paths: List[str], backup: bool = True) -> Dict[str, bool]:
        """
        批量删除文件（兼容性接口）

        参数:
            file_paths: 文件路径列表
            backup: 是否备份原文件

        返回:
            操作结果字典, 键为文件路径, 值为操作是否成功
        """
        try:
            # 检查是否在事件循环中运行
            try:
                loop = asyncio.get_running_loop()
                # 如果在事件循环中，创建任务而不是使用 asyncio.run()
                task = loop.create_task(self.batch_delete_files_async(file_paths, backup))
                result = asyncio.run_coroutine_threadsafe(task, loop).result()
            except RuntimeError:
                # 没有运行中的事件循环，使用 asyncio.run()
                result = asyncio.run(self.batch_delete_files_async(file_paths, backup))

            return result
        except Exception as e:
            logger.error(f"批量删除文件失败: {e}")
            return {path: False for path in file_paths}
    
    async def delete_file_async(self, file_path: str, backup: bool = True) -> bool:
        """
        异步删除文件
        
        参数:
            file_path: 要删除的文件路径
            backup: 是否备份原文件
            
        返回:
            删除成功返回True，否则返回False
        """
        file_path = _normalize_path(file_path)
        start_time = time.time()

        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {file_path}")
            return False

        try:
            # 备份原文件
            if backup:
                await self.backup_file_async(file_path)
            
            # 异步删除文件
            await asyncio.get_event_loop().run_in_executor(
                self.executor,
                os.remove,
                file_path
            )
            
            logger.info(f"删除文件: {file_path}")
            self.metrics.record_operation('delete', 0, time.time() - start_time)
            return True
        except Exception as e:
            logger.error(f"删除文件失败: {file_path}, 错误: {e}")
            return False
    
    def delete_file(self, file_path: str, backup: bool = True) -> bool:
        """
        删除文件
        
        参数:
            file_path: 文件路径
            backup: 是否备份原文件
            
        返回:
            操作是否成功
        """
        try:
            file_path = _normalize_path(file_path)
            return asyncio.run(self.delete_file_async(file_path, backup))
        except Exception as e:
            logger.error(f"删除文件失败: {file_path}, 错误: {e}")
            return False
    
    def create_directory(self, directory_path: str) -> bool:
        """
        创建目录
        
        参数:
            directory_path: 目录路径
            
        返回:
            操作是否成功
        """
        try:
            directory_path = _normalize_path(directory_path)
            if not os.path.exists(directory_path):
                os.makedirs(directory_path)
                logger.info(f"创建目录: {directory_path}")
            return True
        except Exception as e:
            logger.error(f"创建目录失败: {directory_path}, 错误: {e}")
            return False
    
    async def batch_rename_files_async(self, file_paths: List[str], new_names: List[str], backup: bool = True, callback: Optional[Callable] = None, interrupt_event=None) -> Dict[str, Any]:
        """
        异步批量重命名文件
        """
        tracker = ProgressTracker(total_items=len(file_paths))
        start_time = time.time()
        
        if len(file_paths) != len(new_names):
            logger.error("文件路径列表和新文件名列表长度不一致")
            return {'success': 0, 'failed': 0, 'total': len(file_paths)}

        results = {'success': 0, 'failed': 0, 'total': len(file_paths)}
        tasks = []
        
        for i, (file_path, new_name) in enumerate(zip(file_paths, new_names)):
            if interrupt_event and interrupt_event.is_set():
                logger.info(f"批量重命名任务被中断，已处理 {i}/{len(file_paths)} 个文件")
                break
            tasks.append(self.rename_file_async(file_path, new_name, backup))

        for i, task_future in enumerate(asyncio.as_completed(tasks)):
            try:
                if interrupt_event and interrupt_event.is_set():
                    logger.info(f"批量重命名任务在等待结果时被中断")
                    break 
                
                result = await task_future
                if result:
                    results['success'] += 1
                else:
                    results['failed'] += 1
                
                await tracker.update(1)
                if callback:
                    progress_info = await tracker.get_progress_info()
                    callback(
                        progress_info["progress"],
                        f"重命名: {i+1}/{len(file_paths)}",
                        file_paths[i],
                        i+1,
                        len(file_paths),
                        progress_info["elapsed_time"]
                    )
            except Exception as e:
                logger.error(f"重命名任务失败: {file_paths[i]}, 错误: {e}")
                results['failed'] += 1
        
        logger.info(f"批量重命名完成: 成功 {results['success']}, 失败 {results['failed']}, 总计 {results['total']}")
        return results
    
    def batch_rename_files(self, file_paths: List[str], new_names: List[str], backup: bool = True) -> Dict[str, bool]:
        """
        批量重命名文件
        
        参数:
            file_paths: 文件路径列表
            new_names: 新文件名列表
            backup: 是否备份原文件
            
        返回:
            操作结果字典, 键为文件路径, 值为操作是否成功
        """
        try:
            results = asyncio.run(self.batch_rename_files_async(file_paths, new_names, backup))
            # 转换结果格式以符合接口要求
            result_dict = {}
            for i, file_path in enumerate(file_paths):
                if i < results.get('success', 0):
                    result_dict[file_path] = True
                else:
                    result_dict[file_path] = False
            return result_dict
        except Exception as e:
            logger.error(f"批量重命名文件失败: {e}")
            return {file_path: False for file_path in file_paths}
    
    async def batch_move_files_async(self, file_paths: List[str], target_file_path: str, new_names: Optional[List[str]] = None, backup: bool = True, callback: Optional[Callable] = None, interrupt_event=None) -> Dict[str, Any]:
        """
        异步批量移动文件
        """
        tracker = ProgressTracker(total_items=len(file_paths))
        
        if new_names and len(file_paths) != len(new_names):
            logger.error("文件路径列表和新文件名列表长度不一致")
            return {'success': 0, 'failed': 0, 'total': len(file_paths)}

        results = {
            'success': 0, 
            'failed': 0, 
            'total': len(file_paths),
            'success_files': [],
            'failed_files': []
        }
        
        target_file_path_norm = _normalize_path(target_file_path)
        if not os.path.exists(target_file_path_norm):
            try:
                os.makedirs(target_file_path_norm)
                logger.info(f"创建目标目录: {target_file_path_norm}")
            except Exception as e:
                logger.error(f"创建目标目录失败: {target_file_path_norm}, 错误: {e}")
                results['failed'] = len(file_paths)
                results['failed_files'] = file_paths
                return results

        tasks = []
        for i, file_path in enumerate(file_paths):
            if interrupt_event and interrupt_event.is_set():
                logger.info(f"批量移动任务被中断，已处理 {i}/{len(file_paths)} 个文件")
                break
            new_name = new_names[i] if new_names else None
            tasks.append(self.move_file_async(_normalize_path(file_path), target_file_path_norm, new_name, backup))

        for i, task_future in enumerate(asyncio.as_completed(tasks)):
            try:
                if interrupt_event and interrupt_event.is_set():
                    logger.info(f"批量移动任务在等待结果时被中断")
                    break

                result = await task_future
                if result:
                    results['success'] += 1
                    results['success_files'].append(file_paths[i])
                else:
                    results['failed'] += 1
                    results['failed_files'].append(file_paths[i])

                await tracker.update(1)
                if callback:
                    progress_info = await tracker.get_progress_info()
                    callback(
                        progress_info["progress"],
                        f"移动: {i+1}/{len(file_paths)}",
                        file_paths[i],
                        i+1,
                        len(file_paths),
                        progress_info["elapsed_time"]
                    )
            except Exception as e:
                logger.error(f"移动任务失败: {file_paths[i]}, 错误: {e}")
                results['failed'] += 1
                results['failed_files'].append(file_paths[i])

        logger.info(f"批量移动完成: 成功 {results['success']}, 失败 {results['failed']}, 总计 {results['total']}")
        return results
    
    def batch_move_files(self, file_paths: List[str], target_dir: str, backup: bool = True) -> Dict[str, bool]:
        """
        批量移动文件
        
        参数:
            file_paths: 文件路径列表
            target_dir: 目标目录路径
            backup: 是否备份原文件
            
        返回:
            操作结果字典, 键为文件路径, 值为操作是否成功
        """
        try:
            results = asyncio.run(self.batch_move_files_async(file_paths, target_dir, None, backup))
            # 转换结果格式以符合接口要求
            result_dict = {}
            for file_path in file_paths:
                if file_path in results.get('success_files', []):
                    result_dict[file_path] = True
                else:
                    result_dict[file_path] = False
            return result_dict
        except Exception as e:
            logger.error(f"批量移动文件失败: {e}")
            return {file_path: False for file_path in file_paths}
    
    async def batch_delete_files_async(self, file_paths: List[str], backup: bool = True, callback: Optional[Callable] = None, interrupt_event=None) -> Dict[str, Any]:
        """
        异步批量删除文件
        """
        tracker = ProgressTracker(total_items=len(file_paths))
        
        results = {
            'success': 0, 
            'failed': 0, 
            'total': len(file_paths),
            'success_files': [],
            'failed_files': []
        }
        
        tasks = []
        for i, file_path in enumerate(file_paths):
            if interrupt_event and interrupt_event.is_set():
                logger.info(f"批量删除任务被中断，已处理 {i}/{len(file_paths)} 个文件")
                break
            tasks.append(self.delete_file_async(_normalize_path(file_path), backup))

        for i, task_future in enumerate(asyncio.as_completed(tasks)):
            try:
                if interrupt_event and interrupt_event.is_set():
                    logger.info(f"批量删除任务在等待结果时被中断")
                    break
                
                result = await task_future
                if result:
                    results['success'] += 1
                    results['success_files'].append(file_paths[i])
                else:
                    results['failed'] += 1
                    results['failed_files'].append(file_paths[i])
                
                await tracker.update(1)
                if callback:
                    progress_info = await tracker.get_progress_info()
                    callback(
                        progress_info["progress"],
                        f"删除: {i+1}/{len(file_paths)}",
                        file_paths[i],
                        i+1,
                        len(file_paths),
                        progress_info["elapsed_time"]
                    )
            except Exception as e:
                logger.error(f"删除任务失败: {file_paths[i]}, 错误: {e}")
                results['failed'] += 1
                results['failed_files'].append(file_paths[i])

        logger.info(f"批量删除完成: 成功 {results['success']}, 失败 {results['failed']}, 总计 {results['total']}")
        return results
    
    def batch_delete_files(self, file_paths: List[str], backup: bool = True) -> Dict[str, bool]:
        """
        批量删除文件
        
        参数:
            file_paths: 文件路径列表
            backup: 是否备份原文件
            
        返回:
            操作结果字典, 键为文件路径, 值为操作是否成功
        """
        try:
            results = asyncio.run(self.batch_delete_files_async(file_paths, backup))
            # 转换结果格式以符合接口要求
            result_dict = {}
            for file_path in file_paths:
                if file_path in results.get('success_files', []):
                    result_dict[file_path] = True
                else:
                    result_dict[file_path] = False
            return result_dict
        except Exception as e:
            logger.error(f"批量删除文件失败: {e}")
            return {file_path: False for file_path in file_paths}
    
    def get_operation_stats(self) -> Dict[str, int]:
        """
        获取操作统计信息
        
        返回:
            操作统计信息
        """
        return self.operation_count