#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
重命名服务接口定义

定义文件重命名相关的业务服务接口
遵循RULE-001: 模块职责单一原则
遵循RULE-003: 事件驱动通信规范
"""

from abc import ABC, abstractmethod
from typing import AsyncGenerator, Optional, List, Dict, Any
from enum import Enum

from src.data.dto.rename_dto import (
    RenameRequest, RenameResult, RenameRule, RenamePreview,
    RenameRuleType, CaseType
)
from src.data.dto.base_dto import ProgressUpdate, ErrorInfo
from .base_service import IBaseService, ITaskService, IValidationService


# 常量定义
DEFAULT_COUNTER_DIGITS = 3  # 默认计数器位数


class RenameConflictResolution(Enum):
    """重命名冲突解决策略枚举"""
    SKIP = "skip"
    OVERWRITE = "overwrite"
    ADD_SUFFIX = "add_suffix"
    ADD_PREFIX = "add_prefix"
    ADD_COUNTER = "add_counter"
    PROMPT_USER = "prompt_user"


class IRenameService(IBaseService, ITaskService, IValidationService):
    """
    文件重命名服务接口
    
    负责文件和目录的重命名操作
    """
    
    @abstractmethod
    async def preview_rename(self, request: RenameRequest) -> List[RenamePreview]:
        """
        预览重命名操作
        
        Args:
            request: 重命名请求DTO
            
        Returns:
            List[RenamePreview]: 重命名预览列表
            
        Raises:
            ValueError: 当请求数据无效时
        """
        pass
    
    @abstractmethod
    async def execute_rename(self, request: RenameRequest) -> AsyncGenerator[ProgressUpdate, None]:
        """
        执行重命名操作
        
        Args:
            request: 重命名请求DTO
            
        Yields:
            ProgressUpdate: 重命名进度更新
            
        Raises:
            ValueError: 当请求数据无效时
            PermissionError: 当没有重命名权限时
            FileNotFoundError: 当文件不存在时
        """
        pass
    
    @abstractmethod
    async def get_rename_result(self, task_id: str) -> Optional[RenameResult]:
        """
        获取重命名结果
        
        Args:
            task_id: 任务ID
            
        Returns:
            RenameResult: 重命名结果DTO，如果任务不存在返回None
        """
        pass
    
    @abstractmethod
    async def validate_rename_request(self, request: RenameRequest) -> List[str]:
        """
        验证重命名请求
        
        Args:
            request: 重命名请求DTO
            
        Returns:
            List[str]: 验证错误列表，空列表表示验证通过
        """
        pass
    
    @abstractmethod
    async def check_name_conflicts(self, previews: List[RenamePreview]) -> List[str]:
        """
        检查命名冲突
        
        Args:
            previews: 重命名预览列表
            
        Returns:
            List[str]: 冲突的文件路径列表
        """
        pass
    
    @abstractmethod
    async def resolve_conflicts(self, previews: List[RenamePreview], 
                              strategy: RenameConflictResolution) -> List[RenamePreview]:
        """
        解决命名冲突
        
        Args:
            previews: 重命名预览列表
            strategy: 冲突解决策略
            
        Returns:
            List[RenamePreview]: 解决冲突后的预览列表
        """
        pass
    
    @abstractmethod
    async def undo_rename(self, task_id: str) -> bool:
        """
        撤销重命名操作
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否撤销成功
        """
        pass
    
    @abstractmethod
    async def get_rename_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """
        获取重命名历史
        
        Args:
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 重命名历史记录
        """
        pass


class IRenameRuleService(IBaseService):
    """
    重命名规则服务接口
    
    负责重命名规则的管理和应用
    """
    
    @abstractmethod
    async def apply_rule(self, rule: RenameRule, file_name: str) -> str:
        """
        应用重命名规则
        
        Args:
            rule: 重命名规则
            file_name: 原文件名
            
        Returns:
            str: 新文件名
            
        Raises:
            ValueError: 当规则无效时
        """
        pass
    
    @abstractmethod
    async def apply_rules(self, rules: List[RenameRule], file_name: str) -> str:
        """
        应用多个重命名规则
        
        Args:
            rules: 重命名规则列表
            file_name: 原文件名
            
        Returns:
            str: 新文件名
        """
        pass
    
    @abstractmethod
    async def validate_rule(self, rule: RenameRule) -> List[str]:
        """
        验证重命名规则
        
        Args:
            rule: 重命名规则
            
        Returns:
            List[str]: 验证错误列表，空列表表示验证通过
        """
        pass
    
    @abstractmethod
    async def test_rule(self, rule: RenameRule, test_names: List[str]) -> Dict[str, str]:
        """
        测试重命名规则
        
        Args:
            rule: 重命名规则
            test_names: 测试文件名列表
            
        Returns:
            Dict[str, str]: 原文件名到新文件名的映射
        """
        pass
    
    @abstractmethod
    async def save_rule_template(self, name: str, rules: List[RenameRule], 
                               description: str = "") -> bool:
        """
        保存规则模板
        
        Args:
            name: 模板名称
            rules: 规则列表
            description: 模板描述
            
        Returns:
            bool: 是否保存成功
        """
        pass
    
    @abstractmethod
    async def load_rule_template(self, name: str) -> Optional[List[RenameRule]]:
        """
        加载规则模板
        
        Args:
            name: 模板名称
            
        Returns:
            List[RenameRule]: 规则列表，如果模板不存在返回None
        """
        pass
    
    @abstractmethod
    async def get_rule_templates(self) -> List[Dict[str, Any]]:
        """
        获取所有规则模板
        
        Returns:
            List[Dict[str, Any]]: 模板信息列表
        """
        pass
    
    @abstractmethod
    async def delete_rule_template(self, name: str) -> bool:
        """
        删除规则模板
        
        Args:
            name: 模板名称
            
        Returns:
            bool: 是否删除成功
        """
        pass


class IRenamePatternService(IBaseService):
    """
    重命名模式服务接口
    
    负责智能重命名模式的识别和建议
    """
    
    @abstractmethod
    async def detect_naming_pattern(self, file_names: List[str]) -> Optional[Dict[str, Any]]:
        """
        检测命名模式
        
        Args:
            file_names: 文件名列表
            
        Returns:
            Dict[str, Any]: 检测到的模式信息，如果没有模式返回None
        """
        pass
    
    @abstractmethod
    async def suggest_rename_rules(self, file_names: List[str]) -> List[RenameRule]:
        """
        建议重命名规则
        
        Args:
            file_names: 文件名列表
            
        Returns:
            List[RenameRule]: 建议的规则列表
        """
        pass
    
    @abstractmethod
    async def normalize_file_names(self, file_names: List[str]) -> Dict[str, str]:
        """
        标准化文件名
        
        Args:
            file_names: 文件名列表
            
        Returns:
            Dict[str, str]: 原文件名到标准化文件名的映射
        """
        pass
    
    @abstractmethod
    async def generate_sequential_names(self, base_name: str, count: int, 
                                      start_number: int = 1, digits: int = DEFAULT_COUNTER_DIGITS) -> List[str]:
        """
        生成序列化文件名
        
        Args:
            base_name: 基础名称
            count: 生成数量
            start_number: 起始数字
            digits: 数字位数
            
        Returns:
            List[str]: 生成的文件名列表
        """
        pass
    
    @abstractmethod
    async def extract_metadata_from_name(self, file_name: str) -> Dict[str, Any]:
        """
        从文件名提取元数据
        
        Args:
            file_name: 文件名
            
        Returns:
            Dict[str, Any]: 提取的元数据
        """
        pass
