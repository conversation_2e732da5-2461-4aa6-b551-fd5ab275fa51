#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
事件总线实现

提供事件发布、订阅、分发功能
遵循RULE-003: 事件驱动通信规范
"""

import asyncio
import logging
import threading
import time
from abc import ABC, abstractmethod
from collections import defaultdict, deque
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass
from typing import Callable, Dict, List, Optional, Any, Set
import weakref

from .event_definitions import EventData, EventPriority

# 常量定义
DEFAULT_MAX_WORKERS = 8
DEFAULT_SHUTDOWN_TIMEOUT = 5.0
DEFAULT_SLEEP_INTERVAL = 0.01
DEFAULT_BATCH_SLEEP_INTERVAL = 0.1
DEFAULT_STATS_CLEANUP_INTERVAL = 30.0

logger = logging.getLogger(__name__)


class IEventBus(ABC):
    """事件总线接口"""
    
    @abstractmethod
    def publish(self, event_type: str, event_data: EventData) -> None:
        """发布事件"""
        pass
    
    @abstractmethod
    def subscribe(self, event_type: str, handler: Callable[[EventData], None], 
                  subscriber_id: Optional[str] = None) -> str:
        """订阅事件，返回订阅ID"""
        pass
    
    @abstractmethod
    def unsubscribe(self, event_type: str, subscription_id: str) -> bool:
        """取消订阅"""
        pass
    
    @abstractmethod
    def unsubscribe_all(self, subscriber_id: str) -> int:
        """取消指定订阅者的所有订阅"""
        pass


@dataclass
class EventSubscription:
    """事件订阅信息"""
    subscription_id: str
    subscriber_id: Optional[str]
    handler: Callable[[EventData], None]
    event_type: str
    created_time: float
    is_async: bool = False
    
    def __hash__(self):
        return hash(self.subscription_id)


class EventBus(IEventBus):
    """
    事件总线实现
    
    特性:
    - 异步事件处理
    - 事件优先级支持
    - 订阅者管理
    - 错误处理和重试
    - 性能监控
    """
    
    def __init__(self, max_workers: int = DEFAULT_MAX_WORKERS, max_queue_size: int = 1000):
        self._subscriptions: Dict[str, Set[EventSubscription]] = defaultdict(set)
        self._subscriber_subscriptions: Dict[str, Set[str]] = defaultdict(set)
        self._event_queue: deque = deque(maxlen=max_queue_size)
        self._executor = ThreadPoolExecutor(max_workers=max_workers, 
                                          thread_name_prefix="event_worker")
        self._lock = threading.RLock()
        self._running = False
        self._worker_thread: Optional[threading.Thread] = None
        self._stats = {
            "events_published": 0,
            "events_processed": 0,
            "events_failed": 0,
            "subscriptions_count": 0
        }
        
    def start(self) -> None:
        """启动事件总线"""
        with self._lock:
            if self._running:
                return
            
            self._running = True
            self._worker_thread = threading.Thread(
                target=self._process_events,
                name="event_bus_worker",
                daemon=True
            )
            self._worker_thread.start()
            logger.info("事件总线已启动")
    
    def stop(self) -> None:
        """停止事件总线"""
        with self._lock:
            if not self._running:
                return
            
            self._running = False
            
        if self._worker_thread:
            self._worker_thread.join(timeout=DEFAULT_SHUTDOWN_TIMEOUT)
            
        self._executor.shutdown(wait=True)
        logger.info("事件总线已停止")
    
    def publish(self, event_type: str, event_data: EventData) -> None:
        """发布事件"""
        if not self._running:
            logger.warning(f"事件总线未运行，忽略事件: {event_type}")
            return
        
        try:
            # 添加到队列
            self._event_queue.append((event_type, event_data))
            self._stats["events_published"] += 1
            
            logger.debug(f"事件已发布: {event_type} (队列长度: {len(self._event_queue)})")
            
        except Exception as e:
            logger.error(f"发布事件失败: {event_type}, 错误: {e}")
            self._stats["events_failed"] += 1
    
    def subscribe(self, event_type: str, handler: Callable[[EventData], None], 
                  subscriber_id: Optional[str] = None) -> str:
        """订阅事件"""
        import uuid
        subscription_id = str(uuid.uuid4())
        
        # 检查处理器是否为异步
        is_async = asyncio.iscoroutinefunction(handler)
        
        subscription = EventSubscription(
            subscription_id=subscription_id,
            subscriber_id=subscriber_id,
            handler=handler,
            event_type=event_type,
            created_time=time.time(),
            is_async=is_async
        )
        
        with self._lock:
            self._subscriptions[event_type].add(subscription)
            if subscriber_id:
                self._subscriber_subscriptions[subscriber_id].add(subscription_id)
            self._stats["subscriptions_count"] += 1
        
        logger.debug(f"订阅事件: {event_type}, 订阅者: {subscriber_id}")
        return subscription_id
    
    def unsubscribe(self, event_type: str, subscription_id: str) -> bool:
        """取消订阅"""
        with self._lock:
            subscriptions = self._subscriptions.get(event_type, set())
            for subscription in list(subscriptions):
                if subscription.subscription_id == subscription_id:
                    subscriptions.remove(subscription)
                    
                    # 从订阅者映射中移除
                    if subscription.subscriber_id:
                        subscriber_subs = self._subscriber_subscriptions.get(
                            subscription.subscriber_id, set())
                        subscriber_subs.discard(subscription_id)
                    
                    self._stats["subscriptions_count"] -= 1
                    logger.debug(f"取消订阅: {event_type}, 订阅ID: {subscription_id}")
                    return True
        
        return False
    
    def unsubscribe_all(self, subscriber_id: str) -> int:
        """取消指定订阅者的所有订阅"""
        count = 0
        with self._lock:
            subscription_ids = list(self._subscriber_subscriptions.get(subscriber_id, set()))
            
            for subscription_id in subscription_ids:
                # 查找并移除订阅
                for event_type, subscriptions in self._subscriptions.items():
                    for subscription in list(subscriptions):
                        if subscription.subscription_id == subscription_id:
                            subscriptions.remove(subscription)
                            count += 1
                            break
            
            # 清空订阅者映射
            if subscriber_id in self._subscriber_subscriptions:
                del self._subscriber_subscriptions[subscriber_id]
            
            self._stats["subscriptions_count"] -= count
        
        logger.debug(f"取消订阅者所有订阅: {subscriber_id}, 数量: {count}")
        return count
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._lock:
            return {
                **self._stats,
                "queue_size": len(self._event_queue),
                "event_types": len(self._subscriptions),
                "is_running": self._running
            }
    
    def _process_events(self) -> None:
        """事件处理工作线程"""
        logger.info("事件处理线程已启动")
        
        while self._running:
            try:
                # 获取事件
                if not self._event_queue:
                    time.sleep(DEFAULT_SLEEP_INTERVAL)  # 10ms
                    continue
                
                event_type, event_data = self._event_queue.popleft()
                self._dispatch_event(event_type, event_data)
                
            except Exception as e:
                logger.error(f"事件处理线程错误: {e}")
                time.sleep(DEFAULT_BATCH_SLEEP_INTERVAL)
        
        logger.info("事件处理线程已停止")
    
    def _dispatch_event(self, event_type: str, event_data: EventData) -> None:
        """分发事件到订阅者"""
        with self._lock:
            subscriptions = list(self._subscriptions.get(event_type, set()))
        
        if not subscriptions:
            logger.debug(f"没有订阅者处理事件: {event_type}")
            return
        
        # 按优先级排序
        subscriptions.sort(key=lambda s: event_data.priority.value, reverse=True)
        
        for subscription in subscriptions:
            try:
                if subscription.is_async:
                    # 异步处理器
                    self._executor.submit(self._handle_async_event, subscription, event_data)
                else:
                    # 同步处理器
                    self._executor.submit(self._handle_sync_event, subscription, event_data)
                    
            except Exception as e:
                logger.error(f"分发事件失败: {event_type}, 订阅者: {subscription.subscriber_id}, 错误: {e}")
                self._stats["events_failed"] += 1
        
        self._stats["events_processed"] += 1
    
    def _handle_sync_event(self, subscription: EventSubscription, event_data: EventData) -> None:
        """处理同步事件"""
        try:
            start_time = time.time()
            subscription.handler(event_data)
            duration = time.time() - start_time
            
            if duration > 1.0:  # 超过1秒的处理时间
                logger.warning(f"事件处理耗时过长: {subscription.event_type}, "
                             f"订阅者: {subscription.subscriber_id}, 耗时: {duration:.2f}s")
                
        except Exception as e:
            logger.error(f"同步事件处理失败: {subscription.event_type}, "
                        f"订阅者: {subscription.subscriber_id}, 错误: {e}")
            self._stats["events_failed"] += 1
    
    def _handle_async_event(self, subscription: EventSubscription, event_data: EventData) -> None:
        """处理异步事件"""
        import asyncio  # 确保在方法内部导入

        try:
            # 创建新的事件循环来运行异步处理器
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                start_time = time.time()
                loop.run_until_complete(
                    asyncio.wait_for(subscription.handler(event_data), timeout=DEFAULT_STATS_CLEANUP_INTERVAL)
                )
                duration = time.time() - start_time
                
                if duration > 1.0:
                    logger.warning(f"异步事件处理耗时过长: {subscription.event_type}, "
                                 f"订阅者: {subscription.subscriber_id}, 耗时: {duration:.2f}s")
                    
            finally:
                loop.close()
                
        except asyncio.TimeoutError:
            logger.error(f"异步事件处理超时: {subscription.event_type}, "
                        f"订阅者: {subscription.subscriber_id}")
            self._stats["events_failed"] += 1
        except Exception as e:
            logger.error(f"异步事件处理失败: {subscription.event_type}, "
                        f"订阅者: {subscription.subscriber_id}, 错误: {e}")
            self._stats["events_failed"] += 1
