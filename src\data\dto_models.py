#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据传输对象(DTO)模型定义

该模块定义了系统中各模块间通信使用的数据传输对象，
替换原有的多参数传递方式，提供类型安全和结构化的数据交换。

作者: AI助手
日期: 2023-06-01
版本: 2.0.0
"""

from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime
from enum import Enum
import uuid


class TaskType(Enum):
    """任务类型枚举"""
    FILE_SCAN = "file_scan"
    DUPLICATE_DETECTION = "duplicate_detection"
    JUNK_CLEANUP = "junk_cleanup"
    RENAME_OPERATION = "rename_operation"
    WHITELIST_CHECK = "whitelist_check"
    FILE_OPERATION = "file_operation"


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class OperationType(Enum):
    """操作类型枚举"""
    MOVE = "move"
    DELETE = "delete"
    RENAME = "rename"
    COPY = "copy"


@dataclass
class ProgressUpdate:
    """进度更新DTO"""
    task_id: str
    progress: float  # 0.0 - 100.0
    status_message: str
    current_item: Optional[str] = None
    total_items: Optional[int] = None
    processed_items: Optional[int] = None
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "task_id": self.task_id,
            "progress": self.progress,
            "status_message": self.status_message,
            "current_item": self.current_item,
            "total_items": self.total_items,
            "processed_items": self.processed_items,
            "timestamp": self.timestamp.isoformat()
        }


@dataclass
class ScanRequest:
    """文件扫描请求DTO"""
    task_id: str
    directories: List[str]
    recursive: bool = True
    update_database: bool = True
    include_hidden: bool = False
    file_extensions: Optional[List[str]] = None
    min_file_size: int = 0
    max_file_size: Optional[int] = None
    interrupt_event: Optional[Any] = None
    progress_callback: Optional[Any] = None
    
    def __post_init__(self):
        if not self.task_id:
            self.task_id = f"scan_{uuid.uuid4().hex[:8]}"


@dataclass
class ScanResult:
    """文件扫描结果DTO"""
    task_id: str
    success: bool
    total_files: int = 0
    processed_files: int = 0
    video_files: int = 0
    junk_files: int = 0
    whitelist_files: int = 0
    total_size: int = 0
    scan_time: float = 0.0
    error_count: int = 0
    errors: List[str] = field(default_factory=list)
    file_types: Dict[str, int] = field(default_factory=dict)
    directories_scanned: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "task_id": self.task_id,
            "success": self.success,
            "total_files": self.total_files,
            "processed_files": self.processed_files,
            "video_files": self.video_files,
            "junk_files": self.junk_files,
            "whitelist_files": self.whitelist_files,
            "total_size": self.total_size,
            "scan_time": self.scan_time,
            "error_count": self.error_count,
            "errors": self.errors,
            "file_types": self.file_types,
            "directories_scanned": self.directories_scanned
        }


@dataclass
class DuplicateDetectionRequest:
    """重复文件检测请求DTO"""
    task_id: str
    directories: Optional[List[str]] = None
    min_file_size: int = 1024
    file_extensions: Optional[List[str]] = None
    use_database: bool = True
    algorithm: str = "md5"  # md5, sha1, sha256
    progress_callback: Optional[Any] = None
    
    def __post_init__(self):
        if not self.task_id:
            self.task_id = f"duplicate_{uuid.uuid4().hex[:8]}"


@dataclass
class DuplicateGroup:
    """重复文件组DTO"""
    hash_value: str
    file_count: int
    total_size: int
    files: List[Dict[str, Any]]
    
    def get_largest_file(self) -> Optional[Dict[str, Any]]:
        """获取组中最大的文件"""
        if not self.files:
            return None
        return max(self.files, key=lambda f: f.get('size', 0))
    
    def get_oldest_file(self) -> Optional[Dict[str, Any]]:
        """获取组中最旧的文件"""
        if not self.files:
            return None
        return min(self.files, key=lambda f: f.get('modified_time', ''))


@dataclass
class DuplicateDetectionResult:
    """重复文件检测结果DTO"""
    task_id: str
    success: bool
    duplicate_groups: List[DuplicateGroup] = field(default_factory=list)
    total_duplicates: int = 0
    total_duplicate_size: int = 0
    potential_savings: int = 0
    detection_time: float = 0.0
    errors: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "task_id": self.task_id,
            "success": self.success,
            "duplicate_groups": [
                {
                    "hash": group.hash_value,
                    "count": group.file_count,
                    "size": group.total_size,
                    "files": group.files
                } for group in self.duplicate_groups
            ],
            "total_duplicates": self.total_duplicates,
            "total_duplicate_size": self.total_duplicate_size,
            "potential_savings": self.potential_savings,
            "detection_time": self.detection_time,
            "errors": self.errors
        }


@dataclass
class RenameRequest:
    """重命名请求DTO"""
    task_id: str
    files: List[Dict[str, str]]  # [{"old_path": "", "new_path": ""}]
    backup: bool = True
    dry_run: bool = False
    progress_callback: Optional[Any] = None
    
    def __post_init__(self):
        if not self.task_id:
            self.task_id = f"rename_{uuid.uuid4().hex[:8]}"


@dataclass
class RenameResult:
    """重命名结果DTO"""
    task_id: str
    success: bool
    total_files: int = 0
    renamed_files: int = 0
    failed_files: int = 0
    errors: List[str] = field(default_factory=list)
    renamed_list: List[Dict[str, str]] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "task_id": self.task_id,
            "success": self.success,
            "total_files": self.total_files,
            "renamed_files": self.renamed_files,
            "failed_files": self.failed_files,
            "errors": self.errors,
            "renamed_list": self.renamed_list
        }


@dataclass
class FileOperationRequest:
    """文件操作请求DTO"""
    task_id: str
    operation: OperationType
    files: List[str]
    target_directory: Optional[str] = None
    new_names: Optional[List[str]] = None
    backup: bool = True
    confirm_overwrite: bool = False
    progress_callback: Optional[Any] = None
    
    def __post_init__(self):
        if not self.task_id:
            self.task_id = f"fileop_{uuid.uuid4().hex[:8]}"


@dataclass
class FileOperationResult:
    """文件操作结果DTO"""
    task_id: str
    operation: OperationType
    success: bool
    total_files: int = 0
    processed_files: int = 0
    failed_files: int = 0
    errors: List[str] = field(default_factory=list)
    results: List[Dict[str, Any]] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "task_id": self.task_id,
            "operation": self.operation.value,
            "success": self.success,
            "total_files": self.total_files,
            "processed_files": self.processed_files,
            "failed_files": self.failed_files,
            "errors": self.errors,
            "results": self.results
        }


@dataclass
class UIEvent:
    """UI事件DTO"""
    event_type: str
    source_component: str
    data: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "event_type": self.event_type,
            "source_component": self.source_component,
            "data": self.data,
            "timestamp": self.timestamp.isoformat()
        }


@dataclass
class ViewState:
    """视图状态DTO"""
    component_id: str
    visible: bool = True
    enabled: bool = True
    data: Dict[str, Any] = field(default_factory=dict)
    filters: Dict[str, Any] = field(default_factory=dict)
    sort_config: Optional[Dict[str, Any]] = None
    selection: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "component_id": self.component_id,
            "visible": self.visible,
            "enabled": self.enabled,
            "data": self.data,
            "filters": self.filters,
            "sort_config": self.sort_config,
            "selection": self.selection
        }


@dataclass
class PanelConfig:
    """面板配置DTO"""
    panel_id: str
    title: str
    enabled: bool = True
    visible: bool = True
    position: Tuple[int, int] = (0, 0)
    size: Tuple[int, int] = (400, 300)
    settings: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "panel_id": self.panel_id,
            "title": self.title,
            "enabled": self.enabled,
            "visible": self.visible,
            "position": self.position,
            "size": self.size,
            "settings": self.settings
        }
