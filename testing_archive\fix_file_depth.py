#!/usr/bin/env python3
"""
修复数据库中文件深度的脚本
"""

import sys
import os
import logging

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def setup_logger():
    """设置日志器"""
    logger = logging.getLogger("DepthFixer")
    logger.setLevel(logging.INFO)
    
    # 清除现有处理器
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    formatter = logging.Formatter('[%(levelname)s] %(message)s')
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    return logger

def calculate_correct_depth(path):
    """计算正确的文件深度"""
    if not path:
        return 1
    
    # 规范化路径
    normalized_path = path.replace('\\', '/')
    
    # 移除开头和结尾的斜杠
    if normalized_path.startswith('/'):
        normalized_path = normalized_path[1:]
    if normalized_path.endswith('/'):
        normalized_path = normalized_path[:-1]
    
    # 分割路径
    parts = [part for part in normalized_path.split('/') if part]
    
    # 深度 = 路径部分数量
    depth = len(parts)
    
    # 最小深度为1（根目录）
    return max(1, depth)

def fix_file_depths():
    """修复文件深度"""
    logger = setup_logger()
    
    try:
        # 导入数据库管理器
        from src.data.db_manager import MongoDBManager
        
        # 创建数据库连接
        db_manager = MongoDBManager()
        
        logger.info("🔧 开始修复文件深度...")
        
        # 获取所有文件
        all_files = list(db_manager.collection.find({}))
        total_files = len(all_files)
        
        logger.info(f"📊 找到 {total_files} 个文件记录")
        
        if total_files == 0:
            logger.info("✅ 没有文件需要修复")
            return True
        
        # 统计需要修复的文件
        files_to_fix = []
        
        for file_doc in all_files:
            file_path = file_doc.get('path', '')
            current_depth = file_doc.get('depth', 1)
            correct_depth = calculate_correct_depth(file_path)
            
            if current_depth != correct_depth:
                files_to_fix.append({
                    '_id': file_doc['_id'],
                    'path': file_path,
                    'current_depth': current_depth,
                    'correct_depth': correct_depth
                })
        
        logger.info(f"📋 需要修复深度的文件: {len(files_to_fix)} 个")
        
        if len(files_to_fix) == 0:
            logger.info("✅ 所有文件深度都正确，无需修复")
            return True
        
        # 显示修复预览
        logger.info("🔍 修复预览（前10个）:")
        for i, file_info in enumerate(files_to_fix[:10]):
            logger.info(f"   {i+1}. {file_info['path']}")
            logger.info(f"      当前深度: {file_info['current_depth']} -> 正确深度: {file_info['correct_depth']}")
        
        if len(files_to_fix) > 10:
            logger.info(f"   ... 还有 {len(files_to_fix) - 10} 个文件需要修复")
        
        # 确认修复
        response = input("\n是否继续修复？(y/N): ").strip().lower()
        if response != 'y':
            logger.info("❌ 用户取消修复")
            return False
        
        # 执行修复
        logger.info("🚀 开始执行修复...")
        
        fixed_count = 0
        error_count = 0
        
        for i, file_info in enumerate(files_to_fix):
            try:
                # 更新深度
                result = db_manager.collection.update_one(
                    {'_id': file_info['_id']},
                    {'$set': {'depth': file_info['correct_depth']}}
                )
                
                if result.modified_count > 0:
                    fixed_count += 1
                
                # 显示进度
                if (i + 1) % 100 == 0 or (i + 1) == len(files_to_fix):
                    progress = (i + 1) / len(files_to_fix) * 100
                    logger.info(f"📈 修复进度: {progress:.1f}% ({i + 1}/{len(files_to_fix)})")
                
            except Exception as e:
                logger.error(f"❌ 修复文件失败 {file_info['path']}: {e}")
                error_count += 1
        
        # 修复完成
        logger.info("✅ 修复完成！")
        logger.info(f"📊 修复统计:")
        logger.info(f"   成功修复: {fixed_count} 个文件")
        logger.info(f"   修复失败: {error_count} 个文件")
        logger.info(f"   总计处理: {len(files_to_fix)} 个文件")
        
        # 验证修复结果
        logger.info("🔍 验证修复结果...")
        
        verification_errors = []
        for file_info in files_to_fix[:10]:  # 验证前10个
            updated_doc = db_manager.collection.find_one({'_id': file_info['_id']})
            if updated_doc:
                new_depth = updated_doc.get('depth', 1)
                if new_depth != file_info['correct_depth']:
                    verification_errors.append(f"{file_info['path']}: 期望深度 {file_info['correct_depth']}, 实际深度 {new_depth}")
        
        if verification_errors:
            logger.warning("⚠️  验证发现问题:")
            for error in verification_errors:
                logger.warning(f"   {error}")
        else:
            logger.info("✅ 验证通过，所有文件深度已正确修复")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 修复过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_depth_calculation():
    """测试深度计算功能"""
    logger = setup_logger()
    
    logger.info("🧪 测试深度计算功能")
    
    test_cases = [
        ("E:", 1),
        ("E:/", 1),
        ("E:/新建文件夹 (2)", 2),
        ("E:/新建文件夹 (2)/", 2),
        ("E:/新建文件夹 (2)/附件1.rar", 3),
        ("C:/Program Files/App/config.ini", 4),
        ("", 1),
        ("/", 1),
        ("folder", 1),
        ("folder/subfolder", 2),
        ("folder/subfolder/file.txt", 3)
    ]
    
    all_correct = True
    
    for path, expected_depth in test_cases:
        actual_depth = calculate_correct_depth(path)
        status = "✅" if actual_depth == expected_depth else "❌"
        
        if actual_depth != expected_depth:
            all_correct = False
        
        logger.info(f"   {status} '{path}' -> 深度: {actual_depth} (期望: {expected_depth})")
    
    if all_correct:
        logger.info("✅ 所有测试用例通过")
    else:
        logger.error("❌ 部分测试用例失败")
    
    return all_correct

def main():
    """主函数"""
    logger = setup_logger()
    
    logger.info("🚀 文件深度修复工具")
    logger.info("=" * 50)
    
    try:
        # 测试深度计算
        logger.info("1. 测试深度计算功能...")
        if not test_depth_calculation():
            logger.error("❌ 深度计算测试失败，停止修复")
            return False
        
        logger.info("\n2. 修复数据库中的文件深度...")
        success = fix_file_depths()
        
        if success:
            logger.info("\n🎉 文件深度修复完成！")
            logger.info("💡 现在可以重新扫描文件，文件树应该能正确显示层级结构")
        else:
            logger.error("\n❌ 文件深度修复失败")
        
        return success
        
    except KeyboardInterrupt:
        logger.info("\n⚠️  用户中断修复")
        return False
    except Exception as e:
        logger.error(f"\n💥 修复过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✨ 修复完成！请重新扫描文件以查看效果。")
    else:
        print("\n❌ 修复失败，请检查错误信息。")
