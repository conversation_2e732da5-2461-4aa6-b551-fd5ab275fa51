#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
配置管理器

该模块实现配置文件的管理和读取:
- ConfigManager: 配置管理类
- 支持YAML配置文件
- 提供默认配置和配置验证
- 支持热重载配置

作者: AI助手
日期: 2024-01-01
版本: 2.0.0
"""

import os
import yaml
from typing import Dict, Any, Optional
from pathlib import Path
import time
from src.utils.logger import get_logger


class ConfigManager:
    """
    配置管理器
    
    管理应用程序的配置文件和参数
    """

    def __init__(self, config_file: str = None):
        """
        初始化配置管理器
        
        参数:
            config_file: 配置文件路径
        """
        self.logger = get_logger("ConfigManager")
        
        # 默认配置文件路径
        if config_file is None:
            config_file = os.path.join(
                os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                "config", "tree_optimization.yaml"
            )
        
        self.config_file = config_file
        self.config_data = {}
        self.last_modified = 0
        
        # 默认配置
        self.default_config = {
            "tree_optimization": {
                "max_depth": 10,
                "batch_size": 1000,
                "cache_size": 10000,
                "network_timeout": 30,
                "virtual_node_threshold": 100,
                "enable_dual_verification": True,
                "enable_network_folders": True,
                "auto_refresh_interval": 300,  # 5分钟
                "performance_monitoring": True
            },
            "database": {
                "connection_pool_size": 10,
                "query_timeout": 30,
                "retry_count": 3,
                "retry_delay": 1.0,
                "enable_indexing": True,
                "batch_operation_size": 1000
            },
            "logging": {
                "level": "INFO",
                "file_path": "logs/tree_optimization.log",
                "max_file_size": "10MB",
                "backup_count": 5,
                "enable_console": True
            },
            "performance": {
                "thread_pool_size": 4,
                "async_batch_size": 100,
                "memory_limit_mb": 512,
                "enable_profiling": False,
                "profile_output_dir": "profiles"
            },
            "ui": {
                "tree_node_limit": 1000,
                "lazy_loading": True,
                "expand_depth": 2,
                "show_file_count": True,
                "show_folder_size": True,
                "refresh_interval": 60  # 1分钟
            }
        }
        
        # 加载配置
        self._load_config()

    def _load_config(self) -> bool:
        """
        加载配置文件
        
        返回:
            bool: 是否成功加载
        """
        try:
            # 检查配置文件是否存在
            if not os.path.exists(self.config_file):
                self.logger.info(f"配置文件不存在，创建默认配置: {self.config_file}")
                self._create_default_config()
                return True
            
            # 检查文件修改时间
            current_modified = os.path.getmtime(self.config_file)
            if current_modified <= self.last_modified:
                return True  # 文件未修改
            
            # 读取配置文件
            with open(self.config_file, 'r', encoding='utf-8') as f:
                loaded_config = yaml.safe_load(f)
            
            if loaded_config:
                # 合并配置（默认配置 + 用户配置）
                self.config_data = self._merge_config(self.default_config, loaded_config)
                self.last_modified = current_modified
                self.logger.info(f"配置文件加载成功: {self.config_file}")
            else:
                self.logger.warning(f"配置文件为空，使用默认配置: {self.config_file}")
                self.config_data = self.default_config.copy()
            
            # 验证配置
            self._validate_config()
            
            return True
            
        except yaml.YAMLError as e:
            self.logger.error(f"配置文件格式错误: {e}")
            self.config_data = self.default_config.copy()
            return False
            
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            self.config_data = self.default_config.copy()
            return False

    def _create_default_config(self) -> bool:
        """
        创建默认配置文件
        
        返回:
            bool: 是否成功创建
        """
        try:
            # 确保配置目录存在
            config_dir = os.path.dirname(self.config_file)
            os.makedirs(config_dir, exist_ok=True)
            
            # 写入默认配置
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(self.default_config, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
            
            self.config_data = self.default_config.copy()
            self.last_modified = os.path.getmtime(self.config_file)
            
            self.logger.info(f"默认配置文件创建成功: {self.config_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"创建默认配置文件失败: {e}")
            self.config_data = self.default_config.copy()
            return False

    def _merge_config(self, default: Dict[str, Any], user: Dict[str, Any]) -> Dict[str, Any]:
        """
        合并配置（递归合并字典）
        
        参数:
            default: 默认配置
            user: 用户配置
        返回:
            Dict[str, Any]: 合并后的配置
        """
        result = default.copy()
        
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_config(result[key], value)
            else:
                result[key] = value
        
        return result

    def _validate_config(self) -> bool:
        """
        验证配置参数
        
        返回:
            bool: 配置是否有效
        """
        try:
            # 验证数值范围
            validations = [
                ("tree_optimization.max_depth", 1, 50),
                ("tree_optimization.batch_size", 100, 10000),
                ("tree_optimization.cache_size", 1000, 100000),
                ("tree_optimization.network_timeout", 5, 300),
                ("tree_optimization.virtual_node_threshold", 10, 10000),
                ("database.connection_pool_size", 1, 100),
                ("database.query_timeout", 5, 300),
                ("database.retry_count", 1, 10),
                ("performance.thread_pool_size", 1, 20),
                ("performance.async_batch_size", 10, 1000),
                ("performance.memory_limit_mb", 64, 4096),
                ("ui.tree_node_limit", 100, 10000),
                ("ui.expand_depth", 1, 10)
            ]
            
            for path, min_val, max_val in validations:
                value = self.get(path)
                if value is not None and (value < min_val or value > max_val):
                    self.logger.warning(
                        f"配置参数超出范围 {path}={value}, 应在 [{min_val}, {max_val}] 范围内"
                    )
                    # 使用默认值
                    self._set_nested_value(path, self._get_nested_value(self.default_config, path))
            
            # 验证布尔值
            bool_configs = [
                "tree_optimization.enable_dual_verification",
                "tree_optimization.enable_network_folders",
                "tree_optimization.performance_monitoring",
                "database.enable_indexing",
                "logging.enable_console",
                "performance.enable_profiling",
                "ui.lazy_loading",
                "ui.show_file_count",
                "ui.show_folder_size"
            ]
            
            for path in bool_configs:
                value = self.get(path)
                if value is not None and not isinstance(value, bool):
                    self.logger.warning(f"配置参数类型错误 {path}={value}, 应为布尔值")
                    # 使用默认值
                    self._set_nested_value(path, self._get_nested_value(self.default_config, path))
            
            return True
            
        except Exception as e:
            self.logger.error(f"配置验证失败: {e}")
            return False

    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值（支持嵌套键，如 'tree_optimization.max_depth'）
        
        参数:
            key: 配置键
            default: 默认值
        返回:
            Any: 配置值
        """
        try:
            # 检查是否需要重新加载配置
            self._check_reload()
            
            return self._get_nested_value(self.config_data, key, default)
            
        except Exception as e:
            self.logger.warning(f"获取配置失败 {key}: {e}")
            return default

    def set(self, key: str, value: Any) -> bool:
        """
        设置配置值
        
        参数:
            key: 配置键
            value: 配置值
        返回:
            bool: 是否成功
        """
        try:
            self._set_nested_value(key, value)
            return True
            
        except Exception as e:
            self.logger.error(f"设置配置失败 {key}={value}: {e}")
            return False

    def save(self) -> bool:
        """
        保存配置到文件
        
        返回:
            bool: 是否成功
        """
        try:
            # 确保配置目录存在
            config_dir = os.path.dirname(self.config_file)
            os.makedirs(config_dir, exist_ok=True)
            
            # 写入配置文件
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(self.config_data, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
            
            self.last_modified = os.path.getmtime(self.config_file)
            self.logger.info(f"配置文件保存成功: {self.config_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {e}")
            return False

    def reload(self) -> bool:
        """
        重新加载配置文件
        
        返回:
            bool: 是否成功
        """
        self.last_modified = 0  # 强制重新加载
        return self._load_config()

    def get_section(self, section: str) -> Dict[str, Any]:
        """
        获取配置节
        
        参数:
            section: 节名称
        返回:
            Dict[str, Any]: 配置节内容
        """
        try:
            self._check_reload()
            return self.config_data.get(section, {})
            
        except Exception as e:
            self.logger.warning(f"获取配置节失败 {section}: {e}")
            return {}

    def _check_reload(self):
        """
        检查是否需要重新加载配置
        """
        try:
            if os.path.exists(self.config_file):
                current_modified = os.path.getmtime(self.config_file)
                if current_modified > self.last_modified:
                    self.logger.info("检测到配置文件变化，重新加载")
                    self._load_config()
        except Exception as e:
            self.logger.warning(f"检查配置文件修改时间失败: {e}")

    def _get_nested_value(self, data: Dict[str, Any], key: str, default: Any = None) -> Any:
        """
        获取嵌套字典的值
        
        参数:
            data: 数据字典
            key: 嵌套键（用.分隔）
            default: 默认值
        返回:
            Any: 值
        """
        keys = key.split('.')
        current = data
        
        for k in keys:
            if isinstance(current, dict) and k in current:
                current = current[k]
            else:
                return default
        
        return current

    def _set_nested_value(self, key: str, value: Any):
        """
        设置嵌套字典的值
        
        参数:
            key: 嵌套键（用.分隔）
            value: 值
        """
        keys = key.split('.')
        current = self.config_data
        
        # 导航到最后一级的父字典
        for k in keys[:-1]:
            if k not in current:
                current[k] = {}
            current = current[k]
        
        # 设置值
        current[keys[-1]] = value

    def get_tree_optimization_config(self) -> Dict[str, Any]:
        """
        获取文件树优化配置
        
        返回:
            Dict[str, Any]: 文件树优化配置
        """
        return self.get_section("tree_optimization")

    def get_database_config(self) -> Dict[str, Any]:
        """
        获取数据库配置
        
        返回:
            Dict[str, Any]: 数据库配置
        """
        return self.get_section("database")

    def get_performance_config(self) -> Dict[str, Any]:
        """
        获取性能配置
        
        返回:
            Dict[str, Any]: 性能配置
        """
        return self.get_section("performance")

    def get_ui_config(self) -> Dict[str, Any]:
        """
        获取UI配置
        
        返回:
            Dict[str, Any]: UI配置
        """
        return self.get_section("ui")

    def is_feature_enabled(self, feature: str) -> bool:
        """
        检查功能是否启用
        
        参数:
            feature: 功能名称
        返回:
            bool: 是否启用
        """
        feature_map = {
            "dual_verification": "tree_optimization.enable_dual_verification",
            "network_folders": "tree_optimization.enable_network_folders",
            "performance_monitoring": "tree_optimization.performance_monitoring",
            "indexing": "database.enable_indexing",
            "profiling": "performance.enable_profiling",
            "lazy_loading": "ui.lazy_loading"
        }
        
        if feature in feature_map:
            return self.get(feature_map[feature], False)
        
        return False

    def get_config_summary(self) -> Dict[str, Any]:
        """
        获取配置摘要
        
        返回:
            Dict[str, Any]: 配置摘要
        """
        try:
            return {
                "config_file": self.config_file,
                "last_modified": time.ctime(self.last_modified) if self.last_modified else "未知",
                "sections": list(self.config_data.keys()),
                "tree_optimization": {
                    "max_depth": self.get("tree_optimization.max_depth"),
                    "batch_size": self.get("tree_optimization.batch_size"),
                    "cache_size": self.get("tree_optimization.cache_size"),
                    "network_timeout": self.get("tree_optimization.network_timeout")
                },
                "features_enabled": {
                    "dual_verification": self.is_feature_enabled("dual_verification"),
                    "network_folders": self.is_feature_enabled("network_folders"),
                    "performance_monitoring": self.is_feature_enabled("performance_monitoring"),
                    "lazy_loading": self.is_feature_enabled("lazy_loading")
                }
            }
            
        except Exception as e:
            self.logger.error(f"获取配置摘要失败: {e}")
            return {}


# 全局配置管理器实例
_config_manager = None


def get_config_manager(config_file: str = None) -> ConfigManager:
    """
    获取全局配置管理器实例
    
    参数:
        config_file: 配置文件路径
    返回:
        ConfigManager: 配置管理器实例
    """
    global _config_manager
    
    if _config_manager is None:
        _config_manager = ConfigManager(config_file)
    
    return _config_manager