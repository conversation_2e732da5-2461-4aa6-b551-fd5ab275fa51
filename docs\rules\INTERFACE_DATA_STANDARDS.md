# 接口数据传递规范

## 📋 **规范概述**

本规范定义了SmartFileManager项目中方法间接口数据传递的标准，旨在提升类型安全性、可维护性和开发效率。

## 🎯 **核心原则**

### **INTERFACE-001: 强类型接口原则**
**优先级**: 🔴 最高

**规则描述**:
- ✅ **必须**: 所有公共方法的返回值必须使用强类型DTO对象
- ✅ **必须**: 复杂数据结构必须定义专用的DTO类
- ✅ **必须**: 所有DTO类必须提供完整的类型注解
- ❌ **禁止**: 返回原始的dict、list、tuple等弱类型数据
- ❌ **禁止**: 使用Any类型作为返回值类型注解

### **INTERFACE-002: DTO设计标准**
**优先级**: 🔴 最高

**规则描述**:
- ✅ **必须**: 接口DTO类必须继承BaseDTO
- ✅ **必须**: 使用@dataclass装饰器定义DTO
- ✅ **必须**: 提供to_dict()和from_dict()方法
- ✅ **必须**: 包含完整的文档字符串
- ✅ **推荐**: 使用frozen=True保证不可变性（除非需要修改）

## 📊 **DTO分类标准**

### **信息查询DTO (Info DTOs)**
用于返回查询信息，通常是只读数据。

**命名约定**: `*Info`
**特征**: 
- frozen=True（不可变）
- 包含查询结果数据
- 提供格式化方法

**示例**:
```python
@dataclass(frozen=True)
class DirectoryInfo(BaseDTO):
    """目录信息DTO"""
    path: str
    file_count: int
    total_size: int
    exists: bool
    readable: bool
    last_modified: float
    error_message: Optional[str] = None
    
    def validate(self) -> List[str]:
        errors = []
        if not self.path:
            errors.append("路径不能为空")
        return errors
    
    def format_size(self) -> str:
        """格式化文件大小显示"""
        if self.total_size < 1024:
            return f"{self.total_size} B"
        elif self.total_size < 1024 * 1024:
            return f"{self.total_size / 1024:.1f} KB"
        else:
            return f"{self.total_size / (1024 * 1024):.1f} MB"
```

### **统计数据DTO (Statistics DTOs)**
用于返回统计和分析数据。

**命名约定**: `*Statistics`
**特征**:
- frozen=True（不可变）
- 包含数值统计数据
- 提供计算属性

**示例**:
```python
@dataclass(frozen=True)
class DetectionStatistics(BaseDTO):
    """检测统计信息DTO"""
    task_id: str
    total_files_checked: int
    total_duplicates_found: int
    total_space_wasted: int
    duplicate_groups_count: int
    check_duration: float
    average_group_size: float
    largest_group_size: int
    
    @property
    def duplicate_rate(self) -> float:
        """重复文件比率"""
        if self.total_files_checked == 0:
            return 0.0
        return self.total_duplicates_found / self.total_files_checked
    
    @property
    def space_efficiency(self) -> float:
        """空间效率（可节省的空间比例）"""
        if self.total_space_wasted == 0:
            return 1.0
        return 1.0 - (self.total_space_wasted / (self.total_space_wasted + 1))
```

### **集合数据DTO (Collection DTOs)**
用于返回对象集合。

**命名约定**: `*Collection`
**特征**:
- 包含类型化的列表
- 提供集合操作方法
- 支持迭代和索引

**示例**:
```python
@dataclass
class FileCollection(BaseDTO):
    """文件集合DTO"""
    files: List[FileInfo]
    total_count: int
    total_size: int
    collection_time: float = field(default_factory=time.time)
    
    def filter_by_extension(self, extensions: List[str]) -> 'FileCollection':
        """按扩展名过滤文件"""
        filtered_files = [f for f in self.files if f.extension in extensions]
        return FileCollection(
            files=filtered_files,
            total_count=len(filtered_files),
            total_size=sum(f.size for f in filtered_files)
        )
    
    def sort_by_size(self, descending: bool = True) -> 'FileCollection':
        """按大小排序"""
        sorted_files = sorted(self.files, key=lambda f: f.size, reverse=descending)
        return FileCollection(
            files=sorted_files,
            total_count=self.total_count,
            total_size=self.total_size
        )
```

## 🔄 **迁移策略**

### **阶段1: 创建新DTO类**
1. 分析现有返回dict的方法
2. 为每种数据结构创建对应的DTO类
3. 确保DTO类遵循设计标准

### **阶段2: 更新服务实现**
1. 修改服务方法返回新的DTO对象
2. 更新方法的类型注解
3. 确保向后兼容性

### **阶段3: 更新适配器层**
1. 修改适配器方法转换DTO到dict
2. 保持旧接口的兼容性
3. 添加弃用警告

### **阶段4: 更新调用方**
1. 逐步更新调用方使用新接口
2. 移除对旧dict接口的依赖
3. 完成迁移后移除适配器转换

## 📝 **实施检查清单**

### **DTO设计检查**
- [ ] 继承BaseDTO基类
- [ ] 使用@dataclass装饰器
- [ ] 提供完整的类型注解
- [ ] 实现validate()方法
- [ ] 包含详细的文档字符串
- [ ] 提供有用的计算属性或方法

### **接口更新检查**
- [ ] 更新方法返回类型注解
- [ ] 修改方法实现返回DTO对象
- [ ] 更新方法文档字符串
- [ ] 确保向后兼容性
- [ ] 添加单元测试验证

### **质量保证检查**
- [ ] 通过代码检查器验证
- [ ] 所有测试用例通过
- [ ] 性能无显著下降
- [ ] 文档更新完整

## 🎯 **预期收益**

### **开发效率提升**
- ✅ IDE智能提示和自动补全
- ✅ 编译时类型检查
- ✅ 更好的代码可读性
- ✅ 减少运行时错误

### **代码质量提升**
- ✅ 强类型安全保障
- ✅ 接口稳定性增强
- ✅ 更容易的重构和维护
- ✅ 更好的测试覆盖

### **团队协作改善**
- ✅ 清晰的接口契约
- ✅ 自文档化的代码
- ✅ 减少沟通成本
- ✅ 统一的开发标准

## 📚 **参考资源**

- [BaseDTO基类文档](../src/data/dto/base_dto.py)
- [DTO设计模式指南](./DTO_DESIGN_PATTERNS.md)
- [代码检查规则](./CODING_RULES.md)
- [重构指南](../guidelines/REFACTORING_GUIDE.md)

---

**制定日期**: 2025-01-29
**版本**: 1.0.0
**状态**: 生效中
