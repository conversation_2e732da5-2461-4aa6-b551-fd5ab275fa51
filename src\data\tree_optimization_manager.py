#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
文件树优化管理器

该模块实现文件树优化的核心算法:
- TreeOptimizationManager: 文件树优化管理类
- 支持深度优先构建、智能展开、虚拟节点
- 实现双重验证机制（内容哈希+总大小）
- 支持网络文件夹处理

作者: AI助手
日期: 2024-01-01
版本: 2.0.0
"""

import asyncio
import hashlib
import os
from typing import Dict, Any, Optional, List, Set, Tuple
from datetime import datetime
from pathlib import Path
import time
from concurrent.futures import ThreadPoolExecutor
from src.utils.logger import get_logger
from src.data.models import FolderInfo, TreeNodeInfo, FileInfo


class TreeOptimizationManager:
    """
    文件树优化管理器
    
    实现文件树的高效构建、更新和管理
    """

    def __init__(self, db_manager, config_manager=None):
        """
        初始化文件树优化管理器
        
        参数:
            db_manager: 数据库管理器实例
            config_manager: 配置管理器实例
        """
        self.db_manager = db_manager
        self.config_manager = config_manager
        self.logger = get_logger("TreeOptimizationManager")
        
        # 默认配置
        self.max_depth = self._get_config("max_depth", 10)
        self.batch_size = self._get_config("batch_size", 1000)
        self.cache_size = self._get_config("cache_size", 10000)
        self.network_timeout = self._get_config("network_timeout", 30)
        self.virtual_node_threshold = self._get_config("virtual_node_threshold", 100)
        
        # 缓存
        self.folder_cache = {}
        self.tree_node_cache = {}
        self.content_hash_cache = {}
        
        # 网络文件夹处理器
        self.network_handler = NetworkFolderHandler(self.network_timeout)
        
        # 线程池
        self.executor = ThreadPoolExecutor(max_workers=4)

    def _get_config(self, key: str, default: Any) -> Any:
        """
        获取配置值
        
        参数:
            key: 配置键
            default: 默认值
        返回:
            Any: 配置值
        """
        if self.config_manager:
            return self.config_manager.get(f"tree_optimization.{key}", default)
        return default

    async def build_optimized_tree(self, root_paths: List[str], 
                                 progress_callback=None) -> Dict[str, Any]:
        """
        构建优化的文件树
        
        参数:
            root_paths: 根路径列表
            progress_callback: 进度回调函数
        返回:
            Dict[str, Any]: 构建结果
        """
        try:
            self.logger.info(f"开始构建优化文件树，根路径: {root_paths}")
            start_time = time.time()
            
            result = {
                "total_folders": 0,
                "total_files": 0,
                "total_nodes": 0,
                "build_time": 0,
                "cache_hits": 0,
                "network_folders": 0
            }
            
            # 清理缓存
            self._clear_cache()
            
            # 并行处理多个根路径
            tasks = []
            for root_path in root_paths:
                task = self._build_tree_for_root(root_path, progress_callback)
                tasks.append(task)
            
            # 等待所有任务完成
            tree_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 汇总结果
            for tree_result in tree_results:
                if isinstance(tree_result, dict):
                    for key in result:
                        if key in tree_result:
                            result[key] += tree_result[key]
            
            result["build_time"] = time.time() - start_time
            
            self.logger.info(f"文件树构建完成，耗时: {result['build_time']:.2f}秒")
            self.logger.info(f"统计: 文件夹={result['total_folders']}, 文件={result['total_files']}, 节点={result['total_nodes']}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"构建优化文件树失败: {e}")
            raise

    async def _build_tree_for_root(self, root_path: str, 
                                 progress_callback=None) -> Dict[str, Any]:
        """
        为单个根路径构建文件树
        
        参数:
            root_path: 根路径
            progress_callback: 进度回调函数
        返回:
            Dict[str, Any]: 构建结果
        """
        try:
            self.logger.info(f"开始构建根路径文件树: {root_path}")
            
            result = {
                "total_folders": 0,
                "total_files": 0,
                "total_nodes": 0,
                "cache_hits": 0,
                "network_folders": 0
            }
            
            # 检查路径是否存在
            if not os.path.exists(root_path):
                self.logger.warning(f"根路径不存在: {root_path}")
                return result
            
            # 检查是否为网络路径
            is_network = self.network_handler.is_network_path(root_path)
            if is_network:
                result["network_folders"] += 1
                self.logger.info(f"检测到网络路径: {root_path}")
            
            # 深度优先构建
            await self._build_depth_first(root_path, 0, result, progress_callback)
            
            return result
            
        except Exception as e:
            self.logger.error(f"构建根路径文件树失败 {root_path}: {e}")
            return {"total_folders": 0, "total_files": 0, "total_nodes": 0, "cache_hits": 0, "network_folders": 0}

    async def _build_depth_first(self, folder_path: str, depth: int, 
                               result: Dict[str, Any], progress_callback=None) -> Optional[FolderInfo]:
        """
        深度优先构建文件树
        
        参数:
            folder_path: 文件夹路径
            depth: 当前深度
            result: 结果统计
            progress_callback: 进度回调函数
        返回:
            Optional[FolderInfo]: 文件夹信息
        """
        try:
            # 检查深度限制
            if depth > self.max_depth:
                self.logger.debug(f"达到最大深度限制: {folder_path}")
                return None
            
            # 检查缓存
            if folder_path in self.folder_cache:
                result["cache_hits"] += 1
                return self.folder_cache[folder_path]
            
            # 创建文件夹信息
            folder_info = await self._create_folder_info(folder_path, depth)
            if not folder_info:
                return None
            
            # 扫描文件夹内容
            children = await self._scan_folder_contents(folder_path)
            
            # 处理子文件夹
            subfolders = []
            for child_path in children["folders"]:
                child_folder = await self._build_depth_first(
                    child_path, depth + 1, result, progress_callback
                )
                if child_folder:
                    subfolders.append(child_folder)
            
            # 更新文件夹统计信息
            folder_info.files_count = len(children["files"])
            folder_info.subfolders_count = len(subfolders)
            folder_info.children_count = folder_info.files_count + folder_info.subfolders_count
            
            # 计算总大小（只包含直接子文件，不包含子文件夹的递归大小）
            total_size = sum(children["file_sizes"])
            folder_info.total_size = total_size
            
            # 计算内容哈希
            folder_info.content_hash = await self._calculate_content_hash(
                children["folders"], children["files"]
            )
            
            # 保存到数据库
            await self._save_folder_info(folder_info)
            
            # 处理文件
            await self._process_files(children["files"], folder_info.folder_id)
            
            # 创建树节点
            await self._create_tree_nodes(folder_info, subfolders, children["files"])
            
            # 更新缓存
            self.folder_cache[folder_path] = folder_info
            
            # 更新统计
            result["total_folders"] += 1
            result["total_files"] += len(children["files"])
            result["total_nodes"] += 1 + len(children["files"])  # 文件夹节点 + 文件节点
            
            # 报告进度
            if progress_callback:
                progress_callback({
                    "type": "folder_processed",
                    "path": folder_path,
                    "depth": depth,
                    "files_count": len(children["files"]),
                    "subfolders_count": len(subfolders)
                })
            
            return folder_info
            
        except Exception as e:
            self.logger.error(f"深度优先构建失败 {folder_path}: {e}")
            return None

    async def _create_folder_info(self, folder_path: str, depth: int) -> Optional[FolderInfo]:
        """
        创建文件夹信息
        
        参数:
            folder_path: 文件夹路径
            depth: 深度
        返回:
            Optional[FolderInfo]: 文件夹信息
        """
        try:
            # 检查是否为网络文件夹
            is_network = self.network_handler.is_network_path(folder_path)
            
            # 创建文件夹信息
            folder_info = FolderInfo.from_path(folder_path)
            folder_info.depth = depth
            folder_info.is_network_folder = is_network
            folder_info.scan_status = "scanning"
            
            return folder_info
            
        except Exception as e:
            self.logger.error(f"创建文件夹信息失败 {folder_path}: {e}")
            return None

    async def _scan_folder_contents(self, folder_path: str) -> Dict[str, List]:
        """
        扫描文件夹内容
        
        参数:
            folder_path: 文件夹路径
        返回:
            Dict[str, List]: 扫描结果
        """
        try:
            # 检查是否为网络文件夹
            if self.network_handler.is_network_path(folder_path):
                return await self.network_handler.scan_network_folder(folder_path)
            
            # 本地文件夹扫描
            return await self._scan_local_folder(folder_path)
            
        except Exception as e:
            self.logger.error(f"扫描文件夹内容失败 {folder_path}: {e}")
            return {"folders": [], "files": [], "file_sizes": []}

    async def _scan_local_folder(self, folder_path: str) -> Dict[str, List]:
        """
        扫描本地文件夹
        
        参数:
            folder_path: 文件夹路径
        返回:
            Dict[str, List]: 扫描结果
        """
        def _scan():
            folders = []
            files = []
            file_sizes = []
            
            try:
                for item in os.listdir(folder_path):
                    item_path = os.path.join(folder_path, item)
                    
                    if os.path.isdir(item_path):
                        folders.append(item_path)
                    elif os.path.isfile(item_path):
                        files.append(item_path)
                        try:
                            file_sizes.append(os.path.getsize(item_path))
                        except OSError:
                            file_sizes.append(0)
                            
            except (OSError, PermissionError) as e:
                self.logger.warning(f"扫描文件夹失败 {folder_path}: {e}")
            
            return {"folders": folders, "files": files, "file_sizes": file_sizes}
        
        # 在线程池中执行IO操作
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.executor, _scan)

    async def _calculate_content_hash(self, folders: List[str], files: List[str]) -> str:
        """
        计算内容哈希（基于子文件夹和文件名称）
        
        参数:
            folders: 子文件夹列表
            files: 文件列表
        返回:
            str: 内容哈希
        """
        try:
            # 提取名称并排序
            folder_names = sorted([os.path.basename(f) for f in folders])
            file_names = sorted([os.path.basename(f) for f in files])
            
            # 组合内容
            content = "|".join(folder_names + file_names)
            
            # 计算哈希
            return hashlib.md5(content.encode('utf-8')).hexdigest()
            
        except Exception as e:
            self.logger.warning(f"计算内容哈希失败: {e}")
            return ""

    async def _save_folder_info(self, folder_info: FolderInfo) -> bool:
        """
        保存文件夹信息到数据库
        
        参数:
            folder_info: 文件夹信息
        返回:
            bool: 是否成功
        """
        try:
            folders_collection = self.db_manager.async_db.folders
            
            # 使用upsert操作
            await folders_collection.replace_one(
                {"path": folder_info.path},
                folder_info.to_dict(),
                upsert=True
            )
            
            return True
            
        except Exception as e:
            self.logger.error(f"保存文件夹信息失败: {e}")
            return False

    async def _process_files(self, file_paths: List[str], folder_id: str) -> bool:
        """
        处理文件列表
        
        参数:
            file_paths: 文件路径列表
            folder_id: 文件夹ID
        返回:
            bool: 是否成功
        """
        try:
            if not file_paths:
                return True
            
            files_collection = self.db_manager.async_db.files
            
            # 批量更新文件记录
            bulk_operations = []
            
            for file_path in file_paths:
                relative_path = os.path.basename(file_path)
                
                bulk_operations.append(
                    {
                        "updateOne": {
                            "filter": {"path": file_path},
                            "update": {
                                "$set": {
                                    "folder_id": folder_id,
                                    "relative_path": relative_path
                                }
                            },
                            "upsert": False
                        }
                    }
                )
                
                # 批量执行
                if len(bulk_operations) >= self.batch_size:
                    await files_collection.bulk_write(bulk_operations)
                    bulk_operations = []
            
            # 执行剩余操作
            if bulk_operations:
                await files_collection.bulk_write(bulk_operations)
            
            return True
            
        except Exception as e:
            self.logger.error(f"处理文件列表失败: {e}")
            return False

    async def _create_tree_nodes(self, folder_info: FolderInfo, 
                               subfolders: List[FolderInfo], 
                               file_paths: List[str]) -> bool:
        """
        创建树节点
        
        参数:
            folder_info: 文件夹信息
            subfolders: 子文件夹列表
            file_paths: 文件路径列表
        返回:
            bool: 是否成功
        """
        try:
            tree_nodes_collection = self.db_manager.async_db.tree_nodes
            
            # 创建文件夹节点
            folder_node = TreeNodeInfo.create_folder_node(folder_info.path)
            folder_node.children_count = len(subfolders) + len(file_paths)
            
            # 检查是否需要虚拟节点
            if folder_node.children_count > self.virtual_node_threshold:
                # 创建虚拟"更多..."节点
                virtual_node = TreeNodeInfo.create_virtual_node(
                    f"更多... ({folder_node.children_count - self.virtual_node_threshold} 项)",
                    folder_node.node_id
                )
                await tree_nodes_collection.replace_one(
                    {"node_id": virtual_node.node_id},
                    virtual_node.to_dict(),
                    upsert=True
                )
            
            # 保存文件夹节点
            await tree_nodes_collection.replace_one(
                {"path": folder_info.path},
                folder_node.to_dict(),
                upsert=True
            )
            
            # 创建子文件夹节点
            for subfolder in subfolders:
                subfolder_node = TreeNodeInfo.create_folder_node(
                    subfolder.path, folder_node.node_id
                )
                await tree_nodes_collection.replace_one(
                    {"path": subfolder.path},
                    subfolder_node.to_dict(),
                    upsert=True
                )
            
            # 创建文件节点（仅在需要时）
            if len(file_paths) <= self.virtual_node_threshold:
                for file_path in file_paths:
                    file_node = TreeNodeInfo.create_file_node(
                        file_path, folder_node.node_id
                    )
                    await tree_nodes_collection.replace_one(
                        {"path": file_path},
                        file_node.to_dict(),
                        upsert=True
                    )
            
            return True
            
        except Exception as e:
            self.logger.error(f"创建树节点失败: {e}")
            return False

    async def verify_folder_changes(self, folder_path: str) -> Dict[str, Any]:
        """
        双重验证文件夹变化
        
        参数:
            folder_path: 文件夹路径
        返回:
            Dict[str, Any]: 验证结果
        """
        try:
            self.logger.debug(f"验证文件夹变化: {folder_path}")
            
            # 获取当前文件夹信息
            folders_collection = self.db_manager.async_db.folders
            current_folder = await folders_collection.find_one({"path": folder_path})
            
            if not current_folder:
                return {"changed": True, "reason": "folder_not_found"}
            
            # 扫描当前内容
            current_contents = await self._scan_folder_contents(folder_path)
            
            # 计算当前哈希和大小
            current_hash = await self._calculate_content_hash(
                current_contents["folders"], current_contents["files"]
            )
            current_size = sum(current_contents["file_sizes"])
            
            # 比较哈希
            stored_hash = current_folder.get("content_hash", "")
            if current_hash != stored_hash:
                return {
                    "changed": True, 
                    "reason": "content_hash_mismatch",
                    "old_hash": stored_hash,
                    "new_hash": current_hash
                }
            
            # 比较大小（仅直接文件）
            stored_size = current_folder.get("total_size", 0)
            if abs(current_size - stored_size) > 1024:  # 允许1KB误差
                return {
                    "changed": True,
                    "reason": "size_mismatch",
                    "old_size": stored_size,
                    "new_size": current_size
                }
            
            return {"changed": False, "reason": "no_change"}
            
        except Exception as e:
            self.logger.error(f"验证文件夹变化失败 {folder_path}: {e}")
            return {"changed": True, "reason": "verification_error"}

    def _clear_cache(self):
        """
        清理缓存
        """
        self.folder_cache.clear()
        self.tree_node_cache.clear()
        self.content_hash_cache.clear()
        self.logger.debug("缓存已清理")

    def __del__(self):
        """
        析构函数，清理资源
        """
        if hasattr(self, 'executor'):
            self.executor.shutdown(wait=False)


class NetworkFolderHandler:
    """
    网络文件夹处理器
    
    处理网络文件夹的特殊逻辑
    """

    def __init__(self, timeout: int = 30):
        """
        初始化网络文件夹处理器
        
        参数:
            timeout: 超时时间（秒）
        """
        self.timeout = timeout
        self.logger = get_logger("NetworkFolderHandler")
        self.retry_count = 3
        self.retry_delay = 1.0

    def is_network_path(self, path: str) -> bool:
        """
        检测是否为网络路径
        
        参数:
            path: 文件路径
        返回:
            bool: 是否为网络路径
        """
        try:
            # UNC路径检测
            if path.startswith(r"\\"):
                return True
            
            # 映射驱动器检测（Windows）
            if os.name == 'nt' and len(path) >= 2 and path[1] == ':':
                drive = path[:2]
                import subprocess
                try:
                    result = subprocess.run(
                        ['net', 'use', drive], 
                        capture_output=True, 
                        text=True, 
                        timeout=5
                    )
                    return 'Remote name' in result.stdout
                except (subprocess.TimeoutExpired, subprocess.SubprocessError):
                    pass
            
            # SMB路径检测
            if path.startswith('smb://') or path.startswith('//') or path.startswith('\\\\'):
                return True
            
            return False
            
        except Exception as e:
            self.logger.warning(f"网络路径检测失败 {path}: {e}")
            return False

    async def scan_network_folder(self, folder_path: str) -> Dict[str, List]:
        """
        扫描网络文件夹
        
        参数:
            folder_path: 网络文件夹路径
        返回:
            Dict[str, List]: 扫描结果
        """
        for attempt in range(self.retry_count):
            try:
                self.logger.debug(f"扫描网络文件夹 (尝试 {attempt + 1}): {folder_path}")
                
                # 使用超时机制扫描
                result = await asyncio.wait_for(
                    self._scan_network_folder_impl(folder_path),
                    timeout=self.timeout
                )
                
                return result
                
            except asyncio.TimeoutError:
                self.logger.warning(f"网络文件夹扫描超时 (尝试 {attempt + 1}): {folder_path}")
                if attempt < self.retry_count - 1:
                    await asyncio.sleep(self.retry_delay)
                    continue
                else:
                    return {"folders": [], "files": [], "file_sizes": []}
                    
            except Exception as e:
                self.logger.error(f"网络文件夹扫描失败 (尝试 {attempt + 1}) {folder_path}: {e}")
                if attempt < self.retry_count - 1:
                    await asyncio.sleep(self.retry_delay)
                    continue
                else:
                    return {"folders": [], "files": [], "file_sizes": []}

    async def _scan_network_folder_impl(self, folder_path: str) -> Dict[str, List]:
        """
        网络文件夹扫描实现
        
        参数:
            folder_path: 网络文件夹路径
        返回:
            Dict[str, List]: 扫描结果
        """
        def _scan():
            folders = []
            files = []
            file_sizes = []
            
            try:
                for item in os.listdir(folder_path):
                    item_path = os.path.join(folder_path, item)
                    
                    try:
                        if os.path.isdir(item_path):
                            folders.append(item_path)
                        elif os.path.isfile(item_path):
                            files.append(item_path)
                            try:
                                file_sizes.append(os.path.getsize(item_path))
                            except OSError:
                                file_sizes.append(0)
                    except OSError:
                        # 跳过无法访问的项目
                        continue
                        
            except (OSError, PermissionError) as e:
                self.logger.warning(f"扫描网络文件夹失败 {folder_path}: {e}")
            
            return {"folders": folders, "files": files, "file_sizes": file_sizes}
        
        # 在线程池中执行
        loop = asyncio.get_event_loop()
        executor = ThreadPoolExecutor(max_workers=1)
        try:
            return await loop.run_in_executor(executor, _scan)
        finally:
            executor.shutdown(wait=False)