#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
树优化功能快速验证脚本

该脚本用于快速验证树优化功能是否正确集成到项目中，
检查所有必要的组件是否可以正常导入和初始化。

作者: SmartFileManger开发团队
日期: 2024-01-20
版本: 1.0.0
"""

import sys
import traceback
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

print("🔍 开始验证树优化功能集成...")
print("="*60)

# 验证步骤计数器
step = 1
success_count = 0
total_steps = 6

def print_step(description: str, success: bool = True):
    """打印验证步骤结果"""
    global step, success_count
    status = "✅" if success else "❌"
    print(f"{status} 步骤 {step}: {description}")
    if success:
        success_count += 1
    step += 1

# 步骤1: 验证核心模块导入
try:
    from src.core.tree_optimization_manager import TreeOptimizationManager, TreeNode, FolderInfo
    print_step("导入树优化管理器核心模块")
except Exception as e:
    print_step(f"导入树优化管理器核心模块失败: {e}", False)
    traceback.print_exc()

# 步骤2: 验证文件扫描器集成
try:
    from src.core.file_scanner import OptimizedFileScanner
    print_step("导入集成了树优化功能的文件扫描器")
except Exception as e:
    print_step(f"导入文件扫描器失败: {e}", False)
    traceback.print_exc()

# 步骤3: 验证数据库管理器
try:
    from src.data.db_manager import MongoDBManager
    print_step("导入数据库管理器")
except Exception as e:
    print_step(f"导入数据库管理器失败: {e}", False)
    traceback.print_exc()

# 步骤4: 验证异步任务管理器
try:
    from src.utils.async_task_manager import AsyncTaskManager
    print_step("导入异步任务管理器")
except Exception as e:
    print_step(f"导入异步任务管理器失败: {e}", False)
    traceback.print_exc()

# 步骤5: 验证TreeOptimizationManager实例化
try:
    # 创建一个模拟的数据库管理器（不连接真实数据库）
    class MockDBManager:
        def __init__(self):
            self.connected = False
        
        async def connect(self):
            self.connected = True
        
        async def close(self):
            self.connected = False
    
    mock_db = MockDBManager()
    tree_optimizer = TreeOptimizationManager(mock_db)
    print_step("创建树优化管理器实例")
except Exception as e:
    print_step(f"创建树优化管理器实例失败: {e}", False)
    traceback.print_exc()

# 步骤6: 验证文件扫描器中的树优化管理器集成
try:
    mock_async_manager = type('MockAsyncManager', (), {
        'submit_task': lambda self, *args, **kwargs: None,
        'shutdown': lambda self: None
    })()
    
    scanner = OptimizedFileScanner(mock_db, mock_async_manager)
    
    # 检查是否有tree_optimization_manager属性
    if hasattr(scanner, 'tree_optimization_manager'):
        print_step("文件扫描器成功集成树优化管理器")
    else:
        print_step("文件扫描器未集成树优化管理器", False)
except Exception as e:
    print_step(f"验证文件扫描器集成失败: {e}", False)
    traceback.print_exc()

# 显示验证结果汇总
print("\n" + "="*60)
print("📊 验证结果汇总")
print("="*60)
print(f"✅ 成功步骤: {success_count}/{total_steps}")
print(f"❌ 失败步骤: {total_steps - success_count}/{total_steps}")

if success_count == total_steps:
    print("\n🎉 所有验证步骤通过！树优化功能已成功集成。")
    print("\n📋 下一步操作建议:")
    print("1. 运行完整的集成测试: python test_tree_optimization_integration.py")
    print("2. 运行功能演示: python demo_tree_optimization.py")
    print("3. 查看详细文档: README_树优化功能.md")
else:
    print("\n⚠️ 部分验证步骤失败，请检查以下问题:")
    print("1. 确保所有依赖项已安装: pip install -r requirements-dev-utf8.txt")
    print("2. 检查Python路径配置是否正确")
    print("3. 确认所有必要的模块文件都存在")
    print("4. 查看上面的错误信息进行调试")

print("\n" + "="*60)
print("验证完成")
print("="*60)

# 额外信息
print("\n📁 相关文件:")
print(f"- 树优化管理器: {project_root}/src/core/tree_optimization_manager.py")
print(f"- 文件扫描器: {project_root}/src/core/file_scanner.py")
print(f"- 集成测试: {project_root}/test_tree_optimization_integration.py")
print(f"- 功能演示: {project_root}/demo_tree_optimization.py")
print(f"- 使用文档: {project_root}/README_树优化功能.md")