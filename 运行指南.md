# 运行指南

## 1. 进入项目目录
```powershell
cd D:\Coding\smartfileManger  # 请根据实际路径进入项目根目录
```

## 2. 创建并激活虚拟环境
```powershell
python -m venv .venv
.venv\Scripts\Activate.ps1  # Windows PowerShell
# 或
.venv\Scripts\activate.bat  # Windows CMD
# 或
source .venv/bin/activate  # Linux/Mac
```

## 3. 安装依赖
```powershell
pip install --upgrade pip  # 推荐先升级pip
pip install -r requirements-dev-utf8.txt
```

- **如遇到依赖冲突或安装失败，请先升级pip再重试。**
- requirements-dev-utf8.txt 已补充跨平台依赖（如 python-magic-bin、pytest-asyncio 等），确保在新环境下文件树和测试功能可用。

## 4. 启动程序
```powershell
python main.py
```

## 5. 常见问题排查
- **PowerShell执行策略限制**：
  ```powershell
  Set-ExecutionPolicy -Scope Process -ExecutionPolicy Bypass
  ```
- **文件树无法打开/依赖缺失**：
  - 确认已用 pip 安装 requirements-dev-utf8.txt
  - Windows 下需 python-magic-bin
  - Linux 下需 libmagic（可用 apt/yum 安装）
- **数据库连接失败**：
  - 确认 MongoDB 服务已启动
  - 检查 config/settings.yaml 中数据库配置
- **虚拟环境未激活**：
  - 运行 `python -c "import sys; print(sys.prefix)"`，应输出 .venv 路径

## 6. 其他说明
- 如需运行测试：
  ```powershell
  pytest
  ```
- 如需批量处理/视频功能，需确保 ffmpeg 已正确安装并配置环境变量

---

如有疑问请查阅 README.md、file_id_quick_guide.md 或联系开发者。