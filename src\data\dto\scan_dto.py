#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
扫描相关DTO定义

包含文件扫描、目录扫描等相关的数据传输对象
"""

from dataclasses import dataclass
from typing import List, Optional, Dict, Any
from enum import Enum
import uuid
from .base_dto import BaseDTO


class ScanType(Enum):
    """扫描类型枚举"""
    FULL_SCAN = "full_scan"
    QUICK_SCAN = "quick_scan"
    INCREMENTAL_SCAN = "incremental_scan"
    CUSTOM_SCAN = "custom_scan"


class ScanStatus(Enum):
    """扫描状态枚举"""
    NOT_STARTED = "not_started"
    QUEUED = "queued"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass(frozen=True)
class ScanRequest(BaseDTO):
    """
    扫描请求DTO
    
    用于发起文件扫描任务的请求数据
    """
    task_id: str
    directories: List[str]
    scan_type: ScanType = ScanType.FULL_SCAN
    recursive: bool = True
    update_database: bool = True
    calculate_hash: bool = False
    check_duplicates: bool = False
    include_hidden: bool = False
    file_extensions: Optional[List[str]] = None
    exclude_patterns: Optional[List[str]] = None
    max_file_size: Optional[int] = None  # 字节
    min_file_size: Optional[int] = None  # 字节
    
    def validate(self) -> List[str]:
        """验证扫描请求数据"""
        errors = []
        
        if not self.task_id:
            errors.append("任务ID不能为空")
        
        if not self.directories:
            errors.append("扫描目录列表不能为空")
        
        for directory in self.directories:
            if not directory or not directory.strip():
                errors.append("扫描目录路径不能为空")
        
        if self.max_file_size is not None and self.max_file_size <= 0:
            errors.append("最大文件大小必须大于0")
        
        if self.min_file_size is not None and self.min_file_size < 0:
            errors.append("最小文件大小不能为负数")
        
        if (self.max_file_size is not None and self.min_file_size is not None 
            and self.min_file_size > self.max_file_size):
            errors.append("最小文件大小不能大于最大文件大小")
        
        return errors
    
    @classmethod
    def new_quick_scan(cls, directories: List[str]) -> 'ScanRequest':
        """创建快速扫描请求"""
        return cls(
            task_id=str(uuid.uuid4()),
            directories=directories,
            scan_type=ScanType.QUICK_SCAN,
            recursive=True,
            update_database=False,
            calculate_hash=False,
            check_duplicates=False
        )

    @classmethod
    def new_full_scan(cls, directories: List[str]) -> 'ScanRequest':
        """创建完整扫描请求"""
        return cls(
            task_id=str(uuid.uuid4()),
            directories=directories,
            scan_type=ScanType.FULL_SCAN,
            recursive=True,
            update_database=True,
            calculate_hash=True,
            check_duplicates=True
        )


@dataclass(frozen=True)
class FileInfo(BaseDTO):
    """
    文件信息DTO
    
    包含文件的基本信息
    """
    path: str
    name: str
    size: int
    modified_time: float
    created_time: Optional[float] = None
    is_directory: bool = False
    extension: Optional[str] = None
    hash_md5: Optional[str] = None
    hash_sha256: Optional[str] = None
    is_hidden: bool = False
    is_system: bool = False
    permissions: Optional[str] = None
    
    def validate(self) -> List[str]:
        """验证文件信息"""
        errors = []
        
        if not self.path:
            errors.append("文件路径不能为空")
        
        if not self.name:
            errors.append("文件名不能为空")
        
        if self.size < 0:
            errors.append("文件大小不能为负数")
        
        if self.modified_time <= 0:
            errors.append("修改时间必须大于0")
        
        if self.created_time is not None and self.created_time <= 0:
            errors.append("创建时间必须大于0")
        
        return errors


@dataclass(frozen=True)
class ScanResult(BaseDTO):
    """
    扫描结果DTO
    
    包含扫描任务的结果信息
    """
    task_id: str
    status: ScanStatus
    start_time: float
    end_time: Optional[float] = None
    total_files: int = 0
    total_directories: int = 0
    processed_files: int = 0
    failed_files: int = 0
    total_size: int = 0
    scan_duration: Optional[float] = None
    files_per_second: Optional[float] = None
    errors: Optional[List[str]] = None
    warnings: Optional[List[str]] = None
    
    def validate(self) -> List[str]:
        """验证扫描结果"""
        errors = []
        
        if not self.task_id:
            errors.append("任务ID不能为空")
        
        if self.start_time <= 0:
            errors.append("开始时间必须大于0")
        
        if self.end_time is not None and self.end_time < self.start_time:
            errors.append("结束时间不能早于开始时间")
        
        if self.total_files < 0:
            errors.append("总文件数不能为负数")
        
        if self.total_directories < 0:
            errors.append("总目录数不能为负数")
        
        if self.processed_files < 0:
            errors.append("已处理文件数不能为负数")
        
        if self.processed_files > self.total_files:
            errors.append("已处理文件数不能超过总文件数")
        
        if self.failed_files < 0:
            errors.append("失败文件数不能为负数")
        
        if self.failed_files > self.processed_files:
            errors.append("失败文件数不能超过已处理文件数")
        
        if self.total_size < 0:
            errors.append("总大小不能为负数")
        
        return errors
    
    @property
    def success_rate(self) -> float:
        """计算成功率"""
        if self.processed_files == 0:
            return 0.0
        return (self.processed_files - self.failed_files) / self.processed_files * 100.0
    
    @property
    def is_completed(self) -> bool:
        """检查是否已完成"""
        return self.status in [ScanStatus.COMPLETED, ScanStatus.FAILED, ScanStatus.CANCELLED]
