#!/usr/bin/env python3
"""
修复数据库管理器中的logger调用错误
将所有的 logger. 替换为 self.logger.
"""

import re
import os

def fix_logger_calls():
    """修复logger调用"""
    file_path = "src/data/db_manager.py"
    
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 统计替换前的logger调用
    logger_calls = len(re.findall(r'(?<!self\.)logger\.', content))
    print(f"发现 {logger_calls} 个需要修复的logger调用")
    
    # 替换所有的 logger. 为 self.logger.（但不替换已经是 self.logger. 的）
    # 使用负向后瞻断言，确保不替换已经是 self.logger. 的情况
    fixed_content = re.sub(r'(?<!self\.)logger\.', 'self.logger.', content)
    
    # 统计替换后的情况
    remaining_calls = len(re.findall(r'(?<!self\.)logger\.', fixed_content))
    fixed_calls = logger_calls - remaining_calls
    
    print(f"成功修复 {fixed_calls} 个logger调用")
    if remaining_calls > 0:
        print(f"仍有 {remaining_calls} 个logger调用未修复")
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(fixed_content)
    
    print(f"文件已更新: {file_path}")

if __name__ == "__main__":
    fix_logger_calls()
