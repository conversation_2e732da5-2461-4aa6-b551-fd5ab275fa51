#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
重命名服务实现

基于新架构重构的文件重命名服务，遵循RULE-001、RULE-003和RULE-005
使用DTO进行数据传输，通过事件总线进行通信
实现安全的文件操作和完整的回滚机制
"""

import asyncio
import time
import uuid
import os
import shutil
from pathlib import Path
from typing import AsyncGenerator, Optional, List, Dict, Any
from concurrent.futures import ThreadPoolExecutor

from src.services.interfaces import (
    IRenameService, IRenameRuleService, BaseServiceImpl, ServiceStatus,
    RenameConflictResolution
)
from src.data.dto.rename_dto import (
    RenameRequest, RenameResult, RenameRule, RenamePreview,
    RenameOperation, RenameTransaction, RenameHistory,
    RenameRuleType, CaseType
)
from src.data.dto.base_dto import ProgressUpdate, ErrorInfo
from src.data.dto.interface_dto import RenameHistoryInfo, RuleTestResult
from src.ui.events.event_bus import IEventBus
from src.ui.events.event_definitions import (
    create_business_event, BusinessEventType, EventPriority
)

# 常量定义
DEFAULT_MAX_WORKERS = 4
DEFAULT_BATCH_SIZE = 100
DEFAULT_BACKUP_SUFFIX = ".bak"
DEFAULT_CONFLICT_COUNTER_DIGITS = 3
PROGRESS_UPDATE_INTERVAL = 0.5  # 进度更新间隔（秒）
TRANSACTION_TIMEOUT = 300.0  # 事务超时时间（秒）
MAX_UNDO_HISTORY = 100  # 最大撤销历史记录数


class RenameServiceImpl(BaseServiceImpl, IRenameService):
    """
    重命名服务实现
    
    遵循RULE-001: 模块职责单一原则 - 只负责文件重命名
    遵循RULE-003: 事件驱动通信规范 - 通过事件总线通信
    遵循RULE-005: 异步任务实现 - 长时间运行的重命名操作
    """
    
    def __init__(self, event_bus: IEventBus, max_workers: int = DEFAULT_MAX_WORKERS):
        super().__init__("RenameService", "2.0.0")
        self._event_bus = event_bus
        self._max_workers = max_workers
        self._executor = ThreadPoolExecutor(max_workers=max_workers)
        
        # 规则服务
        self._rule_service = RenameRuleServiceImpl()
        
        # 任务管理
        self._rename_tasks: Dict[str, asyncio.Task] = {}
        self._rename_results: Dict[str, RenameResult] = {}
        self._rename_progress: Dict[str, ProgressUpdate] = {}
        
        # 事务管理
        self._transactions: Dict[str, RenameTransaction] = {}
        self._history: List[RenameHistory] = []
        
        # 备份管理
        self._backup_dir: Optional[str] = None
        
    async def start_service(self) -> bool:
        """启动服务"""
        if await super().start_service():
            # 初始化备份目录
            await self._initialize_backup_directory()
            
            # 发布服务启动事件
            event = create_business_event(
                BusinessEventType.DUPLICATE_CHECK_STARTED,  # 复用现有事件类型
                self.get_service_name(),
                {"service_version": self.get_service_version(), "action": "service_started"}
            )
            self._event_bus.publish("SERVICE_STARTED", event)
            return True
        return False
    
    async def stop_service(self) -> bool:
        """停止服务"""
        # 取消所有活动的重命名任务
        await self.cancel_all_tasks()
        
        # 关闭线程池
        self._executor.shutdown(wait=True)
        
        if await super().stop_service():
            # 发布服务停止事件
            event = create_business_event(
                BusinessEventType.DUPLICATE_CHECK_COMPLETED,  # 复用现有事件类型
                self.get_service_name(),
                {"action": "service_stopped"}
            )
            self._event_bus.publish("SERVICE_STOPPED", event)
            return True
        return False
    
    async def preview_rename(self, request: RenameRequest) -> List[RenamePreview]:
        """
        预览重命名操作
        
        遵循RULE-001: 单一职责 - 只负责生成预览
        """
        # 验证请求
        validation_errors = await self.validate_rename_request(request)
        if validation_errors:
            error_info = ErrorInfo(
                code="RENAME_VALIDATION_FAILED",
                message="重命名请求验证失败",
                details="; ".join(validation_errors)
            )
            raise ValueError(error_info.message)
        
        previews = []
        
        for file_path in request.files:
            try:
                # 检查文件是否存在
                if not os.path.exists(file_path):
                    preview = RenamePreview(
                        original_path=file_path,
                        new_path=file_path,
                        original_name=os.path.basename(file_path),
                        new_name=os.path.basename(file_path),
                        has_conflict=True,
                        error_message="文件不存在"
                    )
                    previews.append(preview)
                    continue
                
                # 应用重命名规则
                original_name = os.path.basename(file_path)
                new_name = await self._rule_service.apply_rules(request.rules, original_name)
                
                # 构建新路径
                file_dir = os.path.dirname(file_path)
                new_path = os.path.join(file_dir, new_name)
                
                # 检查是否会覆盖现有文件
                will_overwrite = (os.path.exists(new_path) and 
                                os.path.normpath(file_path) != os.path.normpath(new_path))
                
                preview = RenamePreview(
                    original_path=file_path,
                    new_path=new_path,
                    original_name=original_name,
                    new_name=new_name,
                    will_overwrite=will_overwrite,
                    has_conflict=will_overwrite and not request.overwrite_existing
                )
                
                previews.append(preview)
                
            except Exception as e:
                preview = RenamePreview(
                    original_path=file_path,
                    new_path=file_path,
                    original_name=os.path.basename(file_path),
                    new_name=os.path.basename(file_path),
                    has_conflict=True,
                    error_message=str(e)
                )
                previews.append(preview)
        
        return previews
    
    async def execute_rename(self, request: RenameRequest) -> AsyncGenerator[ProgressUpdate, None]:
        """
        执行重命名操作
        
        遵循RULE-005: 长时间运行的操作实现为异步任务
        """
        # 验证请求
        validation_errors = await self.validate_rename_request(request)
        if validation_errors:
            error_info = ErrorInfo(
                code="RENAME_VALIDATION_FAILED",
                message="重命名请求验证失败",
                details="; ".join(validation_errors)
            )
            raise ValueError(error_info.message)
        
        # 如果只是预览，不执行实际重命名
        if request.preview_only:
            previews = await self.preview_rename(request)
            result = RenameResult(
                task_id=request.task_id,
                total_files=len(request.files),
                successful_renames=0,
                failed_renames=0,
                skipped_files=len(request.files),
                previews=previews
            )
            self._rename_results[request.task_id] = result
            
            # 发送完成进度
            final_progress = ProgressUpdate(
                task_id=request.task_id,
                progress=100.0,
                status_message="预览完成",
                processed_items=len(request.files),
                total_items=len(request.files)
            )
            yield final_progress
            return
        
        # 发布重命名开始事件
        event = create_business_event(
            BusinessEventType.DUPLICATE_CHECK_STARTED,  # 复用现有事件类型
            self.get_service_name(),
            {
                "task_id": request.task_id,
                "files": request.files,
                "rule_count": len(request.rules)
            }
        )
        self._event_bus.publish("RENAME_STARTED", event)
        
        # 创建重命名任务
        rename_task = asyncio.create_task(self._execute_rename_task(request))
        self._rename_tasks[request.task_id] = rename_task
        
        try:
            # 异步生成进度更新
            async for progress in self._monitor_rename_progress(request.task_id):
                yield progress
                
                # 发布进度事件
                progress_event = create_business_event(
                    BusinessEventType.DUPLICATE_CHECK_PROGRESS,  # 复用现有事件类型
                    self.get_service_name(),
                    progress.to_dict()
                )
                self._event_bus.publish("RENAME_PROGRESS", progress_event)
        
        except Exception as e:
            # 处理重命名错误
            error_info = ErrorInfo(
                code="RENAME_EXECUTION_FAILED",
                message=f"重命名执行失败: {str(e)}",
                details=f"任务ID: {request.task_id}"
            )
            
            error_event = create_business_event(
                BusinessEventType.DUPLICATE_CHECK_COMPLETED,  # 复用现有事件类型
                self.get_service_name(),
                error_info.to_dict(),
                EventPriority.HIGH
            )
            self._event_bus.publish("RENAME_FAILED", error_event)
            raise
        
        finally:
            # 清理任务
            if request.task_id in self._rename_tasks:
                del self._rename_tasks[request.task_id]

    async def _execute_rename_task(self, request: RenameRequest) -> RenameResult:
        """执行重命名任务的核心逻辑"""
        start_time = time.time()

        # 创建事务
        transaction_id = str(uuid.uuid4())
        transaction = RenameTransaction(
            transaction_id=transaction_id,
            task_id=request.task_id,
            operations=[],
            start_time=start_time
        )

        try:
            # 初始化进度
            progress = ProgressUpdate(
                task_id=request.task_id,
                progress=0.0,
                status_message="开始重命名...",
                processed_items=0,
                total_items=len(request.files)
            )
            self._rename_progress[request.task_id] = progress

            # 生成预览
            previews = await self.preview_rename(request)

            # 过滤掉有冲突的文件（如果不允许覆盖）
            valid_previews = [p for p in previews if p.is_valid or request.overwrite_existing]

            # 更新进度
            progress.status_message = f"准备重命名 {len(valid_previews)} 个文件..."
            progress.progress = 10.0
            self._rename_progress[request.task_id] = progress

            # 执行重命名操作
            operations = []
            successful_count = 0
            failed_count = 0
            skipped_count = len(previews) - len(valid_previews)

            for i, preview in enumerate(valid_previews):
                try:
                    # 创建操作记录
                    operation_id = str(uuid.uuid4())
                    backup_path = None

                    # 备份原文件（如果需要）
                    if request.backup:
                        backup_path = await self._backup_file(preview.original_path)

                    # 执行重命名
                    await self._rename_file_atomic(preview.original_path, preview.new_path)

                    # 记录成功操作
                    operation = RenameOperation(
                        operation_id=operation_id,
                        original_path=preview.original_path,
                        new_path=preview.new_path,
                        timestamp=time.time(),
                        backup_path=backup_path,
                        success=True
                    )
                    operations.append(operation)
                    successful_count += 1

                except Exception as e:
                    # 记录失败操作
                    operation = RenameOperation(
                        operation_id=str(uuid.uuid4()),
                        original_path=preview.original_path,
                        new_path=preview.new_path,
                        timestamp=time.time(),
                        success=False,
                        error_message=str(e)
                    )
                    operations.append(operation)
                    failed_count += 1

                # 更新进度
                processed = i + 1
                progress_percent = 10.0 + (processed / len(valid_previews)) * 80.0
                progress.progress = min(progress_percent, 90.0)
                progress.processed_items = processed + skipped_count
                progress.status_message = f"已处理 {processed}/{len(valid_previews)} 个文件"
                self._rename_progress[request.task_id] = progress

            # 完成事务
            end_time = time.time()
            transaction = RenameTransaction(
                transaction_id=transaction_id,
                task_id=request.task_id,
                operations=operations,
                start_time=start_time,
                end_time=end_time,
                committed=True
            )
            self._transactions[transaction_id] = transaction

            # 创建结果
            result = RenameResult(
                task_id=request.task_id,
                total_files=len(request.files),
                successful_renames=successful_count,
                failed_renames=failed_count,
                skipped_files=skipped_count,
                previews=previews,
                duration=end_time - start_time
            )

            # 保存结果
            self._rename_results[request.task_id] = result

            # 添加到历史记录
            await self._add_to_history(transaction, "rename")

            # 发布完成事件
            event = create_business_event(
                BusinessEventType.DUPLICATE_CHECK_COMPLETED,  # 复用现有事件类型
                self.get_service_name(),
                result.to_dict()
            )
            self._event_bus.publish("RENAME_COMPLETED", event)

            return result

        except Exception as e:
            # 回滚事务
            if operations:
                await self._rollback_operations(operations)

            # 创建失败结果
            end_time = time.time()
            result = RenameResult(
                task_id=request.task_id,
                total_files=len(request.files),
                successful_renames=0,
                failed_renames=len(request.files),
                skipped_files=0,
                previews=[],
                errors=[str(e)],
                duration=end_time - start_time
            )

            self._rename_results[request.task_id] = result
            raise

    async def _rename_file_atomic(self, old_path: str, new_path: str):
        """原子性重命名文件"""
        # 使用线程池执行文件系统操作
        await asyncio.get_event_loop().run_in_executor(
            self._executor,
            os.rename,
            old_path,
            new_path
        )

    async def _backup_file(self, file_path: str) -> str:
        """备份文件"""
        if not self._backup_dir:
            await self._initialize_backup_directory()

        # 生成备份文件名
        file_name = os.path.basename(file_path)
        timestamp = int(time.time())
        backup_name = f"{file_name}.{timestamp}{DEFAULT_BACKUP_SUFFIX}"
        backup_path = os.path.join(self._backup_dir, backup_name)

        # 复制文件到备份目录
        await asyncio.get_event_loop().run_in_executor(
            self._executor,
            shutil.copy2,
            file_path,
            backup_path
        )

        return backup_path

    async def _initialize_backup_directory(self):
        """初始化备份目录"""
        if not self._backup_dir:
            # 在临时目录创建备份文件夹
            import tempfile
            temp_dir = tempfile.gettempdir()
            self._backup_dir = os.path.join(temp_dir, "smartfilemanager_rename_backup")

            # 创建目录（如果不存在）
            def create_dir():
                os.makedirs(self._backup_dir, exist_ok=True)

            await asyncio.get_event_loop().run_in_executor(
                self._executor,
                create_dir
            )

    async def _rollback_operations(self, operations: List[RenameOperation]):
        """回滚重命名操作"""
        for operation in reversed(operations):  # 逆序回滚
            if operation.success:
                try:
                    # 如果有备份，从备份恢复
                    if operation.backup_path and os.path.exists(operation.backup_path):
                        await self._rename_file_atomic(operation.new_path, operation.original_path)
                    else:
                        # 直接重命名回去
                        if os.path.exists(operation.new_path):
                            await self._rename_file_atomic(operation.new_path, operation.original_path)
                except Exception as e:
                    # 记录回滚失败，但继续处理其他操作
                    import logging
                    logging.getLogger(__name__).error(f"回滚操作失败: {operation.operation_id}, 错误: {e}")

    async def _monitor_rename_progress(self, task_id: str) -> AsyncGenerator[ProgressUpdate, None]:
        """监控重命名进度"""
        last_progress = 0.0

        while task_id in self._rename_tasks:
            current_progress = self._rename_progress.get(task_id)

            if current_progress and current_progress.progress > last_progress:
                yield current_progress
                last_progress = current_progress.progress

                # 如果完成，退出监控
                if current_progress.progress >= 100.0:
                    break

            # 等待一段时间再检查
            await asyncio.sleep(PROGRESS_UPDATE_INTERVAL)

        # 确保最终进度被发送
        final_progress = self._rename_progress.get(task_id)
        if final_progress:
            final_progress.progress = 100.0
            final_progress.status_message = "重命名完成"
            yield final_progress

    async def _add_to_history(self, transaction: RenameTransaction, operation_type: str):
        """添加到历史记录"""
        history = RenameHistory(
            history_id=str(uuid.uuid4()),
            task_id=transaction.task_id,
            transaction_id=transaction.transaction_id,
            timestamp=time.time(),
            operation_type=operation_type,
            file_count=len(transaction.operations),
            success_count=transaction.success_count,
            failure_count=transaction.failure_count,
            description=f"{operation_type} operation with {len(transaction.operations)} files"
        )

        self._history.append(history)

        # 限制历史记录数量
        if len(self._history) > MAX_UNDO_HISTORY:
            self._history = self._history[-MAX_UNDO_HISTORY:]

    # 实现IRenameService接口的其他方法
    async def get_rename_result(self, task_id: str) -> Optional[RenameResult]:
        """获取重命名结果"""
        return self._rename_results.get(task_id)

    async def validate_rename_request(self, request: RenameRequest) -> List[str]:
        """验证重命名请求"""
        errors = []

        # 使用DTO的内置验证
        dto_errors = request.validate()
        errors.extend(dto_errors)

        # 额外的业务验证
        for file_path in request.files:
            if not os.path.exists(file_path):
                errors.append(f"文件不存在: {file_path}")
            elif not os.access(file_path, os.R_OK):
                errors.append(f"文件不可读: {file_path}")
            elif not os.access(os.path.dirname(file_path), os.W_OK):
                errors.append(f"目录不可写: {os.path.dirname(file_path)}")

        return errors

    async def check_name_conflicts(self, previews: List[RenamePreview]) -> List[str]:
        """检查命名冲突"""
        conflicts = []
        new_paths = set()

        for preview in previews:
            # 检查是否与现有文件冲突
            if preview.will_overwrite:
                conflicts.append(preview.new_path)

            # 检查是否与其他重命名操作冲突
            if preview.new_path in new_paths:
                conflicts.append(preview.new_path)
            else:
                new_paths.add(preview.new_path)

        return conflicts

    async def resolve_conflicts(self, previews: List[RenamePreview],
                              strategy: RenameConflictResolution) -> List[RenamePreview]:
        """解决命名冲突"""
        resolved_previews = []

        for preview in previews:
            if not preview.has_conflict:
                resolved_previews.append(preview)
                continue

            if strategy == RenameConflictResolution.SKIP:
                # 跳过冲突文件
                continue
            elif strategy == RenameConflictResolution.ADD_COUNTER:
                # 添加计数器
                new_path = await self._generate_unique_path(preview.new_path)
                resolved_preview = RenamePreview(
                    original_path=preview.original_path,
                    new_path=new_path,
                    original_name=preview.original_name,
                    new_name=os.path.basename(new_path),
                    will_overwrite=False,
                    has_conflict=False
                )
                resolved_previews.append(resolved_preview)
            else:
                # 其他策略暂时跳过
                resolved_previews.append(preview)

        return resolved_previews

    async def _generate_unique_path(self, base_path: str) -> str:
        """生成唯一的文件路径"""
        if not os.path.exists(base_path):
            return base_path

        directory = os.path.dirname(base_path)
        name, ext = os.path.splitext(os.path.basename(base_path))

        counter = 1
        while True:
            new_name = f"{name}_{counter:0{DEFAULT_CONFLICT_COUNTER_DIGITS}d}{ext}"
            new_path = os.path.join(directory, new_name)
            if not os.path.exists(new_path):
                return new_path
            counter += 1

            # 防止无限循环
            if counter > 9999:
                raise ValueError(f"无法为文件生成唯一名称: {base_path}")

    async def undo_rename(self, task_id: str) -> bool:
        """撤销重命名操作"""
        # 查找对应的事务
        transaction = None
        for trans in self._transactions.values():
            if trans.task_id == task_id and trans.committed and not trans.rolled_back:
                transaction = trans
                break

        if not transaction:
            return False

        try:
            # 执行回滚
            await self._rollback_operations(transaction.operations)

            # 标记事务为已回滚
            rolled_back_transaction = RenameTransaction(
                transaction_id=transaction.transaction_id,
                task_id=transaction.task_id,
                operations=transaction.operations,
                start_time=transaction.start_time,
                end_time=transaction.end_time,
                committed=transaction.committed,
                rolled_back=True
            )
            self._transactions[transaction.transaction_id] = rolled_back_transaction

            # 添加到历史记录
            await self._add_to_history(rolled_back_transaction, "undo")

            return True

        except Exception as e:
            import logging
            logging.getLogger(__name__).error(f"撤销重命名失败: {task_id}, 错误: {e}")
            return False

    async def get_rename_history(self, limit: int = 100) -> RenameHistoryInfo:
        """
        获取重命名历史

        Args:
            limit: 返回的历史条目数量限制

        Returns:
            RenameHistoryInfo: 重命名历史信息DTO
        """
        # 按时间倒序排列
        sorted_history = sorted(self._history, key=lambda h: h.timestamp, reverse=True)

        # 限制返回数量
        limited_history = sorted_history[:limit]

        # 转换为字典格式
        entries = [history.to_dict() for history in limited_history]

        # 计算日期范围
        date_range_start = None
        date_range_end = None
        if entries:
            timestamps = [entry.get('timestamp', 0) for entry in entries]
            date_range_start = min(timestamps)
            date_range_end = max(timestamps)

        return RenameHistoryInfo(
            entries=entries,
            total_count=len(entries),
            date_range_start=date_range_start,
            date_range_end=date_range_end
        )

    async def cancel_all_tasks(self):
        """取消所有活动任务"""
        for task_id, task in list(self._rename_tasks.items()):
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass
            finally:
                self._rename_tasks.pop(task_id, None)
                self._rename_progress.pop(task_id, None)

    # 实现IValidationService的抽象方法
    def validate_request(self, request) -> List[str]:
        """验证请求数据"""
        if hasattr(request, 'validate'):
            return request.validate()
        return []

    def validate_file_path(self, file_path: str) -> List[str]:
        """验证文件路径"""
        errors = []

        if not file_path:
            errors.append("文件路径不能为空")
        elif not os.path.exists(file_path):
            errors.append(f"文件不存在: {file_path}")
        elif not os.access(file_path, os.R_OK):
            errors.append(f"文件不可读: {file_path}")

        return errors

    def validate_directory_path(self, directory_path: str) -> List[str]:
        """验证目录路径"""
        errors = []

        if not directory_path:
            errors.append("目录路径不能为空")
        elif not os.path.exists(directory_path):
            errors.append(f"目录不存在: {directory_path}")
        elif not os.path.isdir(directory_path):
            errors.append(f"路径不是目录: {directory_path}")
        elif not os.access(directory_path, os.W_OK):
            errors.append(f"目录不可写: {directory_path}")

        return errors

    def validate_file_operation(self, operation: str, file_paths: List[str]) -> List[str]:
        """验证文件操作"""
        errors = []

        if operation not in ["rename", "preview", "undo"]:
            errors.append(f"不支持的操作: {operation}")

        for file_path in file_paths:
            file_errors = self.validate_file_path(file_path)
            errors.extend(file_errors)

        return errors


class RenameRuleServiceImpl(BaseServiceImpl, IRenameRuleService):
    """
    重命名规则服务实现

    遵循RULE-001: 模块职责单一原则 - 只负责规则处理
    """

    def __init__(self):
        super().__init__("RenameRuleService", "1.0.0")
        self._rule_templates: Dict[str, List[RenameRule]] = {}

    async def apply_rule(self, rule: RenameRule, file_name: str) -> str:
        """应用单个重命名规则"""
        # 验证规则
        rule_errors = await self.validate_rule(rule)
        if rule_errors:
            raise ValueError(f"规则验证失败: {'; '.join(rule_errors)}")

        name, ext = os.path.splitext(file_name)

        # 根据规则类型应用不同的处理
        if rule.rule_type == RenameRuleType.REPLACE_TEXT:
            if rule.old_text and rule.new_text is not None:
                if rule.apply_to_extension:
                    ext = ext.replace(rule.old_text, rule.new_text)
                else:
                    name = name.replace(rule.old_text, rule.new_text)

        elif rule.rule_type == RenameRuleType.ADD_PREFIX:
            if rule.new_text:
                name = rule.new_text + name

        elif rule.rule_type == RenameRuleType.ADD_SUFFIX:
            if rule.new_text:
                name = name + rule.new_text

        elif rule.rule_type == RenameRuleType.REMOVE_TEXT:
            if rule.old_text:
                if rule.apply_to_extension:
                    ext = ext.replace(rule.old_text, "")
                else:
                    name = name.replace(rule.old_text, "")

        elif rule.rule_type == RenameRuleType.CHANGE_CASE:
            if rule.case_type:
                if rule.case_type == CaseType.UPPER:
                    name = name.upper()
                elif rule.case_type == CaseType.LOWER:
                    name = name.lower()
                elif rule.case_type == CaseType.TITLE:
                    name = name.title()
                elif rule.case_type == CaseType.SENTENCE:
                    name = name.capitalize()

        elif rule.rule_type == RenameRuleType.ADD_COUNTER:
            # 这个需要在批量处理时实现，这里只是占位
            pass

        elif rule.rule_type == RenameRuleType.REGEX_REPLACE:
            if rule.regex_pattern and rule.new_text is not None:
                import re
                try:
                    if rule.apply_to_extension:
                        ext = re.sub(rule.regex_pattern, rule.new_text, ext)
                    else:
                        name = re.sub(rule.regex_pattern, rule.new_text, name)
                except re.error as e:
                    raise ValueError(f"正则表达式错误: {e}")

        return name + ext

    async def apply_rules(self, rules: List[RenameRule], file_name: str) -> str:
        """应用多个重命名规则"""
        result = file_name

        for rule in rules:
            result = await self.apply_rule(rule, result)

        return result

    async def validate_rule(self, rule: RenameRule) -> List[str]:
        """验证重命名规则"""
        return rule.validate()

    async def test_rule(self, rule: RenameRule, test_names: List[str]) -> Dict[str, str]:
        """测试重命名规则"""
        results = {}

        for name in test_names:
            try:
                new_name = await self.apply_rule(rule, name)
                results[name] = new_name
            except Exception as e:
                results[name] = f"错误: {str(e)}"

        return results

    async def save_rule_template(self, name: str, rules: List[RenameRule],
                               description: str = "") -> bool:
        """保存规则模板"""
        try:
            # 验证所有规则
            for rule in rules:
                rule_errors = await self.validate_rule(rule)
                if rule_errors:
                    return False

            self._rule_templates[name] = rules
            return True

        except Exception:
            return False

    async def load_rule_template(self, name: str) -> Optional[List[RenameRule]]:
        """加载规则模板"""
        return self._rule_templates.get(name)

    async def get_rule_templates(self) -> List[Dict[str, Any]]:
        """获取所有规则模板"""
        templates = []

        for name, rules in self._rule_templates.items():
            template_info = {
                "name": name,
                "rule_count": len(rules),
                "rules": [rule.to_dict() for rule in rules]
            }
            templates.append(template_info)

        return templates

    async def delete_rule_template(self, name: str) -> bool:
        """删除规则模板"""
        if name in self._rule_templates:
            del self._rule_templates[name]
            return True
        return False

    # 实现BaseServiceImpl的抽象方法
    async def start_service(self) -> bool:
        """启动规则服务"""
        return await super().start_service()

    async def stop_service(self) -> bool:
        """停止规则服务"""
        return await super().stop_service()

    async def cancel_all_tasks(self):
        """取消所有任务（规则服务无任务）"""
        pass

    async def cancel_task(self, task_id: str) -> bool:
        """取消任务（规则服务无任务）"""
        return False

    def get_active_tasks(self) -> List[str]:
        """获取活动任务（规则服务无任务）"""
        return []
