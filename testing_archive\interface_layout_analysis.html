<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能文件管理器界面布局分析</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
        }
        
        h2 {
            color: #34495e;
            margin-top: 40px;
            margin-bottom: 20px;
            padding-left: 15px;
            border-left: 4px solid #3498db;
        }
        
        h3 {
            color: #2980b9;
            margin-top: 30px;
            margin-bottom: 15px;
        }
        
        .diagram-container {
            margin: 30px 0;
            padding: 20px;
            background: #fafafa;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            overflow-x: auto;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 14px;
        }
        
        .layout-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
        }
        
        .layout-table th,
        .layout-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        .layout-table th {
            background-color: #3498db;
            color: white;
            font-weight: bold;
        }
        
        .layout-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .feature-list {
            background: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .feature-list ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .feature-list li {
            margin: 8px 0;
        }
        
        .highlight {
            background: #fff3cd;
            padding: 15px;
            border-left: 4px solid #ffc107;
            margin: 20px 0;
            border-radius: 4px;
        }
        
        .mermaid {
            text-align: center;
            cursor: pointer;
            transition: transform 0.3s ease;
            border: 2px solid transparent;
            border-radius: 8px;
            padding: 10px;
        }

        .mermaid:hover {
            transform: scale(1.02);
            border-color: #3498db;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }

        .zoom-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.9);
            z-index: 1000;
            display: none;
            overflow: auto;
            padding: 20px;
            box-sizing: border-box;
        }

        .zoom-overlay.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .zoom-content {
            background: white;
            border-radius: 15px;
            padding: 30px;
            max-width: 95vw;
            max-height: 95vh;
            overflow: auto;
            position: relative;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }

        .zoom-controls {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 1001;
        }

        .zoom-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 8px 12px;
            margin: 2px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .zoom-btn:hover {
            background: #2980b9;
            transform: scale(1.05);
        }

        .zoom-btn.close {
            background: #e74c3c;
        }

        .zoom-btn.close:hover {
            background: #c0392b;
        }

        .zoom-hint {
            text-align: center;
            color: #7f8c8d;
            font-size: 12px;
            margin-top: 10px;
            font-style: italic;
            opacity: 0.8;
        }

        .zoom-hint:before {
            content: "💡 ";
        }

        .diagram-title {
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }

        .diagram-title {
            text-align: center;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        
        .section {
            margin-bottom: 50px;
        }
        
        .emoji {
            font-size: 1.2em;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏗️ 智能文件管理器界面布局详细分析</h1>
        
        <div class="highlight">
            <strong>📋 文档说明：</strong> 本文档详细分析了智能文件管理器的界面布局结构，包含完整的组件层次图、布局示意图和代码分析，便于理解和后续的界面调整工作。
        </div>

        <div class="section">
            <h2>🎯 1. 整体布局结构图</h2>
            <div class="diagram-container">
                <div class="diagram-title">智能文件管理器 - 完整组件层次结构</div>
                <div class="mermaid" data-title="整体布局结构图">
graph TD
    A[主窗口 root] --> B[主框架 main_frame]
    B --> C[垂直分割窗口 vertical_paned]
    
    C --> D[主内容区 paned_window<br/>水平分割]
    C --> E[底部信息区 information_frame]
    
    D --> F[左侧容器 left_container]
    D --> G[右侧区域 right_frame]
    
    F --> H[左侧Canvas left_canvas<br/>支持滚动]
    F --> I[垂直滚动条 left_vscrollbar]
    F --> J[水平滚动条 left_hscrollbar]
    
    H --> K[左侧管理面板 left_frame]
    
    K --> L[Debug状态框架 debug_status_frame]
    K --> M[目录管理区 dir_manager_frame]
    K --> N[MongoDB管理区 mongodb_frame]
    K --> O[规则管理器区 rules_frame]
    
    L --> L1[Debug按钮 debug_btn]
    L --> L2[状态面板 status_panel]
    
    M --> M1[目录列表框 dir_listbox]
    M --> M2[目录滚动条 dir_scrollbar]
    M --> M3[按钮组 btn_frame]
    
    M3 --> M3A[添加目录 btn_add_directory]
    M3 --> M3B[移除目录 btn_remove]
    M3 --> M3C[清空目录 btn_clear]
    M3 --> M3D[扫描目录 btn_scan]
    M3 --> M3E[刷新目录 btn_refresh]
    
    N --> N1[状态标签框架 status_labels_frame]
    N --> N2[按钮框架 btns_frame]
    
    N1 --> N1A[MongoDB状态 mongodb_status_label]
    N1 --> N1B[操作状态 operation_status_label]
    
    N2 --> N2A[初始化DB btn_mongodb_0]
    N2 --> N2B[读取数据 btn_mongodb_1]
    N2 --> N2C[刷新数据 btn_mongodb_2]
    N2 --> N2D[清空数据 btn_mongodb_3]
    
    O --> O1[功能选项卡 notebook]
    
    O1 --> O1A[重命名面板 rename_panel]
    O1 --> O1B[重复文件面板 duplicate_panel]
    O1 --> O1C[垃圾文件面板 junk_panel]
    O1 --> O1D[白名单面板 whitelist_panel]
    O1 --> O1E[设置面板 settings_panel]
    
    G --> P[文件树框架 file_tree_frame]
    P --> P1[文件面板框架 file_panel_frame]
    P --> P2[文件树面板 file_tree_panel]
    
    E --> Q[日志框架 log_frame]
    E --> R[状态栏 status_bar]
    
    Q --> Q1[日志文本 log_text]
    Q --> Q2[日志滚动条 log_scrollbar]
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec
    style F fill:#f1f8e9
    style G fill:#f1f8e9
    style K fill:#e3f2fd
    style O1 fill:#fff8e1
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📐 2. 界面布局示意图</h2>
            <div class="diagram-container">
                <div class="diagram-title">界面区域分布 - 可视化布局展示</div>
                <div class="mermaid" data-title="界面布局示意图">
block-beta
    columns 4
    
    block:header:4
        title["智能文件管理器主窗口"]
    end
    
    block:left_panel:2
        columns 1
        debug["🔧 Debug状态框架<br/>• Debug按钮<br/>• 状态面板"]
        dir_mgr["📁 目录管理区<br/>• 目录列表框<br/>• 添加/移除/清空/扫描/刷新按钮"]
        mongo_mgr["🗄️ MongoDB管理区<br/>• 连接状态标签<br/>• 操作状态标签<br/>• 初始化/读取/刷新/清空按钮"]
        rules_mgr["⚙️ 规则管理器区<br/>• 功能选项卡 (notebook)<br/>  - 重命名面板<br/>  - 重复文件面板<br/>  - 垃圾文件面板<br/>  - 白名单面板<br/>  - 设置面板"]
    end
    
    block:right_panel:2
        columns 1
        file_tree["🌳 文件树框架<br/>• 文件面板框架<br/>• 文件树面板<br/>• 搜索、过滤功能<br/>• 树形视图显示"]
        space2[" "]
        space3[" "]
        space4[" "]
    end
    
    block:bottom_panel:4
        columns 2
        logs["📋 日志输出区域<br/>• 日志文本显示<br/>• 日志滚动条<br/>• 高度8行，可扩展"]
        status["📊 状态栏<br/>• 进度条显示<br/>• 状态信息<br/>• 系统资源监控"]
    end
    
    style debug fill:#e3f2fd
    style dir_mgr fill:#f3e5f5
    style mongo_mgr fill:#e8f5e8
    style rules_mgr fill:#fff3e0
    style file_tree fill:#f1f8e9
    style logs fill:#fce4ec
    style status fill:#fff8e1
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📋 3. 底部信息区详细结构</h2>
            <div class="diagram-container">
                <div class="diagram-title">底部信息区 (information_frame) - 详细组件布局</div>
                <div class="mermaid" data-title="底部信息区详细结构">
graph TD
    A[底部信息区 information_frame<br/>高度: 200px 最小, 占总高度25%] --> B[日志框架 log_frame]
    A --> C[状态栏 status_bar]

    B --> B1[日志标签框架 LabelFrame<br/>标题: '日志输出']
    B1 --> B2[日志文本 log_text<br/>高度: 8行, 可扩展<br/>滚动条: log_scrollbar]

    C --> C1[状态栏主框架 frame<br/>高度: 60px 固定]

    C1 --> D[左侧区域 left_frame<br/>权重: 1/3]
    C1 --> E[中间区域 middle_frame<br/>权重: 1/3]
    C1 --> F[右侧区域 right_frame<br/>权重: 1/3]

    D --> D1[进度条 progress_bar<br/>模式: determinate<br/>变量: progress_var]
    D --> D2[中止按钮 stop_button<br/>宽度: 6<br/>状态: 可禁用]

    E --> E1[状态标签 status_label<br/>第一行, 居中对齐<br/>显示: 当前操作状态]
    E --> E2[任务信息框架 task_frame<br/>第二行, 2列布局]
    E2 --> E2A[当前任务 stats_task_label<br/>变量: task_label_var<br/>字体: 8pt]
    E2 --> E2B[子任务 stats_subtask_label<br/>变量: subtask_label_var<br/>字体: 8pt]

    F --> F1[耗时标签 elapsed_time_label<br/>第一行, 居中对齐<br/>变量: elapsed_time_var]
    F --> F2[系统信息 cpu_mem_label<br/>第二行, 居中对齐<br/>显示: CPU + 内存使用率<br/>字体: 8pt]

    style A fill:#fce4ec
    style B fill:#e8f5e8
    style C fill:#fff3e0
    style D fill:#e3f2fd
    style E fill:#f3e5f5
    style F fill:#f1f8e9
    style C1 fill:#fff8e1
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🎨 4. 状态栏3等分布局详解</h2>
            <div class="diagram-container">
                <div class="diagram-title">状态栏布局 - Grid 3等分设计</div>
                <div class="mermaid" data-title="状态栏3等分布局">
block-beta
    columns 3

    block:status_header:3
        title["状态栏 (StatusBar) - 高度: 60px"]
    end

    block:left_section:1
        columns 1
        progress["🔄 进度条<br/>• 显示当前操作进度<br/>• 支持确定/不确定模式<br/>• 实时更新百分比"]
        stop_btn["⏹️ 中止按钮<br/>• 宽度: 6字符<br/>• 可启用/禁用<br/>• 中止当前任务"]
    end

    block:middle_section:1
        columns 1
        status_info["📝 状态信息<br/>• 当前操作描述<br/>• 居中对齐显示<br/>• 实时状态更新"]
        task_info["📋 任务信息<br/>• 当前任务 | 子任务<br/>• 2列并排显示<br/>• 8pt字体大小"]
    end

    block:right_section:1
        columns 1
        time_info["⏱️ 耗时信息<br/>• 格式: X分Y秒<br/>• 居中对齐显示<br/>• 实时计时更新"]
        system_info["💻 系统信息<br/>• CPU: X% | 内存: Y%<br/>• 8pt字体大小<br/>• 资源监控显示"]
    end

    style progress fill:#e3f2fd
    style stop_btn fill:#ffebee
    style status_info fill:#f3e5f5
    style task_info fill:#f8bbd9
    style time_info fill:#e8f5e8
    style system_info fill:#c8e6c9
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🔧 5. 核心代码结构</h2>

            <h3>主要布局代码</h3>
            <div class="code-block">
# 主框架结构
self.main_frame = ttk.Frame(self.root)
self.vertical_paned = ttk.PanedWindow(self.main_frame, orient=tk.VERTICAL)
self.paned_window = ttk.PanedWindow(self.vertical_paned, orient=tk.HORIZONTAL)

# 左侧管理面板（可滚动）
self.left_container = ttk.Frame(self.paned_window)
self.left_canvas = tk.Canvas(self.left_container)
self.left_frame = ttk.Frame(self.left_canvas)

# 右侧文件树区域
self.right_frame = ttk.Frame(self.paned_window)

# 底部信息区域
self.information_frame = ttk.Frame(self.vertical_paned)
            </div>

            <h3>底部信息区布局代码</h3>
            <div class="code-block">
# 底部信息区创建和配置
self.vertical_paned.add(self.paned_window, weight=4)
self.information_frame = ttk.Frame(self.vertical_paned, style='Info.TFrame')
self.vertical_paned.add(self.information_frame, weight=1)
# 设置底部信息区的最小高度，确保状态栏可见
self.information_frame.config(height=200)

# 创建日志框架
self.log_frame = ttk.LabelFrame(self.information_frame, text="日志输出")
self.log_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=(5, 0))

# 创建状态栏
self.status_bar = StatusBar(self.information_frame)
frame = self.status_bar.frame
frame.pack(side=tk.BOTTOM, fill=tk.X, pady=(5, 0))
frame.config(height=60)  # 增加高度以容纳更多内容
frame.pack_propagate(False)
            </div>

            <h3>状态栏3等分Grid布局代码</h3>
            <div class="code-block">
# 使用grid布局实现3等分
self.frame.grid_columnconfigure(0, weight=1)  # 左侧区域
self.frame.grid_columnconfigure(1, weight=1)  # 中间区域
self.frame.grid_columnconfigure(2, weight=1)  # 右侧区域

# 左侧区域：进度条和中止按钮
left_frame = ttk.Frame(self.frame)
left_frame.grid(row=0, column=0, sticky="ew", padx=(5, 2), pady=5)
self.progress_bar = ttk.Progressbar(left_frame, variable=self.progress_var, mode="determinate")
self.progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
self.stop_button = ttk.Button(left_frame, text="中止", width=6)
self.stop_button.pack(side=tk.RIGHT)

# 中间区域：任务状态信息（2行布局）
middle_frame = ttk.Frame(self.frame)
middle_frame.grid(row=0, column=1, sticky="ew", padx=2, pady=5)
middle_frame.grid_rowconfigure(0, weight=1)
middle_frame.grid_rowconfigure(1, weight=1)
self.status_label = ttk.Label(middle_frame, text="就绪", anchor="center")
self.status_label.grid(row=0, column=0, sticky="ew", pady=(0, 2))

# 右侧区域：系统信息（2行布局）
right_frame = ttk.Frame(self.frame)
right_frame.grid(row=0, column=2, sticky="ew", padx=(2, 5), pady=5)
self.elapsed_time_label = ttk.Label(right_frame, textvariable=self.elapsed_time_var, anchor="center")
self.elapsed_time_label.grid(row=0, column=0, sticky="ew", pady=(0, 2))
self.cpu_mem_label = ttk.Label(right_frame, text="CPU: 0%  内存: 0%", anchor="center", font=("TkDefaultFont", 8))
self.cpu_mem_label.grid(row=1, column=0, sticky="ew")
            </div>

            <h3>初始分割位置设置</h3>
            <div class="code-block">
def _setup_initial_paned_position(self) -> None:
    """设置初始的分割窗口位置，确保底部信息区可见"""
    try:
        # 获取窗口总高度
        total_height = self.root.winfo_height()
        if total_height > 100:  # 确保窗口已经正确显示
            # 设置底部信息区占总高度的25%，但至少200像素
            bottom_height = max(200, int(total_height * 0.25))
            main_height = total_height - bottom_height

            # 设置垂直分割窗口的位置
            self.vertical_paned.sashpos(0, main_height)
            self.logger.info(f"设置初始分割位置: 主区域={main_height}px, 底部区域={bottom_height}px")
        else:
            # 如果窗口还没有正确显示，延迟重试
            self.root.after(100, self._setup_initial_paned_position)
    except Exception as e:
        self.logger.warning(f"设置初始分割位置失败: {e}")
        # 使用备用方案：直接设置像素位置
        try:
            self.vertical_paned.sashpos(0, 500)  # 假设主区域500像素
        except Exception:
            pass
            </div>
        </div>

        <div class="section">
            <h2>📊 6. 组件详细清单</h2>

            <table class="layout-table">
                <thead>
                    <tr>
                        <th>区域</th>
                        <th>组件名称</th>
                        <th>变量名</th>
                        <th>类型</th>
                        <th>功能描述</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td rowspan="4">🔧 Debug状态框架</td>
                        <td>Debug状态框架</td>
                        <td>debug_status_frame</td>
                        <td>ttk.Frame</td>
                        <td>容器框架</td>
                    </tr>
                    <tr>
                        <td>Debug按钮</td>
                        <td>debug_btn</td>
                        <td>ttk.Button</td>
                        <td>切换Debug模式</td>
                    </tr>
                    <tr>
                        <td>状态面板</td>
                        <td>status_panel</td>
                        <td>StatusPanel</td>
                        <td>显示系统状态</td>
                    </tr>
                    <tr>
                        <td>状态指示灯</td>
                        <td>-</td>
                        <td>StatusLight</td>
                        <td>数据库连接、Hash完整性状态</td>
                    </tr>
                    <tr>
                        <td rowspan="7">📁 目录管理区</td>
                        <td>目录管理框架</td>
                        <td>dir_manager_frame</td>
                        <td>ttk.LabelFrame</td>
                        <td>目录管理容器</td>
                    </tr>
                    <tr>
                        <td>目录列表框</td>
                        <td>dir_listbox</td>
                        <td>tk.Listbox</td>
                        <td>显示已添加目录</td>
                    </tr>
                    <tr>
                        <td>目录滚动条</td>
                        <td>dir_scrollbar</td>
                        <td>ttk.Scrollbar</td>
                        <td>目录列表滚动</td>
                    </tr>
                    <tr>
                        <td>添加目录按钮</td>
                        <td>btn_add_directory</td>
                        <td>ttk.Button</td>
                        <td>添加新目录</td>
                    </tr>
                    <tr>
                        <td>移除目录按钮</td>
                        <td>btn_remove</td>
                        <td>ttk.Button</td>
                        <td>移除选中目录</td>
                    </tr>
                    <tr>
                        <td>清空目录按钮</td>
                        <td>btn_clear</td>
                        <td>ttk.Button</td>
                        <td>清空所有目录</td>
                    </tr>
                    <tr>
                        <td>扫描目录按钮</td>
                        <td>btn_scan</td>
                        <td>ttk.Button</td>
                        <td>扫描选中目录</td>
                    </tr>
                    <tr>
                        <td rowspan="6">🗄️ MongoDB管理区</td>
                        <td>MongoDB框架</td>
                        <td>mongodb_frame</td>
                        <td>ttk.LabelFrame</td>
                        <td>数据库管理容器</td>
                    </tr>
                    <tr>
                        <td>连接状态标签</td>
                        <td>mongodb_status_label</td>
                        <td>ttk.Label</td>
                        <td>显示数据库连接状态</td>
                    </tr>
                    <tr>
                        <td>操作状态标签</td>
                        <td>operation_status_label</td>
                        <td>ttk.Label</td>
                        <td>显示当前操作状态</td>
                    </tr>
                    <tr>
                        <td>初始化DB按钮</td>
                        <td>btn_mongodb_0</td>
                        <td>ttk.Button</td>
                        <td>初始化数据库</td>
                    </tr>
                    <tr>
                        <td>读取数据按钮</td>
                        <td>btn_mongodb_1</td>
                        <td>ttk.Button</td>
                        <td>从数据库读取数据</td>
                    </tr>
                    <tr>
                        <td>刷新数据按钮</td>
                        <td>btn_mongodb_2</td>
                        <td>ttk.Button</td>
                        <td>刷新数据库统计</td>
                    </tr>
                    <tr>
                        <td rowspan="6">⚙️ 规则管理器区</td>
                        <td>规则框架</td>
                        <td>rules_frame</td>
                        <td>ttk.LabelFrame</td>
                        <td>规则管理容器</td>
                    </tr>
                    <tr>
                        <td>功能选项卡</td>
                        <td>notebook</td>
                        <td>ttk.Notebook</td>
                        <td>功能面板容器</td>
                    </tr>
                    <tr>
                        <td>重命名面板</td>
                        <td>rename_panel</td>
                        <td>RenamePanel</td>
                        <td>文件重命名功能</td>
                    </tr>
                    <tr>
                        <td>重复文件面板</td>
                        <td>duplicate_panel</td>
                        <td>DuplicatePanel</td>
                        <td>重复文件检测</td>
                    </tr>
                    <tr>
                        <td>垃圾文件面板</td>
                        <td>junk_panel</td>
                        <td>JunkPanel</td>
                        <td>垃圾文件清理</td>
                    </tr>
                    <tr>
                        <td>白名单面板</td>
                        <td>whitelist_panel</td>
                        <td>WhitelistPanel</td>
                        <td>白名单管理</td>
                    </tr>
                    <tr>
                        <td rowspan="3">🌳 文件树区域</td>
                        <td>文件树框架</td>
                        <td>file_tree_frame</td>
                        <td>ttk.LabelFrame</td>
                        <td>文件树容器</td>
                    </tr>
                    <tr>
                        <td>文件面板框架</td>
                        <td>file_panel_frame</td>
                        <td>ttk.Frame</td>
                        <td>文件统计信息</td>
                    </tr>
                    <tr>
                        <td>文件树面板</td>
                        <td>file_tree_panel</td>
                        <td>FileTreePanel</td>
                        <td>文件树显示和操作</td>
                    </tr>
                    <tr>
                        <td rowspan="20">📋 底部信息区</td>
                        <td>底部信息框架</td>
                        <td>information_frame</td>
                        <td>ttk.Frame</td>
                        <td>底部信息区容器，高度200px最小，占25%</td>
                    </tr>
                    <tr>
                        <td>日志框架</td>
                        <td>log_frame</td>
                        <td>ttk.LabelFrame</td>
                        <td>日志输出容器，标题"日志输出"</td>
                    </tr>
                    <tr>
                        <td>日志文本</td>
                        <td>log_text</td>
                        <td>tk.Text</td>
                        <td>显示应用程序日志，高度8行可扩展</td>
                    </tr>
                    <tr>
                        <td>日志滚动条</td>
                        <td>log_scrollbar</td>
                        <td>ttk.Scrollbar</td>
                        <td>日志文本滚动控制</td>
                    </tr>
                    <tr>
                        <td>状态栏主框架</td>
                        <td>status_bar.frame</td>
                        <td>ttk.Frame</td>
                        <td>状态栏容器，高度60px固定</td>
                    </tr>
                    <tr>
                        <td>左侧区域框架</td>
                        <td>left_frame</td>
                        <td>ttk.Frame</td>
                        <td>状态栏左侧1/3区域</td>
                    </tr>
                    <tr>
                        <td>进度条</td>
                        <td>progress_bar</td>
                        <td>ttk.Progressbar</td>
                        <td>显示当前操作进度，支持确定/不确定模式</td>
                    </tr>
                    <tr>
                        <td>中止按钮</td>
                        <td>stop_button</td>
                        <td>ttk.Button</td>
                        <td>中止当前任务，宽度6字符</td>
                    </tr>
                    <tr>
                        <td>中间区域框架</td>
                        <td>middle_frame</td>
                        <td>ttk.Frame</td>
                        <td>状态栏中间1/3区域，2行布局</td>
                    </tr>
                    <tr>
                        <td>状态标签</td>
                        <td>status_label</td>
                        <td>ttk.Label</td>
                        <td>显示当前操作状态，第一行居中</td>
                    </tr>
                    <tr>
                        <td>任务信息框架</td>
                        <td>task_frame</td>
                        <td>ttk.Frame</td>
                        <td>任务信息容器，第二行2列布局</td>
                    </tr>
                    <tr>
                        <td>当前任务标签</td>
                        <td>stats_task_label</td>
                        <td>ttk.Label</td>
                        <td>显示当前任务名称，8pt字体</td>
                    </tr>
                    <tr>
                        <td>子任务标签</td>
                        <td>stats_subtask_label</td>
                        <td>ttk.Label</td>
                        <td>显示当前子任务信息，8pt字体</td>
                    </tr>
                    <tr>
                        <td>右侧区域框架</td>
                        <td>right_frame</td>
                        <td>ttk.Frame</td>
                        <td>状态栏右侧1/3区域，2行布局</td>
                    </tr>
                    <tr>
                        <td>耗时标签</td>
                        <td>elapsed_time_label</td>
                        <td>ttk.Label</td>
                        <td>显示操作耗时，格式"X分Y秒"</td>
                    </tr>
                    <tr>
                        <td>CPU内存标签</td>
                        <td>cpu_mem_label</td>
                        <td>ttk.Label</td>
                        <td>显示CPU和内存使用率，8pt字体</td>
                    </tr>
                    <tr>
                        <td>进度变量</td>
                        <td>progress_var</td>
                        <td>tk.DoubleVar</td>
                        <td>进度条数值变量</td>
                    </tr>
                    <tr>
                        <td>任务标签变量</td>
                        <td>task_label_var</td>
                        <td>tk.StringVar</td>
                        <td>当前任务文本变量</td>
                    </tr>
                    <tr>
                        <td>子任务标签变量</td>
                        <td>subtask_label_var</td>
                        <td>tk.StringVar</td>
                        <td>子任务文本变量</td>
                    </tr>
                    <tr>
                        <td>耗时变量</td>
                        <td>elapsed_time_var</td>
                        <td>tk.StringVar</td>
                        <td>耗时显示文本变量</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>🎨 7. 布局特点和优势</h2>

            <div class="feature-list">
                <h3><span class="emoji">🔄</span>响应式设计</h3>
                <ul>
                    <li>使用 <code>ttk.PanedWindow</code> 实现可调整的分割线</li>
                    <li>垂直分割：主内容区 vs 底部信息区</li>
                    <li>水平分割：左侧管理面板 vs 右侧文件树</li>
                    <li>权重分配：左右比例 1:3，确保文件树有足够显示空间</li>
                </ul>
            </div>

            <div class="feature-list">
                <h3><span class="emoji">📜</span>滚动支持</h3>
                <ul>
                    <li>左侧管理面板支持垂直和水平滚动</li>
                    <li>使用 Canvas + Scrollbar 实现平滑滚动</li>
                    <li>鼠标滚轮支持：普通滚轮垂直滚动，Shift+滚轮水平滚动</li>
                    <li>自动调整滚动区域大小</li>
                </ul>
            </div>

            <div class="feature-list">
                <h3><span class="emoji">🧩</span>模块化设计</h3>
                <ul>
                    <li>每个功能区域独立封装，便于维护和扩展</li>
                    <li>使用依赖注入管理组件间的关系</li>
                    <li>事件驱动的通信机制</li>
                    <li>统一的主题和字体管理</li>
                </ul>
            </div>

            <div class="feature-list">
                <h3><span class="emoji">⚡</span>性能优化</h3>
                <ul>
                    <li>延迟加载：组件按需创建和初始化</li>
                    <li>事件防抖：避免频繁的UI更新</li>
                    <li>异步处理：耗时操作不阻塞UI线程</li>
                    <li>内存管理：及时释放不需要的资源</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>🛠️ 8. 常见调整建议</h2>

            <div class="highlight">
                <h3>如果您想要调整界面布局，可以考虑以下方面：</h3>
                <ul>
                    <li><strong>调整分割比例：</strong> 修改 <code>paned_window.add()</code> 的 weight 参数</li>
                    <li><strong>改变组件顺序：</strong> 调整 <code>pack()</code> 的顺序或使用 <code>before/after</code> 参数</li>
                    <li><strong>添加新区域：</strong> 在相应的 frame 中添加新的组件</li>
                    <li><strong>修改滚动行为：</strong> 调整 Canvas 的配置或滚动条的显示条件</li>
                    <li><strong>主题定制：</strong> 通过 UIThemeManager 修改颜色和样式</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>📞 9. 沟通指南</h2>

            <p>现在您可以使用以下方式与我沟通界面调整需求：</p>

            <div class="feature-list">
                <ul>
                    <li><strong>指定区域：</strong> 例如"左侧管理面板"、"文件树区域"、"底部信息区"</li>
                    <li><strong>使用组件名：</strong> 例如"dir_manager_frame"、"notebook"、"status_bar"</li>
                    <li><strong>描述具体需求：</strong> 例如"调整高度"、"改变位置"、"添加新功能"</li>
                    <li><strong>参考图表：</strong> 指向上面的图表中的具体部分</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>📋 8. 快速参考</h2>

            <div class="feature-list">
                <h3>主要文件位置：</h3>
                <ul>
                    <li><strong>主窗口：</strong> <code>src/ui/main_window.py</code></li>
                    <li><strong>文件树面板：</strong> <code>src/ui/file_tree.py</code></li>
                    <li><strong>状态栏：</strong> <code>src/ui/status_bar.py</code></li>
                    <li><strong>各功能面板：</strong> <code>src/ui/</code> 目录下的对应文件</li>
                    <li><strong>UI工厂：</strong> <code>src/ui/factory.py</code></li>
                </ul>
            </div>

            <div class="feature-list">
                <h3>关键方法：</h3>
                <ul>
                    <li><strong>创建UI：</strong> <code>create_widgets()</code></li>
                    <li><strong>创建管理面板：</strong> <code>create_management_panel()</code></li>
                    <li><strong>创建功能面板：</strong> <code>_create_panels()</code></li>
                    <li><strong>创建状态栏：</strong> <code>_create_status_bar()</code></li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            },
            block: {
                useMaxWidth: true
            }
        });

        // 等待页面加载完成后添加点击事件
        document.addEventListener('DOMContentLoaded', function() {
            // 为所有mermaid图表添加点击放大功能
            const mermaidDiagrams = document.querySelectorAll('.mermaid');

            mermaidDiagrams.forEach(function(diagram, index) {
                // 添加点击提示
                const hint = document.createElement('div');
                hint.className = 'zoom-hint';
                hint.textContent = '💡 点击图表可放大查看';
                diagram.parentNode.appendChild(hint);

                // 添加点击事件
                diagram.addEventListener('click', function() {
                    openZoomModal(diagram, index);
                });
            });
        });

        function openZoomModal(diagram, index) {
            // 创建放大模态框
            const modal = document.createElement('div');
            modal.className = 'zoom-overlay active';
            modal.id = 'zoom-modal-' + index;

            // 创建模态框内容
            const content = document.createElement('div');
            content.className = 'zoom-content';

            // 创建控制按钮
            const controls = document.createElement('div');
            controls.className = 'zoom-controls';

            const closeBtn = document.createElement('button');
            closeBtn.className = 'zoom-btn close';
            closeBtn.textContent = '✕ 关闭';
            closeBtn.onclick = function() {
                closeZoomModal(modal);
            };

            const fullscreenBtn = document.createElement('button');
            fullscreenBtn.className = 'zoom-btn';
            fullscreenBtn.textContent = '🔍 全屏';
            fullscreenBtn.onclick = function() {
                toggleFullscreen(content);
            };

            const zoomInBtn = document.createElement('button');
            zoomInBtn.className = 'zoom-btn';
            zoomInBtn.textContent = '🔍+ 放大';

            const zoomOutBtn = document.createElement('button');
            zoomOutBtn.className = 'zoom-btn';
            zoomOutBtn.textContent = '🔍- 缩小';

            controls.appendChild(zoomInBtn);
            controls.appendChild(zoomOutBtn);
            controls.appendChild(fullscreenBtn);
            controls.appendChild(closeBtn);

            // 获取图表标题
            const title = diagram.getAttribute('data-title') || '图表';
            const titleElement = document.createElement('h2');
            titleElement.textContent = title;
            titleElement.style.textAlign = 'center';
            titleElement.style.marginBottom = '20px';
            titleElement.style.color = '#2c3e50';

            // 克隆图表内容
            const clonedDiagram = diagram.cloneNode(true);
            let currentScale = 1.2;
            clonedDiagram.style.transform = `scale(${currentScale})`;
            clonedDiagram.style.margin = '20px';
            clonedDiagram.style.transition = 'transform 0.3s ease';

            // 添加缩放功能
            zoomInBtn.onclick = function() {
                currentScale = Math.min(currentScale + 0.2, 3);
                clonedDiagram.style.transform = `scale(${currentScale})`;
            };

            zoomOutBtn.onclick = function() {
                currentScale = Math.max(currentScale - 0.2, 0.5);
                clonedDiagram.style.transform = `scale(${currentScale})`;
            };

            // 组装模态框
            content.appendChild(controls);
            content.appendChild(titleElement);
            content.appendChild(clonedDiagram);
            modal.appendChild(content);

            // 添加到页面
            document.body.appendChild(modal);

            // 添加ESC键关闭功能
            const escHandler = function(e) {
                if (e.key === 'Escape') {
                    closeZoomModal(modal);
                    document.removeEventListener('keydown', escHandler);
                }
            };
            document.addEventListener('keydown', escHandler);

            // 点击背景关闭
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeZoomModal(modal);
                }
            });
        }

        function closeZoomModal(modal) {
            modal.remove();
        }

        function toggleFullscreen(element) {
            if (!document.fullscreenElement) {
                element.requestFullscreen().catch(err => {
                    console.log('无法进入全屏模式:', err);
                });
            } else {
                document.exitFullscreen();
            }
        }
    </script>
</body>
</html>
