# 智能文件管理器异步任务优化方案

## 一、现状分析

### 1. 协程任务中断与 `asyncio.CancelledError` 响应
- `AsyncTaskManager` 已实现协程任务主动检查中断信号（如 `interrupt_event.is_set()`），能正确抛出和捕获 `asyncio.CancelledError`，状态同步良好。
- `AsyncManager` 的协程任务仅依赖外部 `cancel()`，协程内部缺少对中断信号的主动检查，若协程为长耗时循环，响应不及时。
- 业务层（如文件扫描、批量处理等）部分异步任务依赖进度回调抛出异常实现中断，但不够通用，且部分循环/阻塞点可能遗漏检查。

### 2. 任务状态管理接口
- 各类任务管理器（`AsyncManager`、`AsyncTaskManager`等）各自维护任务状态，接口不统一。
- UI和业务层需分别适配不同管理器，增加维护和扩展难度。

---

## 二、优化目标

1. 所有协程任务内部都应定期检查中断信号，及时响应取消请求。
2. 所有协程任务最外层都应捕获 `asyncio.CancelledError`，保证资源释放和状态同步。
3. 业务层异步任务应支持通用的中断信号传递方式（如事件对象、标志位）。
4. 抽象统一的任务状态管理接口，对外提供统一的任务查询、取消、进度获取等API。
5. 任务状态返回统一结构，便于UI和业务层直接消费。

---

## 三、具体优化措施

### 1. 协程任务中断机制优化
- 在所有长耗时协程任务（如文件扫描、批量处理等）循环体内增加中断信号检查（如 `if interrupt_event.is_set(): raise asyncio.CancelledError`）。
- 统一使用 `try/except asyncio.CancelledError` 捕获取消异常，进行资源清理和状态同步。
- 业务层异步任务接口增加 `interrupt_event` 参数，便于外部统一中断。

### 2. 统一任务状态管理接口
- 设计 `UnifiedTaskManager` 类，封装所有异步任务的提交、状态查询、取消、进度获取等操作。
- 统一任务状态枚举（如 PENDING、RUNNING、CANCELLED、COMPLETED、FAILED）。
- 对外暴露统一API，UI和业务层只需依赖该接口。

### 3. 兼容与迁移
- 保持原有 `AsyncManager`、`AsyncTaskManager` 等接口兼容，逐步迁移到新接口。
- 业务层逐步替换为统一任务管理器。

---

## 四、分阶段优化计划

### 阶段一：协程任务中断机制梳理与补全
- [x] 检查所有协程任务实现，补全中断信号检查与 `asyncio.CancelledError` 捕获（已完成：所有核心批量文件操作、视频分析等异步任务均支持 interrupt_event 主动中断与异常捕获）
- [x] 业务层异步任务接口增加 `interrupt_event` 支持。

### 阶段二：统一任务状态管理接口设计与实现
- [x] 设计并实现 `UnifiedTaskManager`，统一任务提交、状态、取消、进度API（已完成，详见src/utils/unified_task_manager.py）
- [ ] 统一任务状态枚举与结构（已集成到UnifiedTaskManager中）

### 阶段三：业务层与UI层适配与迁移
- [x] 业务层异步任务迁移到统一任务管理器（已完成：文件扫描、批量处理、文件操作等核心异步任务均已通过UnifiedTaskManager统一管理）
- [x] UI层适配统一任务状态接口（已完成：所有UI异步任务进度、状态、取消、结果等均通过UnifiedTaskManager统一管理）

### 阶段四：测试与文档完善
- [ ] 完善单元测试与集成测试，确保中断与状态管理功能。
- [ ] 更新开发文档与用户手册。

---

> 本方案将分阶段推进，每完成一部分优化任务，将同步更新本方案文档及相关代码。 