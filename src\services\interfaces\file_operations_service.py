#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
文件操作服务接口

定义文件操作相关的服务接口，遵循RULE-001单一职责原则
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, AsyncGenerator, Optional


class IFileOperationsService(ABC):
    """
    文件操作服务接口
    
    遵循RULE-001: 模块职责单一原则 - 只负责文件系统操作
    遵循RULE-003: 事件驱动通信规范 - 通过事件总线通信
    遵循RULE-005: 异步任务实现 - 长时间运行的文件操作
    """
    
    @abstractmethod
    async def execute_operations(self, request) -> AsyncGenerator:
        """
        执行批量文件操作
        
        参数:
            request: 文件操作请求DTO
            
        返回:
            异步生成器，产生进度更新
        """
        pass
    
    @abstractmethod
    async def get_operation_result(self, task_id: str):
        """
        获取操作结果
        
        参数:
            task_id: 任务ID
            
        返回:
            操作结果DTO或None
        """
        pass
    
    @abstractmethod
    async def validate_operations(self, request) -> List[str]:
        """
        验证文件操作请求
        
        参数:
            request: 文件操作请求DTO
            
        返回:
            验证错误列表
        """
        pass
    
    @abstractmethod
    async def preview_operations(self, request) -> List:
        """
        预览文件操作
        
        参数:
            request: 文件操作请求DTO
            
        返回:
            操作预览列表
        """
        pass
    
    @abstractmethod
    async def cancel_operations(self, task_id: str) -> bool:
        """
        取消文件操作
        
        参数:
            task_id: 任务ID
            
        返回:
            是否成功取消
        """
        pass
    
    @abstractmethod
    async def rollback_operations(self, task_id: str) -> bool:
        """
        回滚文件操作
        
        参数:
            task_id: 任务ID
            
        返回:
            是否成功回滚
        """
        pass
    
    @abstractmethod
    async def get_operation_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """
        获取操作历史
        
        参数:
            limit: 限制返回数量
            
        返回:
            操作历史列表
        """
        pass
