#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
修复数据库数据不一致问题

主要问题：
1. 数据库使用path字段，但FileInfo模型使用file_path字段
2. 数据库中缺少新增的字段：folder_id, tree_node_id, relative_path, depth
3. 索引已经为新字段创建，但数据中没有这些字段
"""

import uuid
from pathlib import Path
from src.data.db_manager import MongoDBManager
from src.utils.format_utils import _normalize_path

def fix_database_inconsistency():
    """修复数据库不一致问题"""
    try:
        # 创建数据库管理器
        db = MongoDBManager()
        
        print("开始修复数据库不一致问题...")
        
        # 1. 检查当前数据状态
        print("\n=== 检查当前数据状态 ===")
        files = db.find_files({})
        print(f"总文件记录数: {len(files)}")
        
        if not files:
            print("数据库中没有文件记录，无需修复")
            return
        
        # 统计需要修复的记录
        need_field_update = 0
        need_path_fix = 0
        
        for file_record in files:
            # 检查是否缺少新字段
            if not all(field in file_record for field in ['folder_id', 'tree_node_id', 'relative_path', 'depth']):
                need_field_update += 1
            
            # 检查是否有file_path字段（应该没有，因为数据库用的是path）
            if 'file_path' not in file_record and 'path' in file_record:
                need_path_fix += 1
        
        print(f"需要添加新字段的记录: {need_field_update}")
        print(f"需要字段名修复的记录: {need_path_fix}")
        
        # 2. 批量更新记录，添加缺失字段
        print("\n=== 开始批量更新记录 ===")
        
        batch_size = 100
        updated_count = 0
        
        for i in range(0, len(files), batch_size):
            batch = files[i:i + batch_size]
            update_operations = []
            
            for file_record in batch:
                file_id = file_record.get('file_id')
                if not file_id:
                    continue
                
                # 准备更新数据
                update_data = {}
                
                # 添加file_path字段（从path字段复制）
                if 'path' in file_record and 'file_path' not in file_record:
                    update_data['file_path'] = file_record['path']
                
                # 添加缺失的新字段
                if 'folder_id' not in file_record:
                    update_data['folder_id'] = None
                
                if 'tree_node_id' not in file_record:
                    update_data['tree_node_id'] = None
                
                if 'relative_path' not in file_record:
                    # 计算相对路径（简化版本，可以后续优化）
                    file_path = file_record.get('path', '')
                    if file_path:
                        path_obj = Path(file_path)
                        update_data['relative_path'] = path_obj.name
                    else:
                        update_data['relative_path'] = None
                
                if 'depth' not in file_record:
                    # 计算深度（根据路径分隔符数量）
                    file_path = file_record.get('path', '')
                    if file_path:
                        normalized_path = _normalize_path(file_path)
                        depth = len(Path(normalized_path).parts)
                        update_data['depth'] = max(1, depth)
                    else:
                        update_data['depth'] = 1
                
                # 如果有需要更新的数据，直接使用MongoDB的update_one方法
                if update_data:
                    try:
                        # 使用file_id字段进行查询，而不是_id字段
                        result = db.collection.update_one(
                            {"file_id": file_id},
                            {"$set": update_data}
                        )
                        if result.modified_count > 0:
                            updated_count += 1
                    except Exception as e:
                        print(f"更新文件 {file_id} 失败: {e}")
            
            print(f"已处理 {min(i + batch_size, len(files))}/{len(files)} 条记录")
        
        print(f"\n成功更新了 {updated_count} 条记录")
        
        # 3. 验证修复结果
        print("\n=== 验证修复结果 ===")
        updated_files = db.find_files({})
        
        if updated_files:
            sample_record = updated_files[0]
            print("修复后的示例记录字段:")
            
            required_fields = ['path', 'file_path', 'name', 'size', 'extension', 
                             'folder_id', 'tree_node_id', 'relative_path', 'depth']
            
            for field in required_fields:
                if field in sample_record:
                    print(f"  ✓ {field}: {sample_record[field]}")
                else:
                    print(f"  ✗ {field}: 字段仍然缺失")
        
        # 4. 更新FileInfo模型的to_dict方法建议
        print("\n=== 修复建议 ===")
        print("1. 数据库字段已统一，现在同时包含path和file_path字段")
        print("2. 建议修改FileInfo.to_dict()方法，保持向后兼容性")
        print("3. 新增字段已添加默认值，可以正常使用文件树优化功能")
        print("4. 建议在代码中统一使用file_path字段，保持path字段用于向后兼容")
        
        db.close()
        print("\n数据库不一致问题修复完成！")
        
    except Exception as e:
        print(f"修复数据库不一致问题时出错: {e}")
        import traceback
        traceback.print_exc()

def create_data_consistency_check():
    """创建数据一致性检查函数"""
    try:
        db = MongoDBManager()
        
        print("\n=== 数据一致性检查 ===")
        
        # 检查字段完整性
        files = db.find_files({})
        if not files:
            print("数据库中没有文件记录")
            return
        
        required_fields = ['path', 'file_path', 'name', 'size', 'extension', 
                          'folder_id', 'tree_node_id', 'relative_path', 'depth']
        
        field_stats = {field: 0 for field in required_fields}
        
        for file_record in files:
            for field in required_fields:
                if field in file_record:
                    field_stats[field] += 1
        
        total_records = len(files)
        print(f"总记录数: {total_records}")
        print("字段完整性统计:")
        
        for field, count in field_stats.items():
            percentage = (count / total_records) * 100 if total_records > 0 else 0
            status = "✓" if percentage == 100 else "✗"
            print(f"  {status} {field}: {count}/{total_records} ({percentage:.1f}%)")
        
        # 检查path和file_path字段一致性
        inconsistent_count = 0
        for file_record in files:
            if 'path' in file_record and 'file_path' in file_record:
                if file_record['path'] != file_record['file_path']:
                    inconsistent_count += 1
        
        if inconsistent_count > 0:
            print(f"\n⚠️  发现 {inconsistent_count} 条记录的path和file_path字段不一致")
        else:
            print("\n✓ path和file_path字段一致性检查通过")
        
        db.close()
        
    except Exception as e:
        print(f"数据一致性检查时出错: {e}")

if __name__ == "__main__":
    print("开始数据库修复流程...")
    
    # 先执行一致性检查
    print("\n步骤1: 执行修复前检查")
    create_data_consistency_check()
    
    # 执行修复
    print("\n步骤2: 执行数据修复")
    fix_database_inconsistency()
    
    # 再次执行一致性检查
    print("\n步骤3: 执行修复后检查")
    create_data_consistency_check()