============================================================
Smart File Manager 崩溃诊断报告
============================================================
诊断时间: 2025-07-30 23:24:21
Python版本: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
操作系统: nt

[系统资源状态]
当前内存使用: 36.4MB
当前线程数: 6
CPU使用率: 1.6%
内存使用范围: 36.2MB - 36.3MB

[文件树问题分析]
- 发现使用after(1, ...)的递归调用，可能导致栈溢出
- 缺少垃圾回收调用，可能导致内存泄漏

[Tkinter问题检查]
未发现Tkinter相关问题

[内存使用分析]
内存使用正常

[修复建议]
1. 减少文件树批处理大小 (batch_size从50改为10)
2. 增加垃圾回收调用频率
3. 限制同时处理的文件数量
4. 添加内存使用监控和限制
5. 优化异步处理逻辑，避免栈溢出
6. 添加异常处理和恢复机制
