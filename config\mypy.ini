[mypy]
# Python版本
python_version = 3.8

# 基本配置
warn_return_any = True
warn_unused_configs = True
warn_redundant_casts = True
warn_unused_ignores = True
warn_no_return = True
warn_unreachable = True
strict_equality = True

# 类型检查严格性
disallow_untyped_defs = True
disallow_incomplete_defs = True
check_untyped_defs = True
disallow_untyped_decorators = True
no_implicit_optional = True

# 错误处理
strict_optional = True
show_error_codes = True
show_column_numbers = True
show_error_context = True

# 导入处理
ignore_missing_imports = True
follow_imports = normal
namespace_packages = True

# 缓存
cache_dir = .mypy_cache
sqlite_cache = True
incremental = True

# 输出格式
color_output = True
error_summary = True
show_absolute_path = True

# 性能
fast_parser = True

# 插件
plugins = 

# 排除的文件和目录
exclude = (?x)(
    ^tests/.*$
    | ^tools/.*$
    | ^config/.*$
    | ^docs/.*$
    | ^\..*$
    | ^__pycache__/.*$
    | ^build/.*$
    | ^dist/.*$
)

# 针对特定模块的配置
[mypy-tests.*]
# 测试文件可以放宽类型检查
disallow_untyped_defs = False
disallow_incomplete_defs = False
check_untyped_defs = False
ignore_errors = True

[mypy-tools.*]
# 工具脚本可以放宽类型检查
disallow_untyped_defs = False
disallow_incomplete_defs = False

[mypy-setup]
# setup.py文件
ignore_errors = True

# 第三方库配置
[mypy-tkinter.*]
ignore_missing_imports = True

[mypy-PIL.*]
ignore_missing_imports = True

[mypy-pymongo.*]
ignore_missing_imports = True

[mypy-psutil.*]
ignore_missing_imports = True

[mypy-pytest.*]
ignore_missing_imports = True

[mypy-mock.*]
ignore_missing_imports = True

[mypy-unittest.mock.*]
ignore_missing_imports = True

# 项目特定配置
[mypy-src.ui.*]
# UI模块可能有一些动态特性
warn_return_any = False
disallow_any_generics = False

[mypy-src.data.models.*]
# 数据模型可能需要更严格的类型检查
strict_optional = True
disallow_any_generics = True

[mypy-src.services.*]
# 服务层需要严格的类型检查
strict_optional = True
disallow_any_generics = True
warn_return_any = True

# 错误码配置
[mypy-error-codes]
# 启用特定错误码的检查
enable_error_code = truthy-bool,redundant-expr,unused-awaitable

# 禁用特定错误码
disable_error_code = name-defined
