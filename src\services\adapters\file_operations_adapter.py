#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
文件操作服务适配器

提供向后兼容性，将新的文件操作服务适配到现有的接口
遵循RULE-001: 模块职责单一原则 - 只负责接口适配
"""

import asyncio
import time
from typing import Dict, List, Optional, Callable, Any
from pathlib import Path

from src.services.implementations.file_operations_service import FileOperationsServiceImpl
from src.data.dto.file_operations_dto import (
    FileOperationRequest, FileOperationItem, FileOperationType,
    ConflictResolution
)
from src.ui.events.event_bus import EventBus

# 常量定义
DEFAULT_CALLBACK_INTERVAL = 1.0  # 回调间隔（秒）


class FileOperationsAdapter:
    """
    文件操作服务适配器
    
    将新的FileOperationsServiceImpl适配到现有的文件操作接口
    确保向后兼容性，同时利用新架构的优势
    """
    
    def __init__(self, event_bus: Optional[EventBus] = None):
        """初始化适配器"""
        self._event_bus = event_bus or EventBus()
        self._service = FileOperationsServiceImpl(self._event_bus)
        self._running_tasks: Dict[str, Any] = {}  # 可以存储Thread或Task
        self._task_status: Dict[str, str] = {}  # 存储任务状态
        
        # 启动事件总线（如果需要）
        if not self._event_bus._running:
            self._event_bus.start()
    
    async def start(self):
        """启动适配器"""
        await self._service.start_service()
    
    async def stop(self):
        """停止适配器"""
        # 取消所有运行中的任务
        for task_id, task in self._running_tasks.items():
            if hasattr(task, 'cancel'):
                task.cancel()
            self._task_status[task_id] = "cancelled"
        
        # 等待asyncio任务完成
        async_tasks = [task for task in self._running_tasks.values() 
                      if hasattr(task, 'cancel')]
        if async_tasks:
            await asyncio.gather(*async_tasks, return_exceptions=True)
        
        await self._service.stop_service()
    
    def copy_files(self, source_files: List[str], target_directory: str,
                   progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """
        复制文件（兼容旧接口）
        
        参数:
            source_files: 源文件列表
            target_directory: 目标目录
            progress_callback: 进度回调函数
        
        返回:
            操作结果字典
        """
        # 转换为新的操作格式
        operations = []
        for source_file in source_files:
            file_name = Path(source_file).name
            target_path = str(Path(target_directory) / file_name)
            
            operation = FileOperationItem(
                source_path=source_file,
                target_path=target_path,
                operation_type=FileOperationType.COPY
            )
            operations.append(operation)
        
        request = FileOperationRequest(
            task_id=self._generate_task_id(),
            operations=operations,
            conflict_resolution=ConflictResolution.RENAME
        )
        
        # 执行操作
        try:
            return asyncio.run(self._execute_operations_async(request, progress_callback))
        except Exception as e:
            return {
                "success": False,
                "message": f"复制文件失败: {str(e)}",
                "details": {"error": str(e)}
            }
    
    def move_files(self, source_files: List[str], target_directory: str,
                   progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """
        移动文件（兼容旧接口）
        
        参数:
            source_files: 源文件列表
            target_directory: 目标目录
            progress_callback: 进度回调函数
        
        返回:
            操作结果字典
        """
        # 转换为新的操作格式
        operations = []
        for source_file in source_files:
            file_name = Path(source_file).name
            target_path = str(Path(target_directory) / file_name)
            
            operation = FileOperationItem(
                source_path=source_file,
                target_path=target_path,
                operation_type=FileOperationType.MOVE
            )
            operations.append(operation)
        
        request = FileOperationRequest(
            task_id=self._generate_task_id(),
            operations=operations,
            conflict_resolution=ConflictResolution.RENAME,
            create_backup=True
        )
        
        # 执行操作
        try:
            return asyncio.run(self._execute_operations_async(request, progress_callback))
        except Exception as e:
            return {
                "success": False,
                "message": f"移动文件失败: {str(e)}",
                "details": {"error": str(e)}
            }
    
    def delete_files(self, files: List[str], 
                     progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """
        删除文件（兼容旧接口）
        
        参数:
            files: 文件列表
            progress_callback: 进度回调函数
        
        返回:
            操作结果字典
        """
        # 转换为新的操作格式
        operations = []
        for file_path in files:
            operation = FileOperationItem(
                source_path=file_path,
                target_path="",  # 删除操作不需要目标路径
                operation_type=FileOperationType.DELETE
            )
            operations.append(operation)
        
        request = FileOperationRequest(
            task_id=self._generate_task_id(),
            operations=operations,
            create_backup=True
        )
        
        # 执行操作
        try:
            return asyncio.run(self._execute_operations_async(request, progress_callback))
        except Exception as e:
            return {
                "success": False,
                "message": f"删除文件失败: {str(e)}",
                "details": {"error": str(e)}
            }
    
    def create_directory(self, directory_path: str) -> Dict[str, Any]:
        """
        创建目录（兼容旧接口）
        
        参数:
            directory_path: 目录路径
        
        返回:
            操作结果字典
        """
        operation = FileOperationItem(
            source_path="",  # 创建目录不需要源路径
            target_path=directory_path,
            operation_type=FileOperationType.CREATE_DIRECTORY
        )
        
        request = FileOperationRequest(
            task_id=self._generate_task_id(),
            operations=[operation]
        )
        
        # 执行操作
        try:
            return asyncio.run(self._execute_operations_async(request))
        except Exception as e:
            return {
                "success": False,
                "message": f"创建目录失败: {str(e)}",
                "details": {"error": str(e)}
            }
    
    async def _execute_operations_async(self, request: FileOperationRequest,
                                       progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """
        异步执行操作（内部方法）
        
        参数:
            request: 文件操作请求
            progress_callback: 进度回调函数
        
        返回:
            操作结果字典
        """
        result_dict = {"success": False, "message": "", "details": {}}
        
        try:
            # 收集进度更新
            progress_updates = []
            async for progress in self._service.execute_operations(request):
                progress_updates.append(progress)
                
                # 调用进度回调
                if progress_callback:
                    try:
                        progress_callback(progress.progress, progress.status_message)
                    except Exception:
                        pass  # 忽略回调错误
                
                if progress.progress >= 100.0:
                    break
            
            # 获取结果
            result = await self._service.get_operation_result(request.task_id)
            if result:
                result_dict = {
                    "success": result.successful_operations > 0,
                    "message": f"成功处理 {result.successful_operations} 个文件",
                    "details": {
                        "total_operations": result.total_operations,
                        "successful": result.successful_operations,
                        "failed": result.failed_operations,
                        "skipped": result.skipped_operations,
                        "duration": result.duration,
                        "success_rate": result.success_rate,
                        "bytes_processed": result.total_bytes_processed
                    }
                }
        
        except Exception as e:
            result_dict = {
                "success": False,
                "message": f"操作失败: {str(e)}",
                "details": {"error": str(e)}
            }
        
        return result_dict
    
    def _generate_task_id(self) -> str:
        """生成任务ID"""
        import uuid
        return str(uuid.uuid4())
    
    def get_task_status(self, task_id: str) -> Optional[str]:
        """获取任务状态（兼容旧接口）"""
        return self._task_status.get(task_id)
    
    async def get_task_result(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务结果（兼容旧接口）"""
        result = await self._service.get_operation_result(task_id)
        if result:
            return {
                "task_id": result.task_id,
                "total_operations": result.total_operations,
                "successful": result.successful_operations,
                "failed": result.failed_operations,
                "skipped": result.skipped_operations,
                "duration": result.duration,
                "success_rate": result.success_rate,
                "bytes_processed": result.total_bytes_processed,
                "errors": result.errors or [],
                "warnings": result.warnings or []
            }
        return None
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务（兼容旧接口）"""
        try:
            return asyncio.run(self._service.cancel_operations(task_id))
        except Exception:
            return False


# 向后兼容性函数
def copy_files_sync(source_files: List[str], target_directory: str,
                   progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
    """
    向后兼容的同步复制文件函数
    
    使用新的服务架构，但保持旧的接口
    """
    adapter = FileOperationsAdapter()
    
    try:
        return adapter.copy_files(source_files, target_directory, progress_callback)
    except Exception as e:
        return {
            "success": False,
            "message": f"复制文件失败: {str(e)}",
            "details": {"error": str(e)}
        }


def move_files_sync(source_files: List[str], target_directory: str,
                   progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
    """
    向后兼容的同步移动文件函数
    
    使用新的服务架构，但保持旧的接口
    """
    adapter = FileOperationsAdapter()
    
    try:
        return adapter.move_files(source_files, target_directory, progress_callback)
    except Exception as e:
        return {
            "success": False,
            "message": f"移动文件失败: {str(e)}",
            "details": {"error": str(e)}
        }


def delete_files_sync(files: List[str],
                     progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
    """
    向后兼容的同步删除文件函数
    
    使用新的服务架构，但保持旧的接口
    """
    adapter = FileOperationsAdapter()
    
    try:
        return adapter.delete_files(files, progress_callback)
    except Exception as e:
        return {
            "success": False,
            "message": f"删除文件失败: {str(e)}",
            "details": {"error": str(e)}
        }


# 全局适配器实例（用于向后兼容）
_global_adapter: Optional[FileOperationsAdapter] = None


def get_global_file_operations_adapter() -> FileOperationsAdapter:
    """获取全局文件操作服务适配器"""
    global _global_adapter
    if _global_adapter is None:
        _global_adapter = FileOperationsAdapter()
    return _global_adapter
