#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Database Cleaner 安装配置

通用数据库清理工具的安装脚本。

作者: AI助手
日期: 2024-01-01
版本: 1.0.0
"""

from setuptools import setup, find_packages
from pathlib import Path

# 读取README文件
readme_file = Path(__file__).parent / "db_cleaner" / "README.md"
if readme_file.exists():
    with open(readme_file, "r", encoding="utf-8") as f:
        long_description = f.read()
else:
    long_description = "通用数据库清理工具，支持多种数据库类型和清理策略。"

# 读取版本信息
version_file = Path(__file__).parent / "db_cleaner" / "__init__.py"
version = "1.0.0"
if version_file.exists():
    with open(version_file, "r", encoding="utf-8") as f:
        for line in f:
            if line.startswith("__version__"):
                version = line.split("=")[1].strip().strip('"').strip("'")
                break

# 基础依赖
install_requires = [
    "pyyaml>=5.4.0",
    "toml>=0.10.0",
    "click>=8.0.0",
]

# 可选依赖
extras_require = {
    "mongodb": [
        "motor>=3.0.0",
        "pymongo>=4.0.0",
    ],
    "mysql": [
        "aiomysql>=0.1.0",
        "pymysql>=1.0.0",
    ],
    "postgresql": [
        "asyncpg>=0.27.0",
    ],
    "all": [
        "motor>=3.0.0",
        "pymongo>=4.0.0",
        "aiomysql>=0.1.0", 
        "pymysql>=1.0.0",
        "asyncpg>=0.27.0",
    ],
    "dev": [
        "pytest>=7.0.0",
        "pytest-asyncio>=0.21.0",
        "pytest-cov>=4.0.0",
        "black>=22.0.0",
        "flake8>=5.0.0",
        "mypy>=1.0.0",
        "pre-commit>=2.20.0",
    ],
    "docs": [
        "sphinx>=5.0.0",
        "sphinx-rtd-theme>=1.0.0",
        "myst-parser>=0.18.0",
    ]
}

setup(
    name="db-cleaner",
    version=version,
    author="AI Assistant",
    author_email="<EMAIL>",
    description="通用数据库清理工具",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/your-repo/db-cleaner",
    project_urls={
        "Bug Reports": "https://github.com/your-repo/db-cleaner/issues",
        "Source": "https://github.com/your-repo/db-cleaner",
        "Documentation": "https://db-cleaner.readthedocs.io",
    },
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: System Administrators",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Database",
        "Topic :: System :: Systems Administration",
        "Topic :: Utilities",
    ],
    keywords="database cleanup cleaner mongodb mysql postgresql maintenance",
    python_requires=">=3.8",
    install_requires=install_requires,
    extras_require=extras_require,
    entry_points={
        "console_scripts": [
            "db-cleaner=db_cleaner.cli:main",
        ],
    },
    include_package_data=True,
    package_data={
        "db_cleaner": [
            "examples/*.yaml",
            "examples/*.json",
            "examples/*.toml",
        ],
    },
    zip_safe=False,
)
