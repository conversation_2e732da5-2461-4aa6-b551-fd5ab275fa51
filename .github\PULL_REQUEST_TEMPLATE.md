# Pull Request 检查清单

## 📝 变更说明
请简要描述此PR的主要变更内容：

- [ ] 新功能开发
- [ ] Bug修复
- [ ] 性能优化
- [ ] 代码重构
- [ ] 文档更新
- [ ] 测试改进
- [ ] 其他: ___________

### 变更详情
<!-- 请详细描述变更的内容、原因和影响 -->


### 相关Issue
<!-- 如果此PR解决了某个Issue，请在此处引用 -->
关联Issue: #

## ✅ 代码质量检查

### 基础检查
- [ ] 代码通过所有自动化测试
- [ ] 代码通过规则检查器验证 (`python tools/code_checker.py --check-rules`)
- [ ] 代码覆盖率不低于80%
- [ ] 所有新增代码有适当的文档和注释
- [ ] 代码格式符合项目规范 (Black + isort)
- [ ] 通过类型检查 (MyPy)
- [ ] 通过代码质量检查 (Pylint)
- [ ] 通过安全检查 (Bandit)

### 架构合规性检查
- [ ] **模块职责单一**: 每个模块只负责一个明确的职责
- [ ] **DTO数据传递**: 模块间使用DTO对象进行数据传递
- [ ] **事件驱动通信**: 通过事件总线进行模块间通信
- [ ] **数据库操作集中**: 数据库操作集中在DatabaseService
- [ ] **UI业务分离**: UI层无业务逻辑，业务层无UI操作
- [ ] **依赖注入**: 正确使用依赖注入，避免硬编码依赖
- [ ] **接口依赖**: 依赖接口而非具体实现

### 性能合规性检查
- [ ] **大文件处理**: 大文件使用分块处理 (8MB-32MB块)
- [ ] **批量数据库操作**: 数据库操作使用批量处理
- [ ] **内存使用控制**: 内存使用在限制范围内 (总计<2GB, 单任务<512MB)
- [ ] **异步任务**: 长时间操作实现为异步任务
- [ ] **索引查询**: 正确使用索引进行数据库查询
- [ ] **并发控制**: 并发数量在合理范围内
- [ ] **UI响应性**: UI操作响应时间 < 100ms

### 错误处理合规性检查
- [ ] **异常捕获**: 所有异常都被正确捕获和处理
- [ ] **错误信息**: 错误信息结构化且用户友好
- [ ] **重试机制**: 关键操作有重试机制
- [ ] **日志记录**: 错误被正确记录到日志
- [ ] **异常安全**: 异常不会导致程序崩溃或数据损坏

## 🧪 测试要求

### 测试覆盖
- [ ] **单元测试**: 新增功能有对应的单元测试
- [ ] **集成测试**: 模块间交互有集成测试覆盖
- [ ] **测试更新**: 修改的功能更新了相关测试
- [ ] **边界测试**: 包含边界条件和异常场景测试
- [ ] **性能测试**: 性能相关变更有性能测试 (如适用)

### 测试质量
- [ ] 测试用例设计合理，覆盖主要场景
- [ ] 测试数据管理合理，无硬编码测试数据
- [ ] 测试环境隔离，测试间无相互影响
- [ ] 测试执行稳定，无随机失败

## 📚 文档更新

### 代码文档
- [ ] **API文档**: 新增API有完整的文档字符串
- [ ] **注释更新**: 复杂逻辑有清晰的注释说明
- [ ] **参数说明**: 参数和返回值说明清晰
- [ ] **使用示例**: 包含使用示例 (如适用)
- [ ] **异常说明**: 异常情况有说明

### 项目文档
- [ ] **架构文档**: 重大架构变更更新了设计文档
- [ ] **用户文档**: 用户可见功能更新了使用说明
- [ ] **配置文档**: 配置变更更新了配置说明
- [ ] **部署文档**: 部署相关变更更新了部署指南

## 🔒 安全性检查

### 数据安全
- [ ] **输入验证**: 所有外部输入都经过验证
- [ ] **SQL注入防护**: 使用参数化查询，防止SQL注入
- [ ] **路径遍历防护**: 文件路径操作有安全检查
- [ ] **文件类型限制**: 文件上传有类型和大小限制

### 敏感信息
- [ ] **无硬编码密钥**: 无硬编码密码、API密钥等敏感信息
- [ ] **敏感信息加密**: 敏感信息正确加密存储
- [ ] **日志安全**: 日志中无敏感信息泄露
- [ ] **错误信息安全**: 错误信息不泄露内部实现细节

## 🎯 性能影响评估

### 性能指标
- [ ] **响应时间**: 不会显著增加响应时间
- [ ] **内存使用**: 不会显著增加内存使用
- [ ] **CPU使用**: 不会显著增加CPU使用
- [ ] **磁盘I/O**: 不会显著增加磁盘I/O

### 性能测试结果
<!-- 如果进行了性能测试，请在此处提供结果 -->
```
性能测试结果:
- 响应时间: 
- 内存使用: 
- CPU使用: 
- 吞吐量: 
```

## 🔄 向后兼容性

- [ ] **API兼容**: 不破坏现有API接口
- [ ] **数据兼容**: 不破坏现有数据格式
- [ ] **配置兼容**: 不破坏现有配置文件
- [ ] **用户体验**: 不显著改变用户操作流程

## 🧪 测试说明

### 测试环境
<!-- 描述测试环境和测试数据 -->

### 测试步骤
<!-- 描述如何测试这些变更 -->
1. 
2. 
3. 

### 预期结果
<!-- 描述预期的测试结果 -->

## 📋 部署说明

### 部署要求
- [ ] **依赖更新**: 无新的依赖要求，或已更新requirements.txt
- [ ] **配置变更**: 无配置变更，或已提供迁移说明
- [ ] **数据库变更**: 无数据库变更，或已提供迁移脚本
- [ ] **环境要求**: 无新的环境要求

### 部署步骤
<!-- 如果有特殊的部署步骤，请在此说明 -->

## 🚨 风险评估

### 潜在风险
- [ ] **功能风险**: 评估了功能回归风险
- [ ] **性能风险**: 评估了性能下降风险
- [ ] **安全风险**: 评估了安全漏洞风险
- [ ] **兼容性风险**: 评估了向后兼容性风险

### 风险缓解措施
<!-- 描述已采取的风险缓解措施 -->

## 📊 审查者检查清单

### 代码审查要点
- [ ] 代码逻辑正确且高效
- [ ] 代码风格符合项目规范
- [ ] 错误处理完善
- [ ] 测试覆盖充分
- [ ] 文档完整准确

### 架构审查要点
- [ ] 架构设计合理
- [ ] 模块职责清晰
- [ ] 依赖关系正确
- [ ] 接口设计稳定
- [ ] 扩展性良好

## ✅ 最终确认

### 提交者确认
- [ ] 我已仔细检查了所有变更
- [ ] 我已运行了所有相关测试
- [ ] 我已更新了相关文档
- [ ] 我确认此PR可以安全合并

### 审查者确认
- [ ] 代码质量符合标准
- [ ] 架构设计合理
- [ ] 测试覆盖充分
- [ ] 文档完整准确
- [ ] 同意合并此PR

---

## 📞 联系信息
如有问题，请联系：
- 提交者: @username
- 审查者: @reviewer
- 架构师: @architect

**注意**: 请确保所有检查项都已完成后再请求审查。未完成的检查项可能导致PR被拒绝。
