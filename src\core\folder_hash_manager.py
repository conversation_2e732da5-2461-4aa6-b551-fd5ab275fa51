#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
文件夹哈希管理器

该模块实现文件夹内容哈希计算和管理功能:
- FolderHashManager: 文件夹哈希管理类
- 基于现有TreeOptimizationManager的_calculate_content_hash方法优化
- 支持增量更新、缓存和异步处理
- 实现数据一致性检查机制

作者: AI助手
日期: 2024-01-01
版本: 1.0.0
"""

import asyncio
import hashlib
import os
from typing import Dict, Any, Optional, List, Set, Tuple
from datetime import datetime
from pathlib import Path
import time
from concurrent.futures import ThreadPoolExecutor
from src.utils.logger import get_logger
from src.utils.format_utils import _normalize_path

logger = get_logger(__name__)


class FolderHashManager:
    """
    文件夹哈希管理器
    
    实现文件夹内容的高效哈希计算、缓存和一致性检查
    """
    
    def __init__(self, cache_size: int = 10000, enable_cache: bool = True):
        """
        初始化文件夹哈希管理器
        
        参数:
            cache_size: 缓存大小
            enable_cache: 是否启用缓存
        """
        self.cache_size = cache_size
        self.enable_cache = enable_cache
        self.logger = logger
        
        # 哈希缓存：{folder_path: (hash_value, last_modified, file_count)}
        self.hash_cache: Dict[str, Tuple[str, float, int]] = {}
        self.cache_access_order: List[str] = []
        
        # 线程池用于I/O密集型操作
        self.executor = ThreadPoolExecutor(max_workers=4, thread_name_prefix="FolderHash")
        
        # 统计信息
        self.cache_hits = 0
        self.cache_misses = 0
        self.hash_calculations = 0
    
    async def calculate_folder_hash(self, folder_path: str, 
                                  include_metadata: bool = False,
                                  force_recalculate: bool = False) -> Optional[str]:
        """
        计算文件夹内容哈希
        
        参数:
            folder_path: 文件夹路径
            include_metadata: 是否包含元数据（大小、修改时间）
            force_recalculate: 是否强制重新计算
        
        返回:
            哈希值字符串，失败时返回None
        """
        try:
            folder_path = _normalize_path(folder_path)
            
            # 检查缓存
            if not force_recalculate and self.enable_cache:
                cached_hash = await self._get_cached_hash(folder_path)
                if cached_hash:
                    self.cache_hits += 1
                    return cached_hash
            
            self.cache_misses += 1
            self.hash_calculations += 1
            
            # 扫描文件夹内容
            contents = await self._scan_folder_contents(folder_path)
            if contents is None:
                return None
            
            # 计算哈希
            hash_value = await self._calculate_content_hash(
                contents["folders"], 
                contents["files"], 
                include_metadata, 
                contents.get("file_sizes", [])
            )
            
            # 更新缓存
            if self.enable_cache and hash_value:
                await self._update_cache(folder_path, hash_value, contents)
            
            return hash_value
            
        except Exception as e:
            self.logger.error(f"计算文件夹哈希失败 {folder_path}: {e}")
            return None
    
    async def _scan_folder_contents(self, folder_path: str) -> Optional[Dict[str, Any]]:
        """
        扫描文件夹内容
        
        参数:
            folder_path: 文件夹路径
        
        返回:
            包含文件夹和文件列表的字典
        """
        try:
            path_obj = Path(folder_path)
            if not path_obj.exists() or not path_obj.is_dir():
                self.logger.warning(f"文件夹不存在或不是目录: {folder_path}")
                return None
            
            # 在线程池中执行I/O操作
            loop = asyncio.get_event_loop()
            contents = await loop.run_in_executor(
                self.executor, 
                self._scan_folder_sync, 
                folder_path
            )
            
            return contents
            
        except Exception as e:
            self.logger.error(f"扫描文件夹内容失败 {folder_path}: {e}")
            return None
    
    def _scan_folder_sync(self, folder_path: str) -> Dict[str, Any]:
        """
        同步扫描文件夹内容
        
        参数:
            folder_path: 文件夹路径
        
        返回:
            包含文件夹和文件信息的字典
        """
        folders = []
        files = []
        file_sizes = []
        total_size = 0
        last_modified = 0
        
        try:
            path_obj = Path(folder_path)
            
            for item in path_obj.iterdir():
                try:
                    if item.is_file():
                        files.append(str(item))
                        file_size = item.stat().st_size
                        file_sizes.append(file_size)
                        total_size += file_size
                        last_modified = max(last_modified, item.stat().st_mtime)
                    elif item.is_dir():
                        folders.append(str(item))
                        last_modified = max(last_modified, item.stat().st_mtime)
                except (OSError, PermissionError):
                    # 忽略无法访问的项目
                    continue
            
            return {
                "folders": folders,
                "files": files,
                "file_sizes": file_sizes,
                "total_size": total_size,
                "last_modified": last_modified,
                "file_count": len(files),
                "folder_count": len(folders)
            }
            
        except Exception as e:
            self.logger.error(f"同步扫描文件夹失败 {folder_path}: {e}")
            return {
                "folders": [],
                "files": [],
                "file_sizes": [],
                "total_size": 0,
                "last_modified": 0,
                "file_count": 0,
                "folder_count": 0
            }
    
    async def _calculate_content_hash(self, folders: List[str], files: List[str], 
                                    include_metadata: bool = False,
                                    file_sizes: Optional[List[int]] = None) -> str:
        """
        计算内容哈希（基于现有TreeOptimizationManager的实现优化）
        
        参数:
            folders: 子文件夹列表
            files: 文件列表
            include_metadata: 是否包含元数据
            file_sizes: 文件大小列表
        
        返回:
            哈希值字符串
        """
        try:
            # 提取名称并排序（保持与现有实现一致）
            folder_names = sorted([os.path.basename(f) for f in folders])
            file_names = sorted([os.path.basename(f) for f in files])
            
            # 构建哈希组件
            hash_components = []
            
            # 添加文件夹名称
            if folder_names:
                hash_components.append(f"folders:{','.join(folder_names)}")
            
            # 添加文件名称
            if file_names:
                hash_components.append(f"files:{','.join(file_names)}")
            
            # 可选：添加元数据信息
            if include_metadata and file_sizes:
                total_size = sum(file_sizes)
                file_count = len(file_names)
                hash_components.append(f"meta:{file_count}:{total_size}")
            
            # 组合内容（保持与现有实现兼容）
            if not hash_components:
                content = ""
            elif len(hash_components) == 1:
                content = hash_components[0]
            else:
                content = "|".join(hash_components)
            
            # 计算MD5哈希
            return hashlib.md5(content.encode('utf-8')).hexdigest()
            
        except Exception as e:
            self.logger.warning(f"计算内容哈希失败: {e}")
            return ""
    
    async def _get_cached_hash(self, folder_path: str) -> Optional[str]:
        """
        从缓存获取哈希值
        
        参数:
            folder_path: 文件夹路径
        
        返回:
            缓存的哈希值，如果不存在或已过期则返回None
        """
        try:
            if folder_path not in self.hash_cache:
                return None
            
            cached_hash, cached_modified, cached_count = self.hash_cache[folder_path]
            
            # 检查文件夹是否被修改
            path_obj = Path(folder_path)
            if not path_obj.exists():
                # 文件夹不存在，移除缓存
                self._remove_from_cache(folder_path)
                return None
            
            current_modified = path_obj.stat().st_mtime
            
            # 简单检查：如果修改时间相同，认为内容未变化
            if abs(current_modified - cached_modified) < 1.0:  # 1秒误差
                # 更新访问顺序
                self._update_cache_access(folder_path)
                return cached_hash
            
            # 修改时间不同，移除缓存
            self._remove_from_cache(folder_path)
            return None
            
        except Exception as e:
            self.logger.debug(f"获取缓存哈希失败 {folder_path}: {e}")
            return None

    async def _update_cache(self, folder_path: str, hash_value: str,
                          contents: Dict[str, Any]) -> None:
        """
        更新缓存

        参数:
            folder_path: 文件夹路径
            hash_value: 哈希值
            contents: 文件夹内容信息
        """
        try:
            # 检查缓存大小
            if len(self.hash_cache) >= self.cache_size:
                # 移除最久未使用的项
                if self.cache_access_order:
                    oldest_path = self.cache_access_order.pop(0)
                    self.hash_cache.pop(oldest_path, None)

            # 添加到缓存
            self.hash_cache[folder_path] = (
                hash_value,
                contents.get("last_modified", time.time()),
                contents.get("file_count", 0)
            )

            # 更新访问顺序
            self._update_cache_access(folder_path)

        except Exception as e:
            self.logger.debug(f"更新缓存失败 {folder_path}: {e}")

    def _update_cache_access(self, folder_path: str) -> None:
        """
        更新缓存访问顺序

        参数:
            folder_path: 文件夹路径
        """
        try:
            # 移除旧的访问记录
            if folder_path in self.cache_access_order:
                self.cache_access_order.remove(folder_path)

            # 添加到末尾（最近访问）
            self.cache_access_order.append(folder_path)

        except Exception as e:
            self.logger.debug(f"更新缓存访问顺序失败 {folder_path}: {e}")

    def _remove_from_cache(self, folder_path: str) -> None:
        """
        从缓存中移除项目

        参数:
            folder_path: 文件夹路径
        """
        try:
            self.hash_cache.pop(folder_path, None)
            if folder_path in self.cache_access_order:
                self.cache_access_order.remove(folder_path)
        except Exception as e:
            self.logger.debug(f"从缓存移除失败 {folder_path}: {e}")

    async def verify_folder_consistency(self, folder_path: str,
                                      stored_hash: str) -> Dict[str, Any]:
        """
        验证文件夹一致性

        参数:
            folder_path: 文件夹路径
            stored_hash: 存储的哈希值

        返回:
            验证结果字典
        """
        try:
            self.logger.debug(f"验证文件夹一致性: {folder_path}")

            # 计算当前哈希
            current_hash = await self.calculate_folder_hash(folder_path, force_recalculate=True)

            if current_hash is None:
                return {
                    "consistent": False,
                    "reason": "calculation_failed",
                    "stored_hash": stored_hash,
                    "current_hash": None
                }

            # 比较哈希值
            consistent = current_hash == stored_hash

            result = {
                "consistent": consistent,
                "reason": "match" if consistent else "hash_mismatch",
                "stored_hash": stored_hash,
                "current_hash": current_hash
            }

            if not consistent:
                # 获取详细的变化信息
                contents = await self._scan_folder_contents(folder_path)
                if contents:
                    result.update({
                        "file_count": contents.get("file_count", 0),
                        "folder_count": contents.get("folder_count", 0),
                        "total_size": contents.get("total_size", 0)
                    })

            return result

        except Exception as e:
            self.logger.error(f"验证文件夹一致性失败 {folder_path}: {e}")
            return {
                "consistent": False,
                "reason": "verification_error",
                "error": str(e),
                "stored_hash": stored_hash,
                "current_hash": None
            }

    async def batch_calculate_hashes(self, folder_paths: List[str],
                                   max_concurrent: int = 5) -> Dict[str, Optional[str]]:
        """
        批量计算文件夹哈希

        参数:
            folder_paths: 文件夹路径列表
            max_concurrent: 最大并发数

        返回:
            路径到哈希值的映射字典
        """
        try:
            semaphore = asyncio.Semaphore(max_concurrent)

            async def calculate_single(path: str) -> Tuple[str, Optional[str]]:
                async with semaphore:
                    hash_value = await self.calculate_folder_hash(path)
                    return path, hash_value

            # 创建任务
            tasks = [calculate_single(path) for path in folder_paths]

            # 执行任务
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 处理结果
            hash_map = {}
            for result in results:
                if isinstance(result, Exception):
                    self.logger.error(f"批量计算哈希时出错: {result}")
                    continue

                path, hash_value = result
                hash_map[path] = hash_value

            return hash_map

        except Exception as e:
            self.logger.error(f"批量计算哈希失败: {e}")
            return {}

    def clear_cache(self) -> None:
        """清空缓存"""
        self.hash_cache.clear()
        self.cache_access_order.clear()
        self.logger.info("文件夹哈希缓存已清空")

    def get_cache_statistics(self) -> Dict[str, Any]:
        """
        获取缓存统计信息

        返回:
            缓存统计信息字典
        """
        total_requests = self.cache_hits + self.cache_misses
        hit_rate = (self.cache_hits / total_requests * 100) if total_requests > 0 else 0

        return {
            "cache_size": len(self.hash_cache),
            "max_cache_size": self.cache_size,
            "cache_hits": self.cache_hits,
            "cache_misses": self.cache_misses,
            "hit_rate_percent": round(hit_rate, 2),
            "hash_calculations": self.hash_calculations,
            "cache_enabled": self.enable_cache
        }

    async def shutdown(self) -> None:
        """关闭哈希管理器，清理资源"""
        try:
            # 关闭线程池
            self.executor.shutdown(wait=True)

            # 清空缓存
            self.clear_cache()

            self.logger.info("文件夹哈希管理器已关闭")

        except Exception as e:
            self.logger.error(f"关闭文件夹哈希管理器时出错: {e}")

    def __str__(self) -> str:
        """字符串表示"""
        stats = self.get_cache_statistics()
        return (f"FolderHashManager(cache_size={stats['cache_size']}/{stats['max_cache_size']}, "
                f"hit_rate={stats['hit_rate_percent']}%, calculations={stats['hash_calculations']})")
