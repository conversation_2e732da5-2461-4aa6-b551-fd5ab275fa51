#!/usr/bin/env python3
"""
快速修复数据库中的深度问题
"""

import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def calculate_correct_depth(path):
    """计算正确的文件深度"""
    if not path:
        return 1
    
    # 规范化路径
    normalized_path = path.replace('\\', '/')
    
    # 移除结尾的斜杠
    if normalized_path.endswith('/') and len(normalized_path) > 1:
        normalized_path = normalized_path[:-1]
    
    # 分割路径
    parts = [part for part in normalized_path.split('/') if part]
    
    # 深度 = 路径部分数量
    depth = len(parts)
    
    # 最小深度为1（根目录）
    return max(1, depth)

def main():
    """主函数"""
    try:
        from src.data.db_manager import MongoDBManager
        
        # 创建数据库连接
        db_manager = MongoDBManager()
        
        print("🔧 开始快速修复文件深度...")
        
        # 获取所有文件
        all_files = list(db_manager.collection.find({}))
        print(f"📊 找到 {len(all_files)} 个文件记录")
        
        # 检查和修复深度
        fixed_count = 0
        
        for file_doc in all_files:
            file_path = file_doc.get('path', '')
            current_depth = file_doc.get('depth', 1)
            correct_depth = calculate_correct_depth(file_path)
            
            print(f"📁 {file_path}")
            print(f"   当前深度: {current_depth}, 正确深度: {correct_depth}")
            
            if current_depth != correct_depth:
                # 更新深度
                result = db_manager.collection.update_one(
                    {'_id': file_doc['_id']},
                    {'$set': {'depth': correct_depth}}
                )
                
                if result.modified_count > 0:
                    print(f"   ✅ 已修复深度: {current_depth} -> {correct_depth}")
                    fixed_count += 1
                else:
                    print(f"   ❌ 修复失败")
            else:
                print(f"   ✅ 深度正确")
        
        print(f"\n🎉 修复完成！共修复 {fixed_count} 个文件的深度")
        
        # 验证修复结果
        print("\n🔍 验证修复结果:")
        updated_files = list(db_manager.collection.find({}))
        for file_doc in updated_files:
            file_path = file_doc.get('path', '')
            depth = file_doc.get('depth', 1)
            expected_depth = calculate_correct_depth(file_path)
            status = "✅" if depth == expected_depth else "❌"
            print(f"   {status} {file_path} -> 深度: {depth}")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✨ 深度修复完成！请重新加载文件树查看效果。")
    else:
        print("\n❌ 修复失败，请检查错误信息。")
