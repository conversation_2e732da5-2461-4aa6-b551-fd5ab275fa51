# 文件夹为核心架构使用指南

## 概述

本指南介绍如何使用新的文件夹为核心的文件树架构。该架构将文件夹作为主要的组织单元，提供更高效的文件管理和更好的用户体验。

## 核心组件

### 1. FolderDTO - 文件夹数据传输对象

```python
from src.data.dto.folder_dto import FolderDTO

# 从路径创建文件夹DTO
folder_dto = FolderDTO.from_path("/path/to/folder")

# 序列化和反序列化
folder_dict = folder_dto.to_dict()
folder_dto2 = FolderDTO.from_dict(folder_dict)

# 管理子文件夹
folder_dto.add_child_folder("child_folder_id")
folder_dto.remove_child_folder("child_folder_id")
```

### 2. FolderHashManager - 文件夹哈希管理器

```python
from src.core.folder_hash_manager import FolderHashManager

# 创建哈希管理器
hash_manager = FolderHashManager(cache_size=5000, enable_cache=True)

# 计算单个文件夹哈希
hash_value = await hash_manager.calculate_folder_hash("/path/to/folder")

# 批量计算哈希
folder_paths = ["/path1", "/path2", "/path3"]
hash_results = await hash_manager.batch_calculate_hashes(folder_paths)

# 验证一致性
verification = await hash_manager.verify_folder_consistency("/path", stored_hash)

# 获取统计信息
stats = hash_manager.get_cache_statistics()
```

### 3. FolderRepository - 文件夹数据访问层

```python
from src.data.folder_repository import FolderRepository

# 创建仓库实例
folder_repo = FolderRepository(db_manager)

# CRUD操作
await folder_repo.create_folder(folder_dto)
folder = await folder_repo.get_folder_by_id("folder_id")
folder = await folder_repo.get_folder_by_path("/path/to/folder")
await folder_repo.update_folder_hash("folder_id", "new_hash")
await folder_repo.delete_folder("folder_id")

# 查询操作
child_folders = await folder_repo.get_child_folders("parent_id")
folders_by_depth = await folder_repo.get_folders_by_depth(2)

# 批量操作
success_count = await folder_repo.batch_create_folders(folder_list)
```

### 4. FileTreeIndexService - 文件树索引服务

```python
from src.data.file_tree_index_service import FileTreeIndexService

# 创建服务实例
tree_service = FileTreeIndexService(db_manager)

# 构建文件夹索引
success = await tree_service.build_folder_index_async(file_list, progress_callback)

# 获取文件夹子项
children = await tree_service.get_folder_children_async("folder_id")
# 返回格式：
# {
#     "folders": [FolderDTO列表],
#     "files": [FileInfo列表],
#     "total_count": 总数,
#     "folder_count": 文件夹数,
#     "file_count": 文件数
# }

# 更新索引
await tree_service.update_index_async(changed_files, progress_callback)

# 运行一致性检查
check_result = await tree_service.run_consistency_check(auto_fix=True)
```

### 5. ConsistencyChecker - 数据一致性检查器

```python
from src.utils.consistency_checker import ConsistencyChecker

# 创建检查器
checker = ConsistencyChecker(folder_repository, hash_manager)

# 运行全面检查
report = await checker.check_all_folders(auto_fix=True, max_concurrent=5)

# 生成报告摘要
summary = checker.generate_report_summary(report)
print(summary)
```

## 使用场景

### 场景1：初始化文件夹索引

```python
async def initialize_folder_index(db_manager, file_list):
    """初始化文件夹索引"""
    
    # 创建服务
    tree_service = FileTreeIndexService(db_manager)
    
    # 定义进度回调
    def progress_callback(progress, message, current_item, current, total, elapsed):
        print(f"进度: {progress:.1%} - {message}")
    
    # 构建索引
    success = await tree_service.build_folder_index_async(
        file_list, 
        progress_callback=progress_callback
    )
    
    if success:
        print("文件夹索引初始化成功")
        
        # 运行一致性检查
        check_result = await tree_service.run_consistency_check(auto_fix=True)
        print(f"一致性检查: {check_result['summary']}")
    else:
        print("文件夹索引初始化失败")
    
    return success
```

### 场景2：文件变更时更新索引

```python
async def handle_file_changes(db_manager, changed_files):
    """处理文件变更"""
    
    tree_service = FileTreeIndexService(db_manager)
    
    # 更新索引
    await tree_service.update_index_async(changed_files)
    
    # 获取缓存统计
    stats = tree_service.get_cache_stats()
    print(f"缓存命中率: {stats['hit_rate']:.1%}")
```

### 场景3：懒加载文件夹内容

```python
async def load_folder_content(db_manager, folder_id):
    """懒加载文件夹内容"""
    
    tree_service = FileTreeIndexService(db_manager)
    
    # 获取文件夹子项
    children = await tree_service.get_folder_children_async(folder_id)
    
    print(f"文件夹包含:")
    print(f"- 子文件夹: {children['folder_count']} 个")
    print(f"- 文件: {children['file_count']} 个")
    
    # 检查是否需要虚拟节点
    if children['total_count'] > tree_service.virtual_node_threshold:
        print("内容较多，建议使用虚拟节点")
    
    return children
```

### 场景4：定期一致性检查

```python
async def scheduled_consistency_check(db_manager):
    """定期一致性检查"""
    
    tree_service = FileTreeIndexService(db_manager)
    
    # 运行检查
    check_result = await tree_service.run_consistency_check(auto_fix=True)
    
    if check_result['success']:
        report = check_result['report']
        
        # 检查是否有严重问题
        if report.has_critical_issues:
            print("⚠️ 发现严重问题，需要人工干预")
            
            # 输出严重问题详情
            for issue in report.issues_found:
                if issue.severity == "critical":
                    print(f"- {issue.description} ({issue.folder_path})")
        else:
            print("✓ 数据一致性检查通过")
        
        # 输出统计信息
        stats = report.issues_by_severity
        print(f"问题统计: 严重{stats['critical']}, 高级{stats['high']}, "
              f"中级{stats['medium']}, 低级{stats['low']}")
    else:
        print("一致性检查失败")
```

## 性能优化建议

### 1. 缓存配置

```python
# 根据内存情况调整缓存大小
hash_manager = FolderHashManager(
    cache_size=10000,  # 大内存环境可以增加
    enable_cache=True
)

tree_service = FileTreeIndexService(db_manager)
# 虚拟节点阈值，超过此数量的子项将使用虚拟节点
tree_service.virtual_node_threshold = 200
```

### 2. 批量操作

```python
# 批量创建文件夹
folder_list = [folder_dto1, folder_dto2, folder_dto3]
success_count = await folder_repo.batch_create_folders(folder_list)

# 批量计算哈希
folder_paths = ["/path1", "/path2", "/path3"]
hash_results = await hash_manager.batch_calculate_hashes(
    folder_paths, 
    max_concurrent=5  # 控制并发数
)
```

### 3. 增量更新

```python
# 只更新变更的文件，而不是重建整个索引
await tree_service.update_index_async(changed_files)

# 强制重新计算特定文件夹的哈希
new_hash = await hash_manager.calculate_folder_hash(
    folder_path, 
    force_recalculate=True
)
```

## 错误处理

### 1. 常见错误和解决方案

```python
try:
    folder_dto = FolderDTO.from_path("/nonexistent/path")
except ValueError as e:
    print(f"路径错误: {e}")

try:
    hash_value = await hash_manager.calculate_folder_hash("/path")
    if hash_value is None:
        print("哈希计算失败，可能是权限问题")
except Exception as e:
    print(f"哈希计算异常: {e}")
```

### 2. 数据一致性问题

```python
# 检查并修复一致性问题
report = await checker.check_all_folders(auto_fix=True)

for issue in report.issues_found:
    if not issue.auto_fixable:
        print(f"需要手动处理: {issue.description}")
        # 根据issue.issue_type进行相应处理
```

## 迁移指南

### 从旧架构迁移

1. **备份现有数据**
2. **运行初始化脚本**
3. **验证数据完整性**
4. **更新应用程序代码**

```python
# 迁移示例
async def migrate_to_folder_architecture(db_manager):
    # 1. 获取所有现有文件
    existing_files = await get_all_files_from_old_system()
    
    # 2. 构建新的文件夹索引
    tree_service = FileTreeIndexService(db_manager)
    success = await tree_service.build_folder_index_async(existing_files)
    
    # 3. 验证迁移结果
    if success:
        check_result = await tree_service.run_consistency_check()
        if check_result['success']:
            print("迁移成功")
        else:
            print("迁移后发现问题，需要检查")
    
    return success
```

## 监控和维护

### 1. 性能监控

```python
# 定期检查缓存性能
stats = hash_manager.get_cache_statistics()
if stats['hit_rate_percent'] < 80:
    print("缓存命中率较低，考虑增加缓存大小")

# 检查索引服务性能
cache_stats = tree_service.get_cache_stats()
print(f"文件树服务缓存状态: {cache_stats}")
```

### 2. 定期维护

```python
# 每日一致性检查
async def daily_maintenance(db_manager):
    tree_service = FileTreeIndexService(db_manager)
    
    # 运行一致性检查
    check_result = await tree_service.run_consistency_check(auto_fix=True)
    
    # 清理缓存
    tree_service.clear_cache()
    
    # 记录统计信息
    stats = tree_service.get_cache_stats()
    log_performance_metrics(stats)
```

这个新的文件夹为核心的架构提供了更好的性能、更清晰的数据组织和更强的一致性保证。通过合理使用这些组件，可以构建高效、可靠的文件管理系统。
