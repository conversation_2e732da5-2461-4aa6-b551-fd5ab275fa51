#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
重复文件相关DTO定义

包含重复文件检测、处理等相关的数据传输对象
"""

from dataclasses import dataclass
from typing import List, Optional, Dict, Any
from enum import Enum
import uuid
from .base_dto import BaseDTO
from .scan_dto import FileInfo


# 常量定义
DEFAULT_MIN_FILE_SIZE = 1024  # 1KB，默认最小文件大小


class DuplicateCheckType(Enum):
    """重复检查类型枚举"""
    BY_SIZE = "by_size"
    BY_HASH = "by_hash"
    BY_NAME = "by_name"
    BY_SIZE_AND_HASH = "by_size_and_hash"
    BY_CONTENT = "by_content"


class DuplicateAction(Enum):
    """重复文件处理动作枚举"""
    DELETE = "delete"
    MOVE_TO_FOLDER = "move_to_folder"
    RENAME = "rename"
    CREATE_HARDLINK = "create_hardlink"
    CREATE_SYMLINK = "create_symlink"
    MARK_ONLY = "mark_only"


@dataclass(frozen=True)
class DuplicateCheckRequest(BaseDTO):
    """
    重复文件检查请求DTO
    """
    task_id: str
    directories: List[str]
    check_type: DuplicateCheckType = DuplicateCheckType.BY_SIZE_AND_HASH
    min_file_size: int = 0
    max_file_size: Optional[int] = None
    include_hidden: bool = False
    file_extensions: Optional[List[str]] = None
    exclude_patterns: Optional[List[str]] = None
    recursive: bool = True
    
    def validate(self) -> List[str]:
        """验证重复检查请求"""
        errors = []
        
        if not self.task_id:
            errors.append("任务ID不能为空")
        
        if not self.directories:
            errors.append("检查目录列表不能为空")
        
        if self.min_file_size < 0:
            errors.append("最小文件大小不能为负数")
        
        if self.max_file_size is not None and self.max_file_size <= 0:
            errors.append("最大文件大小必须大于0")
        
        if (self.max_file_size is not None 
            and self.min_file_size > self.max_file_size):
            errors.append("最小文件大小不能大于最大文件大小")
        
        return errors
    
    @classmethod
    def new_hash_check(cls, directories: List[str]) -> 'DuplicateCheckRequest':
        """创建基于哈希的重复检查请求"""
        return cls(
            task_id=str(uuid.uuid4()),
            directories=directories,
            check_type=DuplicateCheckType.BY_HASH,
            min_file_size=DEFAULT_MIN_FILE_SIZE
        )


@dataclass(frozen=True)
class DuplicateGroup(BaseDTO):
    """
    重复文件组DTO
    
    表示一组重复的文件
    """
    group_id: str
    files: List[FileInfo]
    total_size: int
    duplicate_size: int  # 重复占用的空间
    check_type: DuplicateCheckType
    confidence: float = 1.0  # 重复的置信度 0.0-1.0
    
    def validate(self) -> List[str]:
        """验证重复文件组"""
        errors = []
        
        if not self.group_id:
            errors.append("组ID不能为空")
        
        if len(self.files) < 2:
            errors.append("重复文件组至少需要2个文件")
        
        if self.total_size < 0:
            errors.append("总大小不能为负数")
        
        if self.duplicate_size < 0:
            errors.append("重复大小不能为负数")
        
        if self.duplicate_size > self.total_size:
            errors.append("重复大小不能超过总大小")
        
        if not (0.0 <= self.confidence <= 1.0):
            errors.append("置信度必须在0.0-1.0之间")
        
        return errors
    
    @property
    def file_count(self) -> int:
        """获取文件数量"""
        return len(self.files)
    
    @property
    def can_save_space(self) -> int:
        """计算可节省的空间"""
        if len(self.files) <= 1:
            return 0
        # 保留一个文件，删除其余的，可节省的空间是重复文件的总大小减去一个文件的大小
        single_file_size = self.total_size // len(self.files)
        return self.total_size - single_file_size


@dataclass(frozen=True)
class DuplicateCheckResult(BaseDTO):
    """
    重复文件检查结果DTO
    """
    task_id: str
    duplicate_groups: List[DuplicateGroup]
    total_files_checked: int
    total_duplicates_found: int
    total_space_wasted: int
    check_duration: float
    errors: Optional[List[str]] = None
    warnings: Optional[List[str]] = None
    
    def validate(self) -> List[str]:
        """验证重复检查结果"""
        errors = []
        
        if not self.task_id:
            errors.append("任务ID不能为空")
        
        if self.total_files_checked < 0:
            errors.append("检查文件总数不能为负数")
        
        if self.total_duplicates_found < 0:
            errors.append("发现重复文件数不能为负数")
        
        if self.total_space_wasted < 0:
            errors.append("浪费空间不能为负数")
        
        if self.check_duration < 0:
            errors.append("检查时长不能为负数")
        
        return errors
    
    @property
    def duplicate_rate(self) -> float:
        """计算重复率"""
        if self.total_files_checked == 0:
            return 0.0
        return self.total_duplicates_found / self.total_files_checked * 100.0


@dataclass(frozen=True)
class DuplicateActionRequest(BaseDTO):
    """
    重复文件处理请求DTO
    """
    task_id: str
    group_id: str
    action: DuplicateAction
    target_files: List[str]  # 要处理的文件路径
    keep_file: Optional[str] = None  # 要保留的文件路径
    target_directory: Optional[str] = None  # 目标目录（移动时使用）
    backup: bool = True
    
    def validate(self) -> List[str]:
        """验证重复文件处理请求"""
        errors = []
        
        if not self.task_id:
            errors.append("任务ID不能为空")
        
        if not self.group_id:
            errors.append("组ID不能为空")
        
        if not self.target_files:
            errors.append("目标文件列表不能为空")
        
        if self.action == DuplicateAction.MOVE_TO_FOLDER and not self.target_directory:
            errors.append("移动操作需要指定目标目录")
        
        return errors
