#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
设置面板模块

该模块提供应用程序设置管理功能：
1. 通用设置配置
2. 扫描规则设置
3. 界面主题设置
4. 性能优化设置

作者: AI助手
日期: 2023-06-01
版本: 1.0.0
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import queue
import time
from pathlib import Path
from typing import Dict, Any, List, Optional
import json

# 导入自定义模块
from .interfaces import IPanel, IConfigurable, IValidatable, IThemeable
from src.core.dependency_injection import resolve
from src.utils.logger import get_logger
from src.utils.event_system import EventSystem
from src.utils.format_utils import _normalize_path
from src.utils.debounce_config_manager import DebounceConfigManager
from src.ui.debounce_settings_panel import DebounceSettingsPanel

logger = get_logger(__name__)


class SettingsPanel(IPanel):
    """
    设置面板实现
    
    提供应用程序设置管理功能
    """
    def __init__(self, parent: tk.Widget, main_window: Any, logger: Any):
        """
        初始化设置面板
        
        参数:
            parent: 父容器
            main_window: 主窗口实例
            logger: 日志记录器
        """
        self.parent = parent
        self.main_window = main_window
        self.logger = logger
        self._event_system: Optional[EventSystem] = None
        self._config = self._get_default_config()
        self._validation_errors = []
        self._theme = {}
        
        # 在创建UI之前先加载配置
        self._load_config_from_file()
        
        self.root_frame = ttk.Frame(parent)
        # 规则分组
        rules_group = ttk.LabelFrame(self.root_frame, text="设置")
        rules_group.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        # 其余控件全部布局到rules_group下
        self.create_widgets(parent=rules_group)
        self.settings = {}
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "general": {
                "auto_load_last_dir": True,
                "show_file_icons": True,
                "show_file_size": True,
                "show_file_time": True,
                "work_directory": os.path.expanduser("~/Documents/SmartFileManager"),
                "font_size": 10  # 新增：默认字体大小
            },
            "ui": {
                "auto_load_file_tree": True,  # 启动时自动加载文件树
                "auto_load_delay_ms": 2000,   # 自动加载延迟时间（毫秒）
                "interrupt_check_interval_ms": 100,  # 中断检查间隔（毫秒）
                "show_startup_progress": True,  # 显示启动进度
                "enable_startup_interrupt": True  # 启用启动时中断功能
            },
            "performance": {
                "max_threads": 4,
                "cache_size_mb": 100,
                "memory_limit_mb": 500,
                "file_tree_batch_size": 500  # 新增：文件树每批加载节点数
            },
            "backup": {
                "backup_dir": os.path.expanduser("~/Documents/SmartFileManager/Backup"),
                "auto_backup": True,
                "backup_before_delete": True,
                "max_backup_files": 100
            },
            "logging": {
                "log_level": "INFO",
                "log_to_file": True,
                "log_dir": os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "logs"),
                "max_log_files": 10
            }
        }
    
    def _load_config_from_file(self) -> None:
        """从配置文件加载设置"""
        try:
            if hasattr(self.main_window, 'config_loader') and self.main_window.config_loader:
                file_config = self.main_window.config_loader.get_config("settings")
                if file_config:
                    self._config.update(file_config)
                    self.logger.info("从配置文件加载设置成功")
                else:
                    self.logger.info("配置文件为空，使用默认配置")
            else:
                self.logger.warning("配置加载器未初始化，使用默认配置")
        except Exception as e:
            self.logger.warning(f"从配置文件加载设置失败: {e}，使用默认配置")
    
    def _init_variables(self) -> None:
        """初始化UI变量"""
        # 通用设置变量
        self.auto_load_last_dir_var = tk.BooleanVar()
        self.show_file_icons_var = tk.BooleanVar()
        self.show_file_size_var = tk.BooleanVar()
        self.show_file_time_var = tk.BooleanVar()
        self.font_size_var = tk.StringVar()  # 新增：字体大小变量

        # UI设置变量
        self.auto_load_file_tree_var = tk.BooleanVar()  # 自动加载文件树
        self.auto_load_delay_var = tk.StringVar()       # 自动加载延迟
        self.show_startup_progress_var = tk.BooleanVar()  # 显示启动进度
        self.enable_startup_interrupt_var = tk.BooleanVar()  # 启用启动中断

        # 性能设置变量
        self.max_threads_var = tk.StringVar()
        self.cache_size_var = tk.StringVar()
        self.file_tree_batch_size_var = tk.StringVar()  # 新增：文件树批量阈值变量

        # 备份设置变量
        self.backup_dir_var = tk.StringVar()
        self.auto_backup_var = tk.BooleanVar()

        # 日志设置变量
        self.log_level_var = tk.StringVar()
        self.enable_file_log_var = tk.BooleanVar()
        self.log_dir_var = tk.StringVar()
    
    def create_widgets(self, parent: tk.Widget) -> None:
        """创建UI组件"""
        # 创建设置选项卡
        self.notebook = ttk.Notebook(parent)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建各个选项卡
        self.create_general_tab(parent=self.notebook)
        self.create_backup_tab(parent=self.notebook)
        self.create_log_tab(parent=self.notebook)
        self.create_about_tab(parent=self.notebook)
        
        # 创建底部按钮框架
        self.create_bottom_buttons(parent=parent)
        
        # 加载配置到UI
        self._load_config_to_ui()
    
    def create_general_tab(self, parent: tk.Widget):
        """
        创建常规设置选项卡
        """
        # 创建常规设置框架
        self.general_frame = ttk.Frame(parent)
        self.notebook.add(self.general_frame, text="常规设置")
        
        # 创建常规设置内容
        general_content = ttk.Frame(self.general_frame)
        general_content.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建启动设置框架
        startup_frame = ttk.LabelFrame(general_content, text="启动设置")
        startup_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 自动加载上次目录
        self.auto_load_last_dir_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(
            startup_frame,
            text="启动时自动加载上次扫描的目录",
            variable=self.auto_load_last_dir_var
        ).pack(anchor="w", padx=5, pady=2)

        # 自动加载文件树
        self.auto_load_file_tree_var = tk.BooleanVar(value=True)
        auto_load_cb = ttk.Checkbutton(
            startup_frame,
            text="启动时自动加载文件树",
            variable=self.auto_load_file_tree_var,
            command=self._on_auto_load_file_tree_changed
        )
        auto_load_cb.pack(anchor="w", padx=5, pady=2)

        # 自动加载延迟设置
        delay_frame = ttk.Frame(startup_frame)
        delay_frame.pack(fill="x", padx=20, pady=2)

        ttk.Label(delay_frame, text="自动加载延迟(秒):").pack(side="left")
        self.auto_load_delay_var = tk.StringVar(value="2")
        delay_spinbox = ttk.Spinbox(
            delay_frame,
            from_=0,
            to=10,
            width=5,
            textvariable=self.auto_load_delay_var
        )
        delay_spinbox.pack(side="left", padx=(5, 0))

        # 启用启动时中断
        self.enable_startup_interrupt_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(
            startup_frame,
            text="允许中断启动时的文件树加载",
            variable=self.enable_startup_interrupt_var
        ).pack(anchor="w", padx=5, pady=2)

        # 显示启动进度
        self.show_startup_progress_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(
            startup_frame,
            text="显示启动进度信息",
            variable=self.show_startup_progress_var
        ).pack(anchor="w", padx=5, pady=2)
        
        # 创建界面设置框架
        ui_frame = ttk.LabelFrame(general_content, text="界面设置")
        ui_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 显示文件图标
        self.show_file_icons_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(
            ui_frame, 
            text="在文件树中显示文件图标", 
            variable=self.show_file_icons_var
        ).pack(anchor="w", padx=5, pady=5)
        
        # 显示文件大小
        self.show_file_size_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(
            ui_frame, 
            text="在文件树中显示文件大小", 
            variable=self.show_file_size_var
        ).pack(anchor="w", padx=5, pady=5)
        
        # 显示文件修改时间
        self.show_file_time_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(
            ui_frame, 
            text="在文件树中显示文件修改时间", 
            variable=self.show_file_time_var
        ).pack(anchor="w", padx=5, pady=5)
        
        # 字体大小设置
        font_size_frame = ttk.Frame(ui_frame)
        font_size_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(font_size_frame, text="字体大小:").pack(side=tk.LEFT, padx=(0, 5))
        
        self.font_size_var = tk.StringVar(value="10")
        font_size_combo = ttk.Combobox(
            font_size_frame, 
            textvariable=self.font_size_var, 
            state="readonly",
            width=8
        )
        font_size_combo["values"] = ("8", "9", "10", "11", "12", "14", "16", "18", "20")
        font_size_combo.pack(side=tk.LEFT)
        
        ttk.Label(font_size_frame, text="(需要重启应用程序生效)").pack(side=tk.LEFT, padx=(5, 0))
        
        # 创建性能设置框架
        performance_frame = ttk.LabelFrame(general_content, text="性能设置")
        performance_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 线程数设置
        thread_frame = ttk.Frame(performance_frame)
        thread_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(thread_frame, text="最大线程数:").pack(side=tk.LEFT, padx=(0, 5))
        
        self.max_threads_var = tk.StringVar(value="4")
        thread_spinbox = ttk.Spinbox(
            thread_frame, 
            from_=1, 
            to=16, 
            textvariable=self.max_threads_var, 
            width=5
        )
        thread_spinbox.pack(side=tk.LEFT)
        
        ttk.Label(thread_frame, text="(需要重启应用程序生效)").pack(side=tk.LEFT, padx=(5, 0))
        
        # 文件树每批加载节点数设置
        batch_frame = ttk.Frame(performance_frame)
        batch_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Label(batch_frame, text="文件树每批加载节点数:").pack(side=tk.LEFT, padx=(0, 5))
        self.file_tree_batch_size_var = tk.StringVar(value="500")
        batch_spinbox = ttk.Spinbox(
            batch_frame,
            from_=50,
            to=5000,
            increment=50,
            textvariable=self.file_tree_batch_size_var,
            width=7
        )
        batch_spinbox.pack(side=tk.LEFT)
        ttk.Label(batch_frame, text="(建议100~2000)").pack(side=tk.LEFT, padx=(5, 0))
        
        # 缓存设置
        cache_frame = ttk.Frame(performance_frame)
        cache_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(cache_frame, text="缓存大小 (MB):").pack(side=tk.LEFT, padx=(0, 5))
        
        self.cache_size_var = tk.StringVar(value="100")
        cache_spinbox = ttk.Spinbox(
            cache_frame, 
            from_=10, 
            to=1000, 
            textvariable=self.cache_size_var, 
            width=5
        )
        cache_spinbox.pack(side=tk.LEFT)
        
        # 清除缓存按钮
        clear_cache_btn = ttk.Button(performance_frame, text="清除缓存", command=self.clear_cache)
        clear_cache_btn.pack(anchor="w", padx=5, pady=5)
    
    def create_backup_tab(self, parent: tk.Widget):
        """
        创建备份设置选项卡
        """
        # 创建备份设置框架
        self.backup_frame = ttk.Frame(parent)
        self.notebook.add(self.backup_frame, text="备份设置")
        
        # 创建备份设置内容
        backup_content = ttk.Frame(self.backup_frame)
        backup_content.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建备份目录框架
        backup_dir_frame = ttk.Frame(backup_content)
        backup_dir_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(backup_dir_frame, text="备份目录:").pack(side=tk.LEFT, padx=(0, 5))
        
        self.backup_dir_var = tk.StringVar()
        backup_dir_entry = ttk.Entry(backup_dir_frame, textvariable=self.backup_dir_var)
        backup_dir_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        
        browse_backup_btn = ttk.Button(backup_dir_frame, text="浏览...", command=self.browse_backup_dir)
        browse_backup_btn.pack(side=tk.LEFT)
        
        # 创建备份选项框架
        backup_options_frame = ttk.LabelFrame(backup_content, text="备份选项")
        backup_options_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 自动备份
        self.auto_backup_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(
            backup_options_frame, 
            text="在执行删除或移动操作前自动备份文件", 
            variable=self.auto_backup_var
        ).pack(anchor="w", padx=5, pady=5)
        
        # 保留备份天数
        backup_days_frame = ttk.Frame(backup_options_frame)
        backup_days_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(backup_days_frame, text="保留备份天数:").pack(side=tk.LEFT, padx=(0, 5))
        
        self.backup_days_var = tk.StringVar(value="30")
        backup_days_spinbox = ttk.Spinbox(
            backup_days_frame, 
            from_=1, 
            to=365, 
            textvariable=self.backup_days_var, 
            width=5
        )
        backup_days_spinbox.pack(side=tk.LEFT)
        
        # 最大备份大小
        backup_size_frame = ttk.Frame(backup_options_frame)
        backup_size_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(backup_size_frame, text="最大备份大小 (GB):").pack(side=tk.LEFT, padx=(0, 5))
        
        self.backup_size_var = tk.StringVar(value="10")
        backup_size_spinbox = ttk.Spinbox(
            backup_size_frame, 
            from_=1, 
            to=1000, 
            textvariable=self.backup_size_var, 
            width=5
        )
        backup_size_spinbox.pack(side=tk.LEFT)
        
        # 清理备份按钮
        clean_backup_btn = ttk.Button(backup_options_frame, text="清理过期备份", command=self.clean_backup)
        clean_backup_btn.pack(anchor="w", padx=5, pady=5)
    
    def create_log_tab(self, parent: tk.Widget):
        """
        创建日志设置选项卡
        """
        # 创建日志设置框架
        self.log_frame = ttk.Frame(parent)
        self.notebook.add(self.log_frame, text="日志设置")
        
        # 创建日志设置内容
        log_content = ttk.Frame(self.log_frame)
        log_content.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建日志级别框架
        log_level_frame = ttk.Frame(log_content)
        log_level_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(log_level_frame, text="日志级别:").pack(side=tk.LEFT, padx=(0, 5))
        
        self.log_level_var = tk.StringVar(value="INFO")
        log_level_combo = ttk.Combobox(log_level_frame, textvariable=self.log_level_var, state="readonly")
        log_level_combo["values"] = ("DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL")
        log_level_combo.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # 创建日志文件框架
        log_file_frame = ttk.LabelFrame(log_content, text="日志文件")
        log_file_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 启用文件日志
        self.enable_file_log_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(
            log_file_frame, 
            text="启用文件日志", 
            variable=self.enable_file_log_var,
            command=self.toggle_file_log
        ).pack(anchor="w", padx=5, pady=5)
        
        # 日志目录
        log_dir_frame = ttk.Frame(log_file_frame)
        log_dir_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(log_dir_frame, text="日志目录:").pack(side=tk.LEFT, padx=(0, 5))
        
        self.log_dir_var = tk.StringVar()
        self.log_dir_entry = ttk.Entry(log_dir_frame, textvariable=self.log_dir_var)
        self.log_dir_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        
        self.browse_log_btn = ttk.Button(log_dir_frame, text="浏览...", command=self.browse_log_dir)
        self.browse_log_btn.pack(side=tk.LEFT)
        
        # 日志文件大小
        log_size_frame = ttk.Frame(log_file_frame)
        log_size_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(log_size_frame, text="单个日志文件最大大小 (MB):").pack(side=tk.LEFT, padx=(0, 5))
        
        self.log_size_var = tk.StringVar(value="10")
        self.log_size_spinbox = ttk.Spinbox(
            log_size_frame, 
            from_=1, 
            to=100, 
            textvariable=self.log_size_var, 
            width=5
        )
        self.log_size_spinbox.pack(side=tk.LEFT)
        
        # 日志文件数量
        log_count_frame = ttk.Frame(log_file_frame)
        log_count_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(log_count_frame, text="最大日志文件数量:").pack(side=tk.LEFT, padx=(0, 5))
        
        self.log_count_var = tk.StringVar(value="5")
        self.log_count_spinbox = ttk.Spinbox(
            log_count_frame, 
            from_=1, 
            to=20, 
            textvariable=self.log_count_var, 
            width=5
        )
        self.log_count_spinbox.pack(side=tk.LEFT)
        
        # 查看日志按钮
        view_log_btn = ttk.Button(log_file_frame, text="查看日志文件", command=self.view_log_file)
        view_log_btn.pack(anchor="w", padx=5, pady=5)
        
        # 清除日志按钮
        clear_log_btn = ttk.Button(log_file_frame, text="清除日志文件", command=self.clear_log_file)
        clear_log_btn.pack(anchor="w", padx=5, pady=5)
        
        # 初始化文件日志控件状态
        self.toggle_file_log()
    
    def create_about_tab(self, parent: tk.Widget):
        """
        创建关于选项卡
        """
        # 创建关于框架
        self.about_frame = ttk.Frame(parent)
        self.notebook.add(self.about_frame, text="关于")
        
        # 创建关于内容
        about_content = ttk.Frame(self.about_frame)
        about_content.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 应用程序名称
        app_name_label = ttk.Label(
            about_content, 
            text="智能文件管理器", 
            font=("Arial", 16, "bold")
        )
        app_name_label.pack(pady=(20, 5))
        
        # 版本信息
        version_label = ttk.Label(
            about_content, 
            text="版本 1.0.0", 
            font=("Arial", 10)
        )
        version_label.pack(pady=(0, 20))
        
        # 描述信息
        description_text = (
            "智能文件管理器是一款功能强大的文件管理工具，\n"
            "可以帮助您整理、重命名、查找重复文件和管理垃圾文件。\n\n"
            "主要功能：\n"
            "- 文件扫描和分析\n"
            "- 批量重命名文件\n"
            "- 查找和处理重复文件\n"
            "- 识别和清理垃圾文件\n"
            "- 白名单文件管理\n"
        )
        description_label = ttk.Label(
            about_content, 
            text=description_text, 
            justify=tk.CENTER
        )
        description_label.pack(pady=10)
        
        # 版权信息
        copyright_label = ttk.Label(
            about_content, 
            text="© 2023 智能文件管理器团队。保留所有权利。", 
            font=("Arial", 8)
        )
        copyright_label.pack(pady=(20, 5))
        
        # 创建按钮框架
        button_frame = ttk.Frame(about_content)
        button_frame.pack(pady=10)
        
        # 检查更新按钮
        check_update_btn = ttk.Button(button_frame, text="检查更新", command=self.check_update)
        check_update_btn.pack(side=tk.LEFT, padx=5)
        
        # 访问网站按钮
        visit_website_btn = ttk.Button(button_frame, text="访问网站", command=self.visit_website)
        visit_website_btn.pack(side=tk.LEFT, padx=5)
    
    def create_bottom_buttons(self, parent: tk.Widget):
        """
        创建底部按钮
        """
        # 创建按钮框架
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 应用按钮
        apply_btn = ttk.Button(button_frame, text="应用", command=self.apply_settings)
        apply_btn.pack(side=tk.RIGHT, padx=5)
        
        # 取消按钮
        cancel_btn = ttk.Button(button_frame, text="取消", command=self.cancel_settings)
        cancel_btn.pack(side=tk.RIGHT, padx=5)
        
        # 恢复默认按钮
        reset_btn = ttk.Button(button_frame, text="恢复默认", command=self.reset_settings)
        reset_btn.pack(side=tk.LEFT, padx=5)
    
    def _load_config_to_ui(self) -> None:
        """将配置加载到UI"""
        try:
            general = self._config.get('general', {})
            self.auto_load_last_dir_var.set(general.get('auto_load_last_dir', True))
            self.show_file_icons_var.set(general.get('show_file_icons', True))
            self.show_file_size_var.set(general.get('show_file_size', True))
            self.show_file_time_var.set(general.get('show_file_time', True))
            self.font_size_var.set(str(general.get('font_size', 10)))

            # UI设置
            ui = self._config.get('ui', {})
            self.auto_load_file_tree_var.set(ui.get('auto_load_file_tree', True))
            self.auto_load_delay_var.set(str(ui.get('auto_load_delay_ms', 2000) // 1000))  # 转换为秒
            self.show_startup_progress_var.set(ui.get('show_startup_progress', True))
            self.enable_startup_interrupt_var.set(ui.get('enable_startup_interrupt', True))

            performance = self._config.get('performance', {})
            self.max_threads_var.set(str(performance.get('max_threads', 4)))
            self.cache_size_var.set(str(performance.get('cache_size_mb', 100)))
            self.file_tree_batch_size_var.set(str(performance.get('file_tree_batch_size', 500)))  # 新增
            
            backup = self._config.get('backup', {})
            self.backup_dir_var.set(backup.get('backup_dir', ''))
            self.auto_backup_var.set(backup.get('auto_backup', True))
            
            logging_config = self._config.get('logging', {})
            self.log_level_var.set(logging_config.get('log_level', 'INFO'))
            self.enable_file_log_var.set(logging_config.get('log_to_file', True))
            default_log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "logs")
            self.log_dir_var.set(logging_config.get('log_dir', default_log_dir))
            
            self.logger.info("配置已加载到UI")
        except Exception as e:
            self.logger.error(f"加载配置到UI失败: {e}")
    
    def _collect_config_from_ui(self) -> Dict[str, Any]:
        """从UI收集配置"""
        return {
            "general": {
                "auto_load_last_dir": self.auto_load_last_dir_var.get(),
                "show_file_icons": self.show_file_icons_var.get(),
                "show_file_size": self.show_file_size_var.get(),
                "show_file_time": self.show_file_time_var.get(),
                "font_size": int(self.font_size_var.get())
            },
            "ui": {
                "auto_load_file_tree": self.auto_load_file_tree_var.get(),
                "auto_load_delay_ms": int(float(self.auto_load_delay_var.get()) * 1000),  # 转换为毫秒
                "interrupt_check_interval_ms": 100,  # 固定值
                "show_startup_progress": self.show_startup_progress_var.get(),
                "enable_startup_interrupt": self.enable_startup_interrupt_var.get()
            },
            "performance": {
                "max_threads": int(self.max_threads_var.get()),
                "cache_size_mb": int(self.cache_size_var.get()),
                "file_tree_batch_size": int(self.file_tree_batch_size_var.get())  # 新增
            },
            "backup": {
                "backup_dir": self.backup_dir_var.get(),
                "auto_backup": self.auto_backup_var.get()
            },
            "logging": {
                "log_level": self.log_level_var.get(),
                "log_to_file": self.enable_file_log_var.get(),
                "log_dir": self.log_dir_var.get()
            }
        }
    
    def load_config(self, config: Dict[str, Any]) -> None:
        """加载配置"""
        self._config.update(config)
        if self.root_frame is not None:
            self._load_config_to_ui()
    
    def get_config(self) -> Dict[str, Any]:
        """获取配置"""
        if self.root_frame is not None:
            self._config.update(self._collect_config_from_ui())
        return self._config.copy()
    
    def validate(self) -> List[str]:
        """验证配置"""
        errors = []
        
        try:
            # 验证线程数
            max_threads = int(self.max_threads_var.get())
            if max_threads < 1 or max_threads > 16:
                errors.append("线程数必须在 1-16 之间")
        except ValueError:
            errors.append("线程数必须是有效的整数")
        
        try:
            # 验证缓存大小
            cache_size = int(self.cache_size_var.get())
            if cache_size < 10 or cache_size > 1000:
                errors.append("缓存大小必须在 10-1000 MB 之间")
        except ValueError:
            errors.append("缓存大小必须是有效的整数")
        
        # 验证备份目录
        if self.auto_backup_var.get():
            backup_dir = self.backup_dir_var.get().strip()
            if not backup_dir:
                errors.append("启用自动备份时必须指定备份目录")
        
        # 验证日志目录
        if self.enable_file_log_var.get():
            log_dir = self.log_dir_var.get().strip()
            if not log_dir:
                errors.append("启用文件日志时必须指定日志目录")
        
        self._validation_errors = errors
        return errors
    
    def apply_theme(self, theme: Dict[str, Any]) -> None:
        """应用主题"""
        self._theme = theme
        # 这里可以根据主题更新UI样式
        # 例如更新颜色、字体等
    
    def get_frame(self) -> Optional[tk.Widget]:
        """获取面板框架"""
        if self.root_frame is None:
            self.create_widgets(parent=self.parent)
        return self.root_frame
    
    def show(self) -> None:
        """显示面板"""
        frame = self.get_frame()
        if frame is not None:
            frame.pack(fill=tk.BOTH, expand=True)
    
    def hide(self) -> None:
        """隐藏面板"""
        if self.root_frame is not None:
            self.root_frame.pack_forget()
    
    def destroy(self):
        pass

    # 接口实现方法
    def bind_events(self) -> None:
        """绑定事件"""
        pass
    
    def update_ui(self, data: Dict[str, Any]) -> None:
        """更新UI显示"""
        if "config" in data:
            self.load_config(data["config"])
    
    def load_data(self, data: Any) -> None:
        """加载数据"""
        if isinstance(data, dict):
            self.load_config(data)
    
    def clear_data(self) -> None:
        """清空数据"""
        self._config = self._get_default_config()
        if self.root_frame is not None:
            self._load_config_to_ui()
    
    def get_selected_items(self) -> List[Any]:
        """获取选中项"""
        return []
    
    def refresh(self) -> None:
        """刷新面板"""
        if self.root_frame is not None:
            self._load_config_to_ui()
    
    def save_config(self) -> Dict[str, Any]:
        """保存配置"""
        return self.get_config()
    
    def reset_config(self) -> None:
        """重置配置"""
        self._config = self._get_default_config()
        if self.root_frame is not None:
            self._load_config_to_ui()
    
    def get_validation_errors(self) -> List[str]:
        """获取验证错误"""
        return self.validate()
    
    def clear_validation_errors(self) -> None:
        """清除验证错误"""
        self._validation_errors.clear()
    
    def get_current_theme(self) -> Dict[str, Any]:
        """获取当前主题"""
        return self._theme.copy()
    
    def set_event_system(self, event_system: EventSystem) -> None:
        """设置事件系统"""
        self._event_system = event_system
    
    def save_settings(self):
        """
        保存设置
        """
        try:
            errors = self.validate()
            if errors:
                error_msg = "\n".join(errors)
                messagebox.showerror("验证错误", f"设置验证失败：\n{error_msg}")
                return
            
            # 收集并保存配置
            self._config = self._collect_config_from_ui()
            
            # 保存到配置文件
            try:
                if hasattr(self.main_window, 'config_loader') and self.main_window.config_loader:
                    success = self.main_window.config_loader.save_config("settings", self._config)
                    if success:
                        self.logger.info("设置已保存到配置文件")
                    else:
                        self.logger.error("保存设置到配置文件失败")
                        messagebox.showerror("错误", "保存设置到配置文件失败")
                        return
                else:
                    self.logger.error("配置加载器未初始化，无法保存设置")
                    messagebox.showerror("错误", "配置加载器未初始化，无法保存设置")
                    return
            except Exception as e:
                self.logger.error(f"保存设置到配置文件失败: {e}")
                messagebox.showerror("错误", f"保存设置到配置文件失败: {e}")
                return
            
            # 发送配置变更事件
            if self._event_system:
                self._event_system.publish("config_changed", {
                    "config": self._config.copy()
                })
            
            self.logger.info("设置保存成功")
            messagebox.showinfo("成功", "设置保存成功")
            
        except Exception as e:
            self.logger.error(f"保存设置失败: {e}")
            messagebox.showerror("错误", f"保存设置失败: {e}")
    
    def apply_settings_to_app(self):
        """
        应用设置到应用程序
        """
        # 应用日志设置
        log_level = self.log_level_var.get()
        self.main_window.logger.setLevel(log_level)
        
        # 应用文件日志设置
        if self.enable_file_log_var.get():
            log_dir = self.log_dir_var.get()
            log_size = int(self.log_size_var.get()) * 1024 * 1024  # 转换为字节
            log_count = int(self.log_count_var.get())
            
            # 检查日志目录
            if not log_dir:
                log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
                self.log_dir_var.set(log_dir)
            
            # 确保日志目录存在
            os.makedirs(log_dir, exist_ok=True)
            
            # 更新日志处理器
            self.main_window.logger.update_file_handler(log_dir, log_size, log_count)
        else:
            # 移除文件日志处理器
            self.main_window.logger.remove_file_handler()
        
        # 应用备份设置
        backup_dir = self.backup_dir_var.get()
        if backup_dir:
            # 确保备份目录存在
            os.makedirs(backup_dir, exist_ok=True)
            
            # 更新备份目录
            self.main_window.file_operations.set_backup_dir(backup_dir)
        
        # 应用界面设置
        self.main_window.file_tree_panel.update_display_options(
            show_icons=self.show_file_icons_var.get(),
            show_size=self.show_file_size_var.get(),
            show_time=self.show_file_time_var.get()
        )
    
    def apply_settings(self):
        """
        应用设置
        """
        # 验证设置
        if not self.validate_settings():
            return
        
        # 应用字体大小设置
        try:
            font_size = int(self.font_size_var.get())
            if hasattr(self.main_window, 'ui_factory') and self.main_window.ui_factory:
                self.main_window.ui_factory.apply_font_size(font_size)
                self.logger.info(f"字体大小已更新为: {font_size}")
                
                # 通知文件树面板更新字体大小
                if hasattr(self.main_window, 'file_tree_panel') and self.main_window.file_tree_panel:
                    if hasattr(self.main_window.file_tree_panel, 'update_font_size'):
                        self.main_window.file_tree_panel.update_font_size(font_size)
        except Exception as e:
            self.logger.error(f"应用字体大小设置失败: {e}")
        
        # 保存设置
        self.save_settings()
        # 应用文件树批量阈值设置
        try:
            batch_size = int(self.file_tree_batch_size_var.get())
            if hasattr(self.main_window, 'file_tree_panel') and self.main_window.file_tree_panel:
                if hasattr(self.main_window.file_tree_panel, 'populate_tree'):
                    # 重新加载文件树，传递新的batch_size
                    files = getattr(self.main_window.file_tree_panel, 'files', None)
                    if files:
                        self.main_window.file_tree_panel.populate_tree(files, batch_size=batch_size)
        except Exception as e:
            self.logger.error(f"应用文件树批量阈值设置失败: {e}")
    
    def cancel_settings(self):
        """
        取消设置
        """
        # 重新加载配置到UI
        self._load_config_to_ui()
        
        # 显示提示
        if hasattr(self.main_window, 'log_message'):
            self.main_window.log_message("已取消设置更改", "info")
    
    def reset_settings(self):
        """
        重置设置为默认值
        """
        # 确认重置
        if not messagebox.askyesno("确认重置", "确定要将所有设置重置为默认值吗？"):
            return
        
        # 设置默认值
        # 常规设置
        self.auto_load_last_dir_var.set(True)
        self.show_file_icons_var.set(True)
        self.show_file_size_var.set(True)
        self.show_file_time_var.set(True)
        self.font_size_var.set("10")
        self.max_threads_var.set("4")
        self.cache_size_var.set("100")
        self.file_tree_batch_size_var.set("500") # 新增
        
        # 备份设置
        self.backup_dir_var.set("")
        self.auto_backup_var.set(True)
        self.backup_days_var.set("30")
        self.backup_size_var.set("10")
        
        # 日志设置
        self.log_level_var.set("INFO")
        self.enable_file_log_var.set(True)
        self.log_dir_var.set("")
        self.log_size_var.set("10")
        self.log_count_var.set("5")
        
        # 更新文件日志控件状态
        self.toggle_file_log()
        
        # 显示提示
        self.main_window.log_message("已重置设置为默认值", "info")
    
    def validate_settings(self):
        """
        验证设置
        
        返回:
            bool: 设置是否有效
        """
        # 验证线程数
        try:
            max_threads = int(self.max_threads_var.get())
            if max_threads < 1 or max_threads > 16:
                raise ValueError("线程数必须在 1-16 之间")
        except ValueError as e:
            messagebox.showerror("错误", f"无效的线程数: {str(e)}")
            return False
        
        # 验证缓存大小
        try:
            cache_size = int(self.cache_size_var.get())
            if cache_size < 10 or cache_size > 1000:
                raise ValueError("缓存大小必须在 10-1000 MB 之间")
        except ValueError as e:
            messagebox.showerror("错误", f"无效的缓存大小: {str(e)}")
            return False
        
        # 验证备份设置
        if self.auto_backup_var.get():
            backup_dir = self.backup_dir_var.get().strip()
            if not backup_dir:
                messagebox.showwarning("警告", "启用自动备份时必须指定备份目录")
                return False
            
            try:
                backup_days = int(self.backup_days_var.get())
                if backup_days < 1 or backup_days > 365:
                    raise ValueError("备份保留天数必须在 1-365 之间")
            except ValueError as e:
                messagebox.showerror("错误", f"无效的备份保留天数: {str(e)}")
                return False
            
            try:
                backup_size = int(self.backup_size_var.get())
                if backup_size < 1 or backup_size > 1000:
                    raise ValueError("最大备份大小必须在 1-1000 GB 之间")
            except ValueError as e:
                messagebox.showerror("错误", f"无效的最大备份大小: {str(e)}")
                return False
        
        # 验证日志设置
        if self.enable_file_log_var.get():
            log_dir = self.log_dir_var.get().strip()
            if not log_dir:
                # 使用默认日志目录
                log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
                self.log_dir_var.set(log_dir)
            
            try:
                log_size = int(self.log_size_var.get())
                if log_size < 1 or log_size > 100:
                    raise ValueError("日志文件大小必须在 1-100 MB 之间")
            except ValueError as e:
                messagebox.showerror("错误", f"无效的日志文件大小: {str(e)}")
                return False
            
            try:
                log_count = int(self.log_count_var.get())
                if log_count < 1 or log_count > 20:
                    raise ValueError("日志文件数量必须在 1-20 之间")
            except ValueError as e:
                messagebox.showerror("错误", f"无效的日志文件数量: {str(e)}")
                return False
        
        return True
    
    def _on_auto_load_file_tree_changed(self):
        """自动加载文件树设置变化回调"""
        try:
            enabled = self.auto_load_file_tree_var.get()
            self.logger.info(f"自动加载文件树设置变更为: {enabled}")

            # 可以在这里添加实时应用设置的逻辑
            # 例如通知主窗口设置已变更
            if hasattr(self.main_window, 'on_auto_load_setting_changed'):
                self.main_window.on_auto_load_setting_changed(enabled)

        except Exception as e:
            self.logger.error(f"处理自动加载文件树设置变化失败: {e}")

    def toggle_file_log(self, event=None):
        """
        切换文件日志设置控件状态

        参数:
            event: 事件对象
        """
        # 获取文件日志启用状态
        enable_file_log = self.enable_file_log_var.get()

        # 设置控件状态
        state = "normal" if enable_file_log else "disabled"
        self.log_dir_entry.configure(state=state)
        self.browse_log_btn.configure(state=state)
        self.log_size_spinbox.configure(state=state)
        self.log_count_spinbox.configure(state=state)
    
    def browse_backup_dir(self):
        """
        浏览备份目录
        """
        # 选择目录
        backup_dir = filedialog.askdirectory(title="选择备份目录")
        if backup_dir:
            self.backup_dir_var.set(backup_dir)
    
    def browse_log_dir(self):
        """
        浏览日志目录
        """
        # 选择目录
        log_dir = filedialog.askdirectory(title="选择日志目录")
        if log_dir:
            self.log_dir_var.set(log_dir)
    
    # 以缓存清理为例
    def clear_cache(self):
        """
        清除缓存
        """
        # 确认清除
        if not messagebox.askyesno("确认清除", "确定要清除所有缓存吗？"):
            return
        
        # 清除缓存
        try:
            # 获取缓存目录
            cache_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "cache")
            cache_dir = _normalize_path(cache_dir)
            
            # 检查缓存目录是否存在
            if os.path.isdir(cache_dir):
                # 删除缓存目录中的所有文件
                for filename in os.listdir(cache_dir):
                    file_path = os.path.join(cache_dir, filename)
                    file_path = _normalize_path(file_path)
                    if os.path.isfile(file_path):
                        os.remove(file_path)
                
                # 显示提示
                self.main_window.log_message("缓存已清除", "success")
            else:
                # 显示提示
                self.main_window.log_message("缓存目录不存在", "info")
        except Exception as e:
            # 显示错误
            self.main_window.log_message(f"清除缓存时出错: {str(e)}", "error")
    
    def clean_backup(self):
        """
        清理过期备份
        """
        # 获取备份目录
        backup_dir = self.backup_dir_var.get().strip()
        if not backup_dir:
            messagebox.showwarning("警告", "请先设置备份目录")
            return
        
        # 检查备份目录是否存在
        if not os.path.isdir(backup_dir):
            messagebox.showwarning("警告", f"备份目录 {backup_dir} 不存在")
            return
        
        # 获取备份保留天数
        try:
            backup_days = int(self.backup_days_var.get())
        except ValueError:
            messagebox.showerror("错误", "无效的备份保留天数")
            return
        
        # 确认清理
        if not messagebox.askyesno("确认清理", f"确定要清理 {backup_days} 天前的备份吗？"):
            return
        
        # 添加任务到队列
        self.main_window.task_queue.put({
            "type": "clean_backup",
            "data": {"backup_dir": backup_dir, "backup_days": backup_days}
        })
        
        # 更新状态
        self.main_window.status_text.set("正在清理过期备份...")
        self.main_window.progress_var.set(0)
        self.main_window.task_label.set("清理备份")
        self.main_window.subtask_label.set("")
    
    def view_log_file(self):
        """
        查看日志文件
        """
        # 获取日志目录
        log_dir = self.log_dir_var.get().strip()
        if not log_dir:
            log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
        
        # 检查日志目录是否存在
        if not os.path.isdir(log_dir):
            messagebox.showwarning("警告", f"日志目录 {log_dir} 不存在")
            return
        
        # 获取日志文件列表
        log_files = [f for f in os.listdir(log_dir) if f.endswith(".log")]
        if not log_files:
            messagebox.showinfo("信息", "没有找到日志文件")
            return
        
        # 按修改时间排序
        log_files.sort(key=lambda f: os.path.getmtime(os.path.join(log_dir, f)), reverse=True)
        
        # 打开最新的日志文件
        latest_log = os.path.join(log_dir, log_files[0])
        
        # 使用系统默认程序打开日志文件
        try:
            if sys.platform == "win32":
                os.startfile(latest_log)
            elif sys.platform == "darwin":
                os.system(f"open {latest_log}")
            else:
                os.system(f"xdg-open {latest_log}")
        except Exception as e:
            messagebox.showerror("错误", f"无法打开日志文件: {str(e)}")
    
    def clear_log_file(self):
        """
        清除日志文件
        """
        # 获取日志目录
        log_dir = self.log_dir_var.get().strip()
        if not log_dir:
            log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
        
        # 检查日志目录是否存在
        if not os.path.isdir(log_dir):
            messagebox.showwarning("警告", f"日志目录 {log_dir} 不存在")
            return
        
        # 获取日志文件列表
        log_files = [f for f in os.listdir(log_dir) if f.endswith(".log")]
        if not log_files:
            messagebox.showinfo("信息", "没有找到日志文件")
            return
        
        # 确认清除
        if not messagebox.askyesno("确认清除", f"确定要清除所有 {len(log_files)} 个日志文件吗？"):
            return
        
        # 清除日志文件
        try:
            for filename in log_files:
                file_path = os.path.join(log_dir, filename)
                os.remove(file_path)
            
            # 显示提示
            self.main_window.log_message(f"已清除 {len(log_files)} 个日志文件", "success")
        except Exception as e:
            # 显示错误
            self.main_window.log_message(f"清除日志文件时出错: {str(e)}", "error")
    
    def check_update(self):
        """
        检查更新
        """
        # 显示提示
        messagebox.showinfo("检查更新", "当前已是最新版本 (1.0.0)")
    
    def visit_website(self):
        """
        访问网站
        """
        # 打开网站
        website_url = "https://github.com/yourusername/smartfilemanager"
        
        # 使用系统默认浏览器打开网站
        try:
            import webbrowser
            webbrowser.open(website_url)
        except Exception as e:
            messagebox.showerror("错误", f"无法打开网站: {str(e)}")

    def update_duplicate_groups(self, data):
        """设置面板不处理重复文件组，留空实现以满足接口要求"""
        pass

    def set_find_button_state(self, enabled: bool, text: Optional[str] = None):
        """设置查找按钮状态（可选）"""
        # SettingsPanel不需要查找按钮，所以这个方法可以为空实现
        pass

    def update_window_display(self, feature_name: str, window_size: float):
        """更新功能的当前窗口显示"""
        self.debounce_panel.update_window_display(feature_name, window_size)

    def show_debounce_settings(self):
        """切换到防抖设置页"""
        self.notebook.select(1)  # 切换到防抖设置页


# 测试代码
if __name__ == "__main__":
    root = tk.Tk()
    root.geometry("800x600")
    
    # 创建主窗口模拟对象
    class MainWindowMock:
        def __init__(self):
            self.root = root
            self.status_text = tk.StringVar()
            self.progress_var = tk.DoubleVar()
            self.task_label = tk.StringVar()
            self.subtask_label = tk.StringVar()
            self.task_queue = queue.Queue()
            # mock event_system和async_manager属性
            self.event_system = object()
            self.async_manager = object()
            
            class ConfigLoaderMock:
                def get_config(self, name):
                    return {}
                
                def save_config(self, name, config):
                    pass
            
            class LoggerMock:
                def setLevel(self, level):
                    pass
                
                def update_file_handler(self, log_dir, log_size, log_count):
                    pass
                
                def remove_file_handler(self):
                    pass
            
            class FileOperationsMock:
                def set_backup_dir(self, backup_dir):
                    pass
            
            class FileTreePanelMock:
                def update_display_options(self, show_icons, show_size, show_time):
                    pass
            
            self.config_loader = ConfigLoaderMock()
            self.logger = get_logger("SettingsPanelTest")
            self.file_operations = FileOperationsMock()
            self.file_tree_panel = FileTreePanelMock()
            
            def log_message(self, message, level="info"):
                print(f"[{level}] {message}")
            
            self.log_message = log_message.__get__(self)
    
    main_window = MainWindowMock()
    
    # 创建设置面板
    parent_frame = ttk.Frame(root)
    parent_frame.pack(fill=tk.BOTH, expand=True)
    panel = SettingsPanel(parent_frame, main_window, main_window.logger)
    frame = panel.get_frame()
    if frame is not None:
        frame.pack(fill=tk.BOTH, expand=True)
    
    root.mainloop()