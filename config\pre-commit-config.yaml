# 智能文件管理器 Pre-commit 配置
# 在每次提交前自动运行代码质量检查

repos:
  # 基础代码质量检查
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      # 移除行尾空白
      - id: trailing-whitespace
        args: [--markdown-linebreak-ext=md]
      
      # 确保文件以换行符结尾
      - id: end-of-file-fixer
      
      # 检查YAML文件语法
      - id: check-yaml
        args: [--allow-multiple-documents]
      
      # 检查JSON文件语法
      - id: check-json
      
      # 检查XML文件语法
      - id: check-xml
      
      # 检查大文件
      - id: check-added-large-files
        args: [--maxkb=1000]
      
      # 检查合并冲突标记
      - id: check-merge-conflict
      
      # 检查Python AST语法
      - id: check-ast
      
      # 检查文档字符串
      - id: check-docstring-first
      
      # 检查可执行文件有shebang
      - id: check-executables-have-shebangs
      
      # 修复Python编码声明
      - id: fix-encoding-pragma
        args: [--remove]
      
      # 检查私钥
      - id: detect-private-key

  # Python代码格式化
  - repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
      - id: black
        language_version: python3
        args: [--line-length=100]
        files: \.py$

  # 导入排序
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: [--profile=black, --line-length=100]
        files: \.py$

  # 代码风格检查
  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        args: [--max-line-length=100, --extend-ignore=E203,W503]
        files: \.py$

  # 类型检查
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.3.0
    hooks:
      - id: mypy
        args: [--config-file=config/mypy.ini]
        files: ^src/.*\.py$
        additional_dependencies: [types-all]

  # 安全检查
  - repo: https://github.com/PyCQA/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        args: [-r, -f, json, -o, bandit-report.json]
        files: \.py$
        exclude: ^tests/

  # 文档检查
  - repo: https://github.com/pycqa/pydocstyle
    rev: 6.3.0
    hooks:
      - id: pydocstyle
        args: [--convention=google]
        files: ^src/.*\.py$

  # 复杂度检查
  - repo: https://github.com/xenon-python/xenon
    rev: v0.9.0
    hooks:
      - id: xenon
        args: [--max-average=A, --max-modules=B, --max-absolute=B]
        files: \.py$

  # 自定义规则检查
  - repo: local
    hooks:
      # 自定义代码规则检查器
      - id: custom-rule-checker
        name: Custom Rule Checker
        entry: python tools/code_checker.py --check-rules
        language: system
        pass_filenames: false
        always_run: true
        stages: [commit]
      
      # 检查TODO/FIXME注释
      - id: check-todos
        name: Check TODOs
        entry: bash -c 'if grep -r "TODO\|FIXME\|XXX\|HACK" src/ --include="*.py"; then echo "发现待办事项，请在提交前处理"; exit 1; fi'
        language: system
        pass_filenames: false
        always_run: true
        stages: [commit]
      
      # 检查调试代码
      - id: check-debug-code
        name: Check Debug Code
        entry: bash -c 'if grep -r "print(\|pdb\|debugger\|console\.log" src/ --include="*.py"; then echo "发现调试代码，请在提交前移除"; exit 1; fi'
        language: system
        pass_filenames: false
        always_run: true
        stages: [commit]
      
      # 检查硬编码密码
      - id: check-hardcoded-passwords
        name: Check Hardcoded Passwords
        entry: bash -c 'if grep -ri "password\s*=\s*[\"'\''][^\"'\'']*[\"'\'']" src/ --include="*.py"; then echo "发现硬编码密码，请使用配置文件"; exit 1; fi'
        language: system
        pass_filenames: false
        always_run: true
        stages: [commit]

# 全局配置
default_stages: [commit]
fail_fast: false
minimum_pre_commit_version: 3.0.0

# CI配置
ci:
  autofix_commit_msg: |
    [pre-commit.ci] auto fixes from pre-commit hooks
    
    for more information, see https://pre-commit.ci
  autofix_prs: true
  autoupdate_branch: ''
  autoupdate_commit_msg: '[pre-commit.ci] pre-commit autoupdate'
  autoupdate_schedule: weekly
  skip: []
  submodules: false
