# 重复文件检查系统性能优化方案

## 背景

当前系统在处理大量混合文件类型（特别是1-6GB的大型视频文件和众多小型HTML、TXT文件）时存在性能瓶颈。本优化方案旨在提高系统处理效率，改善用户体验，并确保系统在处理大文件时保持响应性。

## 优化目标

1. 提高哈希计算效率，特别是对大型视频文件
2. 减少数据库操作开销
3. 优化系统资源使用
4. 改善用户中断响应时间
5. 提升整体处理速度

## 分阶段优化计划

### 第一阶段：基础架构优化（1-2周）

#### 1.1 批处理机制改进

- [ ] 实现文件批量处理框架
  - 替换单文件处理为批量处理
  - 设置默认批处理大小为100-500个文件
  - 只在批次处理完成后检查用户中断

```python
def process_files_in_batches(files, batch_size=200):
    """批量处理文件，提高效率"""
    for i in range(0, len(files), batch_size):
        batch = files[i:i+batch_size]
        process_batch(batch)
        # 每批次后检查中断
        if check_interruption_flag():
            break
```

- [ ] 优化数据库查询
  - 创建文件存在状态和哈希值的复合索引
  - 实现批量数据库更新操作
  - 使用事务包装批量操作

```python
def get_files_without_hash(limit=500):
    """获取需要计算哈希的文件批次"""
    return db.files.find({"exists": True, "hash": None}).limit(limit)

def update_file_hashes_batch(file_hash_pairs):
    """批量更新文件哈希值"""
    bulk_operations = []
    for file_id, hash_value in file_hash_pairs:
        bulk_operations.append(
            UpdateOne({"_id": file_id}, {"$set": {"hash": hash_value}})
        )
    if bulk_operations:
        db.files.bulk_write(bulk_operations)
```

#### 1.2 中断处理优化

- [ ] 改进中断检测机制
  - 实现中断标志
  - 在批处理的自然边界检查中断
  - 优化中断响应时间

```python
class InterruptibleBatchProcessor:
    def __init__(self):
        self.interrupted = False
        
    def set_interrupted(self):
        self.interrupted = True
        
    def check_interrupted(self):
        return self.interrupted
        
    def process_with_interruption_check(self, items, batch_size=100):
        results = []
        for i in range(0, len(items), batch_size):
            if self.check_interrupted():
                break
            batch = items[i:i+batch_size]
            results.extend(self.process_batch(batch))
        return results
```

### 第二阶段：文件分类处理策略（2-3周）

#### 2.1 文件分类系统

- [ ] 实现文件大小分类
  - 小文件（<1MB）
  - 中等文件（1MB-100MB）
  - 大文件（100MB-1GB）
  - 超大文件（>1GB）

```python
def classify_file(file_path):
    """根据文件大小分类"""
    size = os.path.getsize(file_path)
    if size < 1024 * 1024:  # 1MB
        return "small"
    elif size < 100 * 1024 * 1024:  # 100MB
        return "medium"
    elif size < 1024 * 1024 * 1024:  # 1GB
        return "large"
    else:
        return "extra_large"
```

- [ ] 文件类型检测
  - 识别视频文件
  - 识别文本文件
  - 其他文件类型分类

```python
def detect_file_type(file_path):
    """检测文件类型"""
    mime, _ = mimetypes.guess_type(file_path)
    if mime:
        if mime.startswith('video/'):
            return "video"
        elif mime.startswith('text/'):
            return "text"
        # 其他类型检测
    return "binary"
```

#### 2.2 差异化处理策略

- [ ] 小文件处理优化
  - 实现批量完整哈希计算
  - 高并发处理小文件

```python
def process_small_files(files):
    """高并发处理小文件"""
    with ThreadPoolExecutor(max_workers=os.cpu_count() * 2) as executor:
        results = list(executor.map(calculate_hash, files))
    return results
```

- [ ] 大文件处理优化
  - 实现分块流式处理
  - 设置适当的缓冲区大小

```python
def calculate_hash_for_large_file(file_path, block_size=8192*1024):
    """分块计算大文件哈希值"""
    hash_obj = hashlib.sha256()
    with open(file_path, 'rb') as f:
        while True:
            data = f.read(block_size)
            if not data:
                break
            hash_obj.update(data)
    return hash_obj.hexdigest()
```

### 第三阶段：视频文件专用优化（3-4周）

#### 3.1 视频文件特征采样

- [ ] 实现视频文件特征提取
  - 提取视频元数据
  - 计算视频头部和尾部哈希
  - 采样关键帧计算哈希

```python
def extract_video_features(video_path):
    """提取视频特征"""
    features = {}
    # 使用ffmpeg或其他库提取视频元数据
    metadata = extract_video_metadata(video_path)
    features['metadata'] = metadata
    
    # 计算头部哈希
    features['header_hash'] = calculate_video_section_hash(video_path, 0, 1024*1024)
    
    # 计算尾部哈希
    file_size = os.path.getsize(video_path)
    if file_size > 2*1024*1024:  # 如果文件足够大
        features['tail_hash'] = calculate_video_section_hash(
            video_path, file_size - 1024*1024, 1024*1024
        )
    
    # 采样关键帧
    features['keyframes_hash'] = calculate_keyframes_hash(video_path)
    
    return features
```

- [ ] 视频特征哈希存储
  - 设计视频特征存储结构
  - 实现特征比较算法

```python
def store_video_features(file_id, features):
    """存储视频特征"""
    db.video_features.update_one(
        {"file_id": file_id},
        {"$set": features},
        upsert=True
    )

def find_similar_videos(features, threshold=0.9):
    """查找相似视频"""
    # 基于特征相似度查找
    similar_videos = []
    # 实现相似度比较逻辑
    return similar_videos
```

#### 3.2 视频文件流式处理

- [ ] 实现视频文件分块流式处理
  - 设置采样间隔
  - 优化I/O操作
  - 实现进度跟踪

```python
def process_video_in_chunks(video_path, chunk_size=4*1024*1024):
    """分块处理视频文件"""
    hash_obj = hashlib.sha256()
    file_size = os.path.getsize(video_path)
    
    # 计算采样点
    sample_points = calculate_sample_points(file_size)
    
    with open(video_path, 'rb') as f:
        current_pos = 0
        for sample_point in sample_points:
            # 跳到采样点
            if sample_point > current_pos:
                f.seek(sample_point - current_pos, 1)
                current_pos = sample_point
            
            # 读取采样数据
            data = f.read(chunk_size)
            current_pos += len(data)
            
            if data:
                hash_obj.update(data)
            else:
                break
                
            # 更新进度
            progress = current_pos / file_size
            update_progress(progress)
            
            # 检查中断
            if check_interruption_flag():
                return None
    
    return hash_obj.hexdigest()
```

### 第四阶段：资源管理优化（2-3周）

#### 4.1 系统资源监控

- [ ] 实现系统资源监控
  - 监控CPU使用率
  - 监控内存使用情况
  - 监控磁盘I/O负载

```python
def monitor_system_resources():
    """监控系统资源使用情况"""
    cpu_percent = psutil.cpu_percent()
    memory_percent = psutil.virtual_memory().percent
    io_counters = psutil.disk_io_counters()
    
    return {
        "cpu_percent": cpu_percent,
        "memory_percent": memory_percent,
        "io_counters": io_counters
    }
```

#### 4.2 自适应资源分配

- [ ] 实现自适应批处理大小
  - 根据系统负载调整批处理大小
  - 动态调整线程数量

```python
class AdaptiveResourceManager:
    def __init__(self, min_batch_size=50, max_batch_size=500):
        self.min_batch_size = min_batch_size
        self.max_batch_size = max_batch_size
        self.current_batch_size = 200  # 默认值
        
    def adjust_batch_size(self):
        """根据系统负载调整批处理大小"""
        resources = monitor_system_resources()
        
        # 根据CPU负载调整
        if resources["cpu_percent"] > 80:
            self.current_batch_size = max(
                self.min_batch_size, 
                self.current_batch_size * 0.8
            )
        elif resources["cpu_percent"] < 40:
            self.current_batch_size = min(
                self.max_batch_size,
                self.current_batch_size * 1.2
            )
            
        return int(self.current_batch_size)
        
    def get_optimal_thread_count(self):
        """获取最佳线程数"""
        resources = monitor_system_resources()
        base_threads = os.cpu_count()
        
        # 根据CPU和内存负载调整
        if resources["cpu_percent"] > 70 or resources["memory_percent"] > 80:
            return max(1, base_threads // 2)
        else:
            return base_threads
```

### 第五阶段：数据库优化（2周）

#### 5.1 数据库查询优化

- [ ] 优化MongoDB查询
  - 创建适当的索引
  - 优化聚合管道
  - 实现查询超时机制

```python
def setup_database_indexes():
    """设置数据库索引"""
    # 文件集合索引
    db.files.create_index([("exists", 1), ("hash", 1)])
    db.files.create_index([("hash", 1)])
    db.files.create_index([("size", 1)])
    
    # 视频特征集合索引
    db.video_features.create_index([("file_id", 1)])
    db.video_features.create_index([("metadata.duration", 1)])
    db.video_features.create_index([("header_hash", 1)])

def find_duplicates_with_timeout(timeout_ms=30000):
    """带超时的重复文件查询"""
    pipeline = [
        {"$match": {"hash": {"$ne": None}, "exists": True}},
        {"$group": {
            "_id": "$hash",
            "count": {"$sum": 1},
            "files": {"$push": {
                "id": "$_id",
                "path": "$path",
                "size": "$size"
            }}
        }},
        {"$match": {"count": {"$gt": 1}}},
        {"$sort": {"files.size": -1}}
    ]
    
    # 设置最大执行时间
    cursor = db.files.aggregate(
        pipeline, 
        maxTimeMS=timeout_ms
    )
    
    return list(cursor)
```

#### 5.2 数据库连接管理

- [ ] 实现数据库连接池
  - 优化连接创建和复用
  - 处理连接错误和重试

```python
class DatabaseConnectionPool:
    def __init__(self, max_connections=10):
        self.max_connections = max_connections
        self.connections = []
        self.in_use = set()
        self.lock = threading.Lock()
        
    def get_connection(self):
        """获取数据库连接"""
        with self.lock:
            # 尝试复用现有连接
            for conn in self.connections:
                if conn not in self.in_use:
                    self.in_use.add(conn)
                    return conn
            
            # 创建新连接（如果未达到最大连接数）
            if len(self.connections) < self.max_connections:
                conn = create_database_connection()
                self.connections.append(conn)
                self.in_use.add(conn)
                return conn
                
            # 等待可用连接
            return None
    
    def release_connection(self, conn):
        """释放数据库连接"""
        with self.lock:
            if conn in self.in_use:
                self.in_use.remove(conn)
```

### 第六阶段：用户界面优化（2周）

#### 6.1 进度反馈改进

- [ ] 实现详细进度反馈
  - 分阶段进度显示
  - 预计剩余时间计算
  - 处理速度指示器

```python
class EnhancedProgressTracker:
    def __init__(self):
        self.start_time = time.time()
        self.total_files = 0
        self.processed_files = 0
        self.current_stage = "初始化"
        self.stages = ["小文件处理", "中型文件处理", "大型文件处理"]
        self.current_stage_index = 0
        
    def update(self, files_processed, stage=None):
        """更新进度"""
        self.processed_files += files_processed
        
        if stage is not None:
            self.current_stage = stage
            if stage in self.stages:
                self.current_stage_index = self.stages.index(stage)
        
        # 计算总体进度
        stage_weight = 1.0 / len(self.stages)
        base_progress = self.current_stage_index * stage_weight
        stage_progress = 0
        
        if self.total_files > 0:
            stage_progress = (self.processed_files / self.total_files) * stage_weight
            
        total_progress = base_progress + stage_progress
        
        # 计算预计剩余时间
        elapsed = time.time() - self.start_time
        if total_progress > 0:
            estimated_total = elapsed / total_progress
            remaining = estimated_total - elapsed
        else:
            remaining = 0
            
        return {
            "progress": total_progress * 100,  # 百分比
            "stage": self.current_stage,
            "processed": self.processed_files,
            "total": self.total_files,
            "elapsed": elapsed,
            "remaining": remaining
        }
```

#### 6.2 结果分阶段显示

- [ ] 实现结果分阶段显示
  - 小文件结果优先显示
  - 大文件结果后续更新
  - 增量结果更新机制

```python
class IncrementalResultsDisplay:
    def __init__(self, ui_update_callback):
        self.results = {}
        self.ui_update_callback = ui_update_callback
        self.last_update_time = 0
        self.update_interval = 1.0  # 秒
        
    def add_results(self, new_results, category):
        """添加新结果"""
        if category not in self.results:
            self.results[category] = []
            
        self.results[category].extend(new_results)
        
        # 控制UI更新频率
        current_time = time.time()
        if current_time - self.last_update_time >= self.update_interval:
            self.update_ui()
            self.last_update_time = current_time
    
    def update_ui(self):
        """更新UI显示"""
        # 组织结果用于显示
        display_results = []
        
        # 优先显示小文件结果
        categories = ["small", "medium", "large", "extra_large"]
        for category in categories:
            if category in self.results:
                display_results.extend(self.results[category])
                
        # 调用UI更新回调
        self.ui_update_callback(display_results)
```

### 第七阶段：测试与性能调优（2周）

#### 7.1 性能基准测试

- [ ] 实现性能测试框架
  - 测试不同文件类型和大小
  - 测试不同系统负载下的性能
  - 收集性能指标

```python
def run_performance_benchmark():
    """运行性能基准测试"""
    test_scenarios = [
        {"name": "小文件测试", "files": generate_small_test_files(1000)},
        {"name": "混合文件测试", "files": generate_mixed_test_files(500)},
        {"name": "大文件测试", "files": generate_large_test_files(20)}
    ]
    
    results = {}
    for scenario in test_scenarios:
        start_time = time.time()
        process_files(scenario["files"])
        elapsed = time.time() - start_time
        
        results[scenario["name"]] = {
            "elapsed_time": elapsed,
            "files_count": len(scenario["files"]),
            "processing_rate": len(scenario["files"]) / elapsed
        }
        
    return results
```

#### 7.2 性能调优

- [ ] 根据测试结果进行调优
  - 优化批处理大小
  - 调整线程数量
  - 优化内存使用

```python
def tune_performance_parameters():
    """调整性能参数"""
    # 测试不同批处理大小
    batch_sizes = [50, 100, 200, 500, 1000]
    batch_results = {}
    
    for size in batch_sizes:
        set_batch_size(size)
        result = measure_performance()
        batch_results[size] = result
    
    # 找出最佳批处理大小
    optimal_batch_size = max(
        batch_sizes, 
        key=lambda s: batch_results[s]["processing_rate"]
    )
    
    # 测试不同线程数
    thread_counts = [1, 2, 4, 8, 16, os.cpu_count()]
    thread_results = {}
    
    for count in thread_counts:
        set_thread_count(count)
        result = measure_performance()
        thread_results[count] = result
        
    # 找出最佳线程数
    optimal_thread_count = max(
        thread_counts,
        key=lambda c: thread_results[c]["processing_rate"]
    )
    
    return {
        "optimal_batch_size": optimal_batch_size,
        "optimal_thread_count": optimal_thread_count
    }
```

## 实施时间表

| 阶段 | 开始时间 | 结束时间 | 负责人 |
|------|----------|----------|--------|
| 第一阶段：基础架构优化 | 第1周 | 第2周 | TBD |
| 第二阶段：文件分类处理策略 | 第3周 | 第5周 | TBD |
| 第三阶段：视频文件专用优化 | 第6周 | 第9周 | TBD |
| 第四阶段：资源管理优化 | 第10周 | 第12周 | TBD |
| 第五阶段：数据库优化 | 第13周 | 第14周 | TBD |
| 第六阶段：用户界面优化 | 第15周 | 第16周 | TBD |
| 第七阶段：测试与性能调优 | 第17周 | 第18周 | TBD |

## 预期成果

1. 整体文件处理速度提升50%以上
2. 大型视频文件处理速度提升200%以上
3. 用户中断响应时间减少到1秒以内
4. 系统资源使用效率提高30%
5. 数据库查询性能提升40%

## 风险与缓解措施

| 风险 | 可能性 | 影响 | 缓解措施 |
|------|--------|------|----------|
| 视频处理库兼容性问题 | 中 | 高 | 提前测试多种视频格式，准备备选方案 |
| 数据库性能瓶颈 | 高 | 高 | 实施分阶段查询，设置查询超时和重试机制 |
| 系统资源过度使用 | 中 | 中 | 实现自适应资源管理，设置资源使用上限 |
| 用户体验下降 | 低 | 高 | 增加进度反馈，实现结果分阶段显示 |
| 现有功能回归 | 中 | 高 | 编写全面的单元测试和集成测试 |

## 后续优化方向

1. 实现分布式处理架构
2. 添加机器学习模型识别近似重复内容
3. 开发专用的视频内容分析功能
4. 实现更精细的文件相似度比较
5. 优化移动设备和网络存储设备的支持