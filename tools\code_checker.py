#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
代码规则检查器

用于检查代码是否符合项目重构规则
"""

import ast
import os
import sys
import json
import time
from pathlib import Path
from typing import List, Dict, Any, Optional
import argparse
from dataclasses import dataclass, asdict


@dataclass
class RuleViolation:
    """规则违规记录"""
    rule_id: str
    file_path: str
    line_number: int
    message: str
    severity: str = "ERROR"
    
    def __str__(self):
        return f"{self.severity}: {self.rule_id} - {self.file_path}:{self.line_number} - {self.message}"


class CodeRuleChecker:
    """代码规则检查器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.violations: List[RuleViolation] = []
        self.stats = {
            "files_checked": 0,
            "total_lines": 0,
            "violations_found": 0,
            "rules_checked": 0
        }
    
    def check_all_rules(self) -> List[RuleViolation]:
        """检查所有规则"""
        self.violations.clear()
        self.stats = {"files_checked": 0, "total_lines": 0, "violations_found": 0, "rules_checked": 8}
        
        print("🔍 开始代码规则检查...")
        
        # 检查Python文件
        python_files = list(self.project_root.rglob("*.py"))
        total_files = len([f for f in python_files if not self._should_skip_file(f)])
        
        for i, py_file in enumerate(python_files):
            if self._should_skip_file(py_file):
                continue
                
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    tree = ast.parse(content)
                    
                self._check_file_rules(py_file, tree, content)
                self.stats["files_checked"] += 1
                self.stats["total_lines"] += len(content.splitlines())
                
                # 显示进度
                if (i + 1) % 10 == 0 or i == total_files - 1:
                    progress = (i + 1) / total_files * 100
                    print(f"  进度: {progress:.1f}% ({i + 1}/{total_files})")
                    
            except Exception as e:
                print(f"⚠️  警告: 无法解析文件 {py_file}: {e}")
        
        self.stats["violations_found"] = len(self.violations)
        return self.violations
    
    def _should_skip_file(self, file_path: Path) -> bool:
        """判断是否跳过文件检查"""
        skip_dirs = {'.git', '__pycache__', '.pytest_cache', 'venv', 'env', 'node_modules', '.vscode', 'htmlcov'}
        skip_files = {'__init__.py'}

        # 跳过特定目录
        if any(part in skip_dirs for part in file_path.parts):
            return True

        # 跳过特定文件
        if file_path.name in skip_files:
            return True

        # 跳过测试文件和工具文件（但允许检查demo文件）
        if ('test' in file_path.name.lower() and not 'demo' in file_path.name.lower()) or 'tools' in file_path.parts:
            return True

        # 跳过临时文件和备份文件
        if file_path.name.endswith(('.tmp', '.bak', '.swp', '.pyc')):
            return True

        return False
    
    def _check_file_rules(self, file_path: Path, tree: ast.AST, content: str):
        """检查单个文件的规则"""
        # RULE-001: 检查模块化设计
        self._check_modular_design(file_path, tree)
        
        # RULE-002: 检查DTO使用
        self._check_dto_usage(file_path, tree)
        
        # RULE-003: 检查事件驱动通信
        self._check_event_driven_communication(file_path, tree)
        
        # RULE-004: 检查数据库操作
        self._check_database_operations(file_path, tree)
        
        # RULE-005: 检查异步任务
        self._check_async_tasks(file_path, tree)
        
        # PERF-001: 检查性能规则
        self._check_performance_rules(file_path, tree)

        # ARCH-001: 检查架构规则
        self._check_architecture_rules(file_path, tree)

        # CODE-001: 检查编程规范
        self._check_coding_standards(file_path, tree, content)

        # QUAL-001: 检查代码质量
        self._check_code_quality(file_path, tree)
    
    def _check_modular_design(self, file_path: Path, tree: ast.AST):
        """检查模块化设计规则 - RULE-001"""
        # 检查UI层是否直接进行数据库操作
        if 'ui' in file_path.parts:
            for node in ast.walk(tree):
                if isinstance(node, ast.Call):
                    if self._is_database_call(node):
                        self.violations.append(RuleViolation(
                            "RULE-001",
                            str(file_path),
                            node.lineno,
                            "UI层禁止直接进行数据库操作，应通过事件总线请求数据"
                        ))
                        
        # 检查业务层是否直接操作UI
        if 'services' in file_path.parts:
            for node in ast.walk(tree):
                if isinstance(node, ast.Call):
                    if self._is_ui_operation(node):
                        self.violations.append(RuleViolation(
                            "RULE-001",
                            str(file_path),
                            node.lineno,
                            "业务服务层禁止直接操作UI组件，应通过事件总线通知UI"
                        ))
    
    def _check_dto_usage(self, file_path: Path, tree: ast.AST):
        """检查DTO使用规范 - RULE-002"""
        # 只检查DTO目录中的文件
        if 'dto' not in file_path.parts:
            return

        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                # 检查是否有@dataclass装饰器
                has_dataclass = any(
                    (isinstance(decorator, ast.Name) and decorator.id == 'dataclass') or
                    (isinstance(decorator, ast.Attribute) and decorator.attr == 'dataclass') or
                    (isinstance(decorator, ast.Call) and
                     ((isinstance(decorator.func, ast.Name) and decorator.func.id == 'dataclass') or
                      (isinstance(decorator.func, ast.Attribute) and decorator.func.attr == 'dataclass')))
                    for decorator in node.decorator_list
                )

                # 检查是否为Enum类
                is_enum = any(
                    isinstance(base, ast.Name) and base.id == 'Enum'
                    for base in node.bases
                )

                # 如果类名包含DTO相关关键词但没有@dataclass
                dto_keywords = ['request', 'response', 'dto', 'data', 'result', 'info', 'preview', 'group', 'rule']
                if any(keyword in node.name.lower() for keyword in dto_keywords):
                    # Enum类和特殊类不需要@dataclass
                    if (not has_dataclass and not node.name.endswith('Base')
                        and node.name != 'EventData' and not is_enum
                        and not node.name.endswith('Type') and not node.name.endswith('Status')
                        and not node.name.endswith('Action')):
                        self.violations.append(RuleViolation(
                            "RULE-002",
                            str(file_path),
                            node.lineno,
                            f"DTO类 {node.name} 必须使用@dataclass装饰器"
                        ))

                    # 检查是否有类型注解
                    if has_dataclass:
                        for field in node.body:
                            if isinstance(field, ast.AnnAssign) and field.annotation is None:
                                self.violations.append(RuleViolation(
                                    "RULE-002",
                                    str(file_path),
                                    field.lineno,
                                    f"DTO字段必须有类型注解"
                                ))
    
    def _check_event_driven_communication(self, file_path: Path, tree: ast.AST):
        """检查事件驱动通信规则 - RULE-003"""
        # 检查是否有直接的模块间调用
        for node in ast.walk(tree):
            if isinstance(node, ast.Call):
                if self._is_direct_module_call(node):
                    self.violations.append(RuleViolation(
                        "RULE-003",
                        str(file_path),
                        node.lineno,
                        "模块间通信必须通过事件总线，禁止直接方法调用"
                    ))
    
    def _check_database_operations(self, file_path: Path, tree: ast.AST):
        """检查数据库操作规范 - RULE-004"""
        for node in ast.walk(tree):
            if isinstance(node, ast.Call):
                # 检查是否在循环中进行数据库操作
                if self._is_database_call(node) and self._is_in_loop(node, tree):
                    self.violations.append(RuleViolation(
                        "RULE-004",
                        str(file_path),
                        node.lineno,
                        "禁止在循环中进行单独的数据库操作，应使用批量操作"
                    ))
                    
                # 检查是否使用了字符串拼接构建查询
                if self._is_string_concat_query(node):
                    self.violations.append(RuleViolation(
                        "RULE-004",
                        str(file_path),
                        node.lineno,
                        "禁止使用字符串拼接构建SQL查询，应使用参数化查询"
                    ))
    
    def _check_async_tasks(self, file_path: Path, tree: ast.AST):
        """检查异步任务规范 - RULE-005"""
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                # 检查长时间运行的操作是否为异步
                if self._is_long_running_operation(node) and not self._is_async_function(node):
                    self.violations.append(RuleViolation(
                        "RULE-005",
                        str(file_path),
                        node.lineno,
                        f"长时间运行的操作 {node.name} 应该实现为异步任务"
                    ))
    
    def _check_performance_rules(self, file_path: Path, tree: ast.AST):
        """检查性能规则 - PERF-001"""
        for node in ast.walk(tree):
            # 检查大文件处理
            if isinstance(node, ast.Call) and self._is_file_read_all(node):
                self.violations.append(RuleViolation(
                    "PERF-001",
                    str(file_path),
                    node.lineno,
                    "大文件应使用分块读取，避免一次性加载到内存"
                ))
                
            # 检查内存使用
            if isinstance(node, ast.ListComp) and self._is_large_list_comprehension(node):
                self.violations.append(RuleViolation(
                    "PERF-001",
                    str(file_path),
                    node.lineno,
                    "大量数据处理应使用生成器表达式，避免创建大列表"
                ))
    
    def _check_architecture_rules(self, file_path: Path, tree: ast.AST):
        """检查架构规则 - ARCH-001"""
        # 检查依赖方向
        if self._is_lower_layer_depending_on_upper(file_path, tree):
            self.violations.append(RuleViolation(
                "ARCH-001",
                str(file_path),
                1,
                "下层模块不应依赖上层模块，违反了分层架构原则"
            ))
    
    def _check_coding_standards(self, file_path: Path, tree: ast.AST, content: str):
        """检查编程规范 - CODE-001"""
        lines = content.splitlines()
        
        # 检查空的except块
        for node in ast.walk(tree):
            if isinstance(node, ast.ExceptHandler):
                if not node.body or (len(node.body) == 1 and isinstance(node.body[0], ast.Pass)):
                    self.violations.append(RuleViolation(
                        "CODE-001",
                        str(file_path),
                        node.lineno,
                        "禁止使用空的except块，应该正确处理异常"
                    ))
        
        # 检查函数长度
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                func_lines = getattr(node, 'end_lineno', node.lineno) - node.lineno + 1

                # 对于测试函数和特殊函数，放宽限制
                if (node.name.startswith('test_') or
                    node.name.startswith('_') or
                    node.name in ['__init__', '__str__', '__repr__', '__call__'] or
                    'test' in str(file_path).lower()):
                    max_lines = 80  # 测试函数和私有函数可以更长
                else:
                    max_lines = 50

                if func_lines > max_lines:
                    self.violations.append(RuleViolation(
                        "CODE-001",
                        str(file_path),
                        node.lineno,
                        f"函数 {node.name} 过长({func_lines}行)，建议拆分为更小的函数"
                    ))
    
    # 辅助方法
    def _is_database_call(self, node: ast.Call) -> bool:
        """判断是否为数据库调用"""
        if isinstance(node.func, ast.Attribute):
            db_methods = ['query', 'insert', 'update', 'delete', 'find', 'save', 'execute']
            return node.func.attr in db_methods
        return False
    
    def _is_ui_operation(self, node: ast.Call) -> bool:
        """判断是否为UI操作"""
        if isinstance(node.func, ast.Attribute):
            ui_methods = ['update', 'refresh', 'show', 'hide', 'set_text', 'set_value']
            return node.func.attr in ui_methods and self._looks_like_ui_object(node.func.value)
        return False
    
    def _looks_like_ui_object(self, node: ast.AST) -> bool:
        """判断是否看起来像UI对象"""
        if isinstance(node, ast.Name):
            ui_keywords = ['window', 'panel', 'button', 'label', 'tree', 'list', 'table']
            return any(keyword in node.id.lower() for keyword in ui_keywords)
        return False

    def _check_code_quality(self, file_path: Path, tree: ast.AST):
        """检查代码质量 - QUAL-001"""
        # 为节点添加父节点信息
        for parent in ast.walk(tree):
            for child in ast.iter_child_nodes(parent):
                child.parent = parent

        for node in ast.walk(tree):
            # 检查过深的嵌套
            if isinstance(node, (ast.If, ast.For, ast.While, ast.With)):
                nesting_level = self._calculate_nesting_level(node)
                if nesting_level > 4:
                    self.violations.append(RuleViolation(
                        "QUAL-001",
                        str(file_path),
                        node.lineno,
                        f"代码嵌套层级过深({nesting_level}层)，建议重构以降低复杂度"
                    ))

            # 检查过长的参数列表
            if isinstance(node, ast.FunctionDef):
                param_count = len(node.args.args)
                if param_count > 6:
                    self.violations.append(RuleViolation(
                        "QUAL-001",
                        str(file_path),
                        node.lineno,
                        f"函数 {node.name} 参数过多({param_count}个)，建议使用配置对象或DTO"
                    ))

            # 检查魔法数字（跳过常量定义）
            if (isinstance(node, ast.Constant) and
                isinstance(node.value, (int, float)) and
                not self._is_acceptable_number(node.value) and
                not self._is_constant_definition(node)):
                self.violations.append(RuleViolation(
                    "QUAL-001",
                    str(file_path),
                    node.lineno,
                    f"避免使用魔法数字 {node.value}，建议定义为常量"
                ))

    def _calculate_nesting_level(self, node: ast.AST, level: int = 0) -> int:
        """计算嵌套层级"""
        max_level = level
        for child in ast.iter_child_nodes(node):
            if isinstance(child, (ast.If, ast.For, ast.While, ast.With, ast.Try)):
                child_level = self._calculate_nesting_level(child, level + 1)
                max_level = max(max_level, child_level)
        return max_level

    def _is_acceptable_number(self, num) -> bool:
        """判断数字是否可接受（不是魔法数字）"""
        # 常见的可接受数字
        acceptable_numbers = {0, 1, 2, -1, 10, 100, 1000}
        return num in acceptable_numbers

    def _is_constant_definition(self, node: ast.Constant) -> bool:
        """判断是否为常量定义"""
        # 检查父节点是否为赋值语句
        parent = getattr(node, 'parent', None)
        if isinstance(parent, ast.Assign):
            # 检查变量名是否为大写（常量命名约定）
            for target in parent.targets:
                if isinstance(target, ast.Name) and target.id.isupper():
                    return True
        return False
    
    def _is_direct_module_call(self, node: ast.Call) -> bool:
        """判断是否为直接的模块间调用"""
        if isinstance(node.func, ast.Attribute):
            if isinstance(node.func.value, ast.Name):
                # 检查是否调用其他模块的方法
                suspicious_names = ['service', 'manager', 'handler', 'processor']
                return any(name in node.func.value.id.lower() for name in suspicious_names)
        return False
    
    def _is_in_loop(self, target_node: ast.AST, tree: ast.AST) -> bool:
        """判断节点是否在循环中"""
        for node in ast.walk(tree):
            if isinstance(node, (ast.For, ast.While)):
                for child in ast.walk(node):
                    if child is target_node:
                        return True
        return False
    
    def _is_string_concat_query(self, node: ast.Call) -> bool:
        """判断是否使用字符串拼接构建查询"""
        if self._is_database_call(node) and node.args:
            first_arg = node.args[0]
            return isinstance(first_arg, (ast.BinOp, ast.JoinedStr))
        return False
    
    def _is_long_running_operation(self, node: ast.FunctionDef) -> bool:
        """判断是否为长时间运行的操作"""
        # 排除工厂方法、属性方法等
        if (node.name.startswith('new_') or node.name.startswith('create_')
            or node.name.startswith('from_') or node.name.startswith('to_')
            or node.name.startswith('is_') or node.name.startswith('get_')
            or node.name.startswith('set_') or node.name == '__init__'
            or node.name.startswith('_')):
            return False

        long_running_keywords = ['scan', 'calculate', 'process', 'analyze', 'hash', 'search', 'index']
        return any(keyword in node.name.lower() for keyword in long_running_keywords)
    
    def _is_async_function(self, node: ast.FunctionDef) -> bool:
        """判断是否为异步函数"""
        return isinstance(node, ast.AsyncFunctionDef)
    
    def _is_file_read_all(self, node: ast.Call) -> bool:
        """判断是否一次性读取整个文件"""
        if isinstance(node.func, ast.Attribute):
            return node.func.attr in ['read', 'readlines'] and not node.args
        return False
    
    def _is_large_list_comprehension(self, node: ast.ListComp) -> bool:
        """判断是否为大型列表推导式"""
        # 简化判断：如果包含文件操作或数据库操作
        for generator in node.generators:
            if isinstance(generator.iter, ast.Call):
                if self._is_database_call(generator.iter) or self._is_file_operation(generator.iter):
                    return True
        return False
    
    def _is_file_operation(self, node: ast.Call) -> bool:
        """判断是否为文件操作"""
        if isinstance(node.func, ast.Name):
            return node.func.id in ['open', 'listdir', 'walk']
        return False
    
    def _is_lower_layer_depending_on_upper(self, file_path: Path, tree: ast.AST) -> bool:
        """判断是否下层依赖上层"""
        # 简化检查：数据层不应导入UI层，但允许导入标准库和同层模块
        if 'data' in file_path.parts or 'db' in file_path.parts:
            # 允许的导入模块
            allowed_modules = {
                'typing', 'dataclasses', 'enum', 'uuid', 'time', 'datetime',
                'json', 'abc', 'collections', 'functools', 'itertools',
                're', 'os', 'sys', 'pathlib', 'logging'
            }

            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        module_name = alias.name.split('.')[0]
                        if 'ui' in alias.name and module_name not in allowed_modules:
                            return True
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        module_name = node.module.split('.')[0]
                        if 'ui' in node.module and module_name not in allowed_modules:
                            return True
        return False
    
    def generate_report(self) -> Dict[str, Any]:
        """生成检查报告"""
        violations_by_rule = {}
        violations_by_severity = {}
        violations_by_file = {}
        
        for violation in self.violations:
            # 按规则分组
            if violation.rule_id not in violations_by_rule:
                violations_by_rule[violation.rule_id] = []
            violations_by_rule[violation.rule_id].append(violation)
            
            # 按严重程度分组
            if violation.severity not in violations_by_severity:
                violations_by_severity[violation.severity] = []
            violations_by_severity[violation.severity].append(violation)
            
            # 按文件分组
            if violation.file_path not in violations_by_file:
                violations_by_file[violation.file_path] = []
            violations_by_file[violation.file_path].append(violation)
        
        return {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "summary": {
                "total_violations": len(self.violations),
                "files_checked": self.stats["files_checked"],
                "total_lines": self.stats["total_lines"],
                "rules_checked": self.stats["rules_checked"]
            },
            "violations_by_rule": {rule: len(viols) for rule, viols in violations_by_rule.items()},
            "violations_by_severity": {sev: len(viols) for sev, viols in violations_by_severity.items()},
            "violations_by_file": {file: len(viols) for file, viols in violations_by_file.items()},
            "top_violations": [asdict(v) for v in self.violations[:10]]
        }


def main():
    parser = argparse.ArgumentParser(description='智能文件管理器代码规则检查器')
    parser.add_argument('--project-root', default='.', help='项目根目录')
    parser.add_argument('--check-rules', action='store_true', help='检查编程规则')
    parser.add_argument('--generate-report', action='store_true', help='生成详细报告')
    parser.add_argument('--output-format', choices=['text', 'json'], default='text', help='输出格式')
    parser.add_argument('--output-file', help='输出文件路径')

    args = parser.parse_args()

    if not (args.check_rules or args.generate_report):
        parser.print_help()
        return

    checker = CodeRuleChecker(args.project_root)
    violations = checker.check_all_rules()

    if args.generate_report:
        report = checker.generate_report()

        if args.output_format == 'json':
            output = json.dumps(report, indent=2, ensure_ascii=False)
        else:
            output = f"""
📊 代码质量检查报告
==================

📈 统计信息:
- 检查文件数: {report['summary']['files_checked']}
- 代码总行数: {report['summary']['total_lines']}
- 检查规则数: {report['summary']['rules_checked']}
- 发现违规数: {report['summary']['total_violations']}

📋 违规分布:
按规则分布: {report['violations_by_rule']}
按严重程度: {report['violations_by_severity']}

🔝 主要违规:
"""
            for i, violation in enumerate(report['top_violations'][:5], 1):
                output += f"{i}. {violation['rule_id']}: {violation['message']}\n"

        if args.output_file:
            with open(args.output_file, 'w', encoding='utf-8') as f:
                f.write(output)
            print(f"📄 报告已保存到: {args.output_file}")
        else:
            print(output)

    if violations:
        print(f"\n❌ 发现 {len(violations)} 个规则违规:")
        for violation in violations[:20]:  # 只显示前20个
            print(f"  {violation}")

        if len(violations) > 20:
            print(f"  ... 还有 {len(violations) - 20} 个违规项")

        sys.exit(1)
    else:
        print("\n✅ 所有规则检查通过!")
        sys.exit(0)


if __name__ == '__main__':
    main()
