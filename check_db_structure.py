#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
检查数据库结构脚本
"""

from src.data.db_manager import MongoDBManager

def check_database_structure():
    """检查数据库结构和数据一致性"""
    try:
        # 创建数据库管理器
        db = MongoDBManager()
        
        # 检查连接状态
        print("数据库连接状态:", db.check_connection_health())
        
        # 获取文件记录
        files = db.find_files({})
        print("数据库中文件记录数:", len(files))
        
        if files:
            # 分析第一条记录的字段
            first_record = files[0]
            print("\n第一条记录的字段:", list(first_record.keys()))
            
            # 检查关键字段
            key_fields = ['path', 'file_path', 'name', 'size', 'extension']
            print("\n关键字段检查:")
            for field in key_fields:
                if field in first_record:
                    print(f"  ✓ {field}: {first_record[field]}")
                else:
                    print(f"  ✗ {field}: 字段不存在")
            
            # 检查新增字段
            new_fields = ['folder_id', 'tree_node_id', 'relative_path', 'depth']
            print("\n新增字段检查:")
            for field in new_fields:
                if field in first_record:
                    print(f"  ✓ {field}: {first_record[field]}")
                else:
                    print(f"  ✗ {field}: 字段不存在")
            
            # 显示完整的示例记录
            print("\n示例记录:")
            for key, value in first_record.items():
                print(f"  {key}: {value}")
        else:
            print("数据库中没有文件记录")
        
        # 检查集合信息
        print("\n数据库集合信息:")
        collections = db.db.list_collection_names()
        print("集合列表:", collections)
        
        # 检查索引
        if 'files' in collections:
            indexes = list(db.db.files.list_indexes())
            print("\nfiles集合索引:")
            for idx in indexes:
                print(f"  {idx['name']}: {idx.get('key', {})}")
        
        db.close()
        
    except Exception as e:
        print(f"检查数据库结构时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_database_structure()