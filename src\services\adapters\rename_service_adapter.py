#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
重命名服务适配器

提供向后兼容性，将新的重命名服务适配到现有的接口
遵循RULE-001: 模块职责单一原则 - 只负责接口适配
"""

import asyncio
import time
from typing import Dict, List, Optional, Callable, Any
from pathlib import Path

from src.services.implementations.rename_service import RenameServiceImpl
from src.data.dto.rename_dto import RenameRequest, RenameRule, RenameRuleType
from src.ui.events.event_bus import EventBus

# 常量定义
DEFAULT_CALLBACK_INTERVAL = 1.0  # 回调间隔（秒）
DEFAULT_MIN_FILE_SIZE = 0  # 默认最小文件大小


class RenameServiceAdapter:
    """
    重命名服务适配器
    
    将新的RenameServiceImpl适配到现有的重命名接口
    确保向后兼容性，同时利用新架构的优势
    """
    
    def __init__(self, event_bus: Optional[EventBus] = None):
        """初始化适配器"""
        self._event_bus = event_bus or EventBus()
        self._service = RenameServiceImpl(self._event_bus)
        self._running_tasks: Dict[str, Any] = {}  # 可以存储Thread或Task
        self._task_status: Dict[str, str] = {}  # 存储任务状态
        
        # 启动事件总线（如果需要）
        if not self._event_bus._running:
            self._event_bus.start()
    
    async def start(self):
        """启动适配器"""
        await self._service.start_service()
    
    async def stop(self):
        """停止适配器"""
        # 取消所有运行中的任务
        for task_id, task in self._running_tasks.items():
            if hasattr(task, 'cancel'):
                task.cancel()
            self._task_status[task_id] = "cancelled"
        
        # 等待asyncio任务完成
        async_tasks = [task for task in self._running_tasks.values() 
                      if hasattr(task, 'cancel')]
        if async_tasks:
            await asyncio.gather(*async_tasks, return_exceptions=True)
        
        await self._service.stop_service()
    
    def preview_rename(self, rules: List[Dict[str, Any]], files: List[str]) -> List[Dict[str, str]]:
        """
        预览重命名结果（兼容旧接口）
        
        参数:
            rules: 重命名规则列表（旧格式）
            files: 文件路径列表
        
        返回:
            重命名预览结果 [{"old_path": "", "new_path": ""}]
        """
        # 转换规则格式
        rename_rules = self._convert_legacy_rules(rules)
        
        # 创建重命名请求
        request = RenameRequest(
            task_id=self._generate_task_id(),
            files=files,
            rules=rename_rules,
            preview_only=True
        )
        
        # 执行预览
        try:
            previews = asyncio.run(self._service.preview_rename(request))
            
            # 转换为旧格式
            result = []
            for preview in previews:
                result.append({
                    "old_path": preview.original_path,
                    "new_path": preview.new_path,
                    "old_name": preview.original_name,
                    "new_name": preview.new_name,
                    "has_conflict": preview.has_conflict,
                    "error": preview.error_message
                })
            
            return result
        
        except Exception as e:
            # 返回错误结果
            return [{"old_path": f, "new_path": f, "error": str(e)} for f in files]
    
    async def apply_rename_async(self, request_dict: Dict[str, Any]) -> Dict[str, Any]:
        """
        异步应用重命名（兼容旧接口）
        
        参数:
            request_dict: 重命名请求字典
        
        返回:
            重命名结果字典
        """
        # 转换请求格式
        rename_request = self._convert_legacy_request(request_dict)
        
        # 执行重命名
        result_dict = {"success": False, "message": "", "details": {}}
        
        try:
            # 收集进度更新
            progress_updates = []
            async for progress in self._service.execute_rename(rename_request):
                progress_updates.append(progress)
                if progress.progress >= 100.0:
                    break
            
            # 获取结果
            result = await self._service.get_rename_result(rename_request.task_id)
            if result:
                result_dict = {
                    "success": result.successful_renames > 0,
                    "message": f"成功重命名 {result.successful_renames} 个文件",
                    "details": {
                        "total_files": result.total_files,
                        "successful": result.successful_renames,
                        "failed": result.failed_renames,
                        "skipped": result.skipped_files,
                        "duration": result.duration,
                        "success_rate": result.success_rate
                    }
                }
        
        except Exception as e:
            result_dict = {
                "success": False,
                "message": f"重命名失败: {str(e)}",
                "details": {"error": str(e)}
            }
        
        return result_dict
    
    def apply_rename(self, request_dict: Dict[str, Any]) -> Dict[str, Any]:
        """
        同步应用重命名（兼容旧接口）
        
        参数:
            request_dict: 重命名请求字典
        
        返回:
            重命名结果字典
        """
        try:
            return asyncio.run(self.apply_rename_async(request_dict))
        except Exception as e:
            return {
                "success": False,
                "message": f"重命名失败: {str(e)}",
                "details": {"error": str(e)}
            }
    
    def _convert_legacy_rules(self, legacy_rules: List[Dict[str, Any]]) -> List[RenameRule]:
        """将旧格式的规则转换为新格式"""
        rules = []
        
        for rule_dict in legacy_rules:
            rule_type_str = rule_dict.get("type", "replace_text")
            
            # 映射规则类型
            if rule_type_str == "replace_text":
                rule_type = RenameRuleType.REPLACE_TEXT
            elif rule_type_str == "add_prefix":
                rule_type = RenameRuleType.ADD_PREFIX
            elif rule_type_str == "add_suffix":
                rule_type = RenameRuleType.ADD_SUFFIX
            elif rule_type_str == "remove_text":
                rule_type = RenameRuleType.REMOVE_TEXT
            elif rule_type_str == "change_case":
                rule_type = RenameRuleType.CHANGE_CASE
            elif rule_type_str == "regex_replace":
                rule_type = RenameRuleType.REGEX_REPLACE
            else:
                rule_type = RenameRuleType.REPLACE_TEXT  # 默认
            
            # 创建规则
            rule = RenameRule(
                rule_type=rule_type,
                old_text=rule_dict.get("old_text"),
                new_text=rule_dict.get("new_text"),
                regex_pattern=rule_dict.get("pattern"),
                apply_to_extension=rule_dict.get("apply_to_extension", False)
            )
            
            rules.append(rule)
        
        return rules
    
    def _convert_legacy_request(self, request_dict: Dict[str, Any]) -> RenameRequest:
        """将旧格式的请求转换为新格式"""
        task_id = request_dict.get("task_id", self._generate_task_id())
        files = request_dict.get("files", [])
        rules = self._convert_legacy_rules(request_dict.get("rules", []))
        
        return RenameRequest(
            task_id=task_id,
            files=files,
            rules=rules,
            preview_only=request_dict.get("preview_only", False),
            backup=request_dict.get("backup", True),
            overwrite_existing=request_dict.get("overwrite_existing", False)
        )
    
    def _generate_task_id(self) -> str:
        """生成任务ID"""
        import uuid
        return str(uuid.uuid4())
    
    def get_task_status(self, task_id: str) -> Optional[str]:
        """获取任务状态（兼容旧接口）"""
        return self._task_status.get(task_id)
    
    async def get_task_result(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务结果（兼容旧接口）"""
        result = await self._service.get_rename_result(task_id)
        if result:
            return {
                "task_id": result.task_id,
                "total_files": result.total_files,
                "successful": result.successful_renames,
                "failed": result.failed_renames,
                "skipped": result.skipped_files,
                "duration": result.duration,
                "success_rate": result.success_rate,
                "errors": result.errors or [],
                "warnings": result.warnings or []
            }
        return None
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务（兼容旧接口）"""
        if task_id in self._running_tasks:
            task = self._running_tasks[task_id]
            if hasattr(task, 'cancel'):
                task.cancel()
            self._task_status[task_id] = "cancelled"
            self._running_tasks.pop(task_id, None)
            return True
        return False
    
    async def undo_rename(self, task_id: str) -> bool:
        """撤销重命名（兼容旧接口）"""
        return await self._service.undo_rename(task_id)
    
    async def get_rename_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取重命名历史（兼容旧接口）"""
        return await self._service.get_rename_history(limit)


# 向后兼容性函数
async def apply_rename_rules_async(files: List[str], rules: List[Dict[str, Any]],
                                 progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
    """
    向后兼容的重命名函数（重写）
    
    使用新的服务架构，但保持旧的接口
    """
    adapter = RenameServiceAdapter()
    await adapter.start()
    
    try:
        request_dict = {
            "files": files,
            "rules": rules,
            "preview_only": False,
            "backup": True
        }
        
        return await adapter.apply_rename_async(request_dict)
    finally:
        await adapter.stop()


def apply_rename_rules(files: List[str], rules: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    向后兼容的同步重命名函数（重写）
    
    使用新的服务架构，但保持旧的接口
    """
    adapter = RenameServiceAdapter()
    
    try:
        request_dict = {
            "files": files,
            "rules": rules,
            "preview_only": False,
            "backup": True
        }
        
        return adapter.apply_rename(request_dict)
    except Exception as e:
        return {
            "success": False,
            "message": f"重命名失败: {str(e)}",
            "details": {"error": str(e)}
        }


# 全局适配器实例（用于向后兼容）
_global_adapter: Optional[RenameServiceAdapter] = None


def get_global_rename_adapter() -> RenameServiceAdapter:
    """获取全局重命名服务适配器"""
    global _global_adapter
    if _global_adapter is None:
        _global_adapter = RenameServiceAdapter()
    return _global_adapter
