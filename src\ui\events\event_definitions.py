#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
事件定义模块

定义系统中所有的事件类型和事件数据结构
遵循RULE-003: 事件驱动通信规范
"""

from dataclasses import dataclass
from typing import Any, Dict, Optional
from enum import Enum
import time
import uuid


class EventPriority(Enum):
    """事件优先级枚举"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


@dataclass(frozen=True)
class EventData:
    """
    事件数据基类

    所有事件数据都应该继承此类
    """
    event_id: str
    event_type: str
    source: str
    timestamp: float
    priority: EventPriority = EventPriority.NORMAL
    data: Optional[Dict[str, Any]] = None

    @classmethod
    def create(cls, event_type: str, source: str, data: Optional[Dict[str, Any]] = None,
               priority: EventPriority = EventPriority.NORMAL) -> 'EventData':
        """创建事件数据"""
        return cls(
            event_id=str(uuid.uuid4()),
            event_type=event_type,
            source=source,
            timestamp=time.time(),
            priority=priority,
            data=data or {}
        )


# UI事件类型定义 (遵循 动词_名词_状态 格式)
class UIEventType(Enum):
    """UI事件类型枚举"""
    # 扫描相关事件
    SCAN_REQUESTED = "SCAN_REQUESTED"
    SCAN_CANCELLED = "SCAN_CANCELLED"

    # 重复文件相关事件
    DUPLICATE_CHECK_REQUESTED = "DUPLICATE_CHECK_REQUESTED"
    DUPLICATE_ACTION_REQUESTED = "DUPLICATE_ACTION_REQUESTED"

    # 重命名相关事件
    RENAME_REQUESTED = "RENAME_REQUESTED"
    RENAME_PREVIEW_REQUESTED = "RENAME_PREVIEW_REQUESTED"

    # 垃圾文件相关事件
    JUNK_SCAN_REQUESTED = "JUNK_SCAN_REQUESTED"
    JUNK_CLEANUP_REQUESTED = "JUNK_CLEANUP_REQUESTED"

    # 白名单相关事件
    WHITELIST_ADD_REQUESTED = "WHITELIST_ADD_REQUESTED"
    WHITELIST_REMOVE_REQUESTED = "WHITELIST_REMOVE_REQUESTED"

    # 界面相关事件
    UI_REFRESH_REQUESTED = "UI_REFRESH_REQUESTED"
    UI_STATUS_UPDATE_REQUESTED = "UI_STATUS_UPDATE_REQUESTED"
    UI_PROGRESS_UPDATE_REQUESTED = "UI_PROGRESS_UPDATE_REQUESTED"


# 业务事件类型定义
class BusinessEventType(Enum):
    """业务事件类型枚举"""
    # 扫描相关事件
    SCAN_STARTED = "SCAN_STARTED"
    SCAN_PROGRESS = "SCAN_PROGRESS"
    SCAN_COMPLETED = "SCAN_COMPLETED"
    SCAN_FAILED = "SCAN_FAILED"
    SCAN_PAUSED = "SCAN_PAUSED"
    SCAN_RESUMED = "SCAN_RESUMED"
    SCAN_CANCELLED = "SCAN_CANCELLED"
    SCAN_ERROR = "SCAN_ERROR"
    SCAN_PRIORITY_CHANGED = "SCAN_PRIORITY_CHANGED"

    # 文件处理事件
    FILE_PROCESSED = "FILE_PROCESSED"
    FILE_HASH_CALCULATED = "FILE_HASH_CALCULATED"
    FILE_HASH_FAILED = "FILE_HASH_FAILED"

    # 重复文件相关事件
    DUPLICATE_CHECK_STARTED = "DUPLICATE_CHECK_STARTED"
    DUPLICATE_CHECK_PROGRESS = "DUPLICATE_CHECK_PROGRESS"
    DUPLICATE_CHECK_COMPLETED = "DUPLICATE_CHECK_COMPLETED"
    DUPLICATE_GROUP_FOUND = "DUPLICATE_GROUP_FOUND"
    DUPLICATE_ACTION_STARTED = "DUPLICATE_ACTION_STARTED"
    DUPLICATE_ACTION_COMPLETED = "DUPLICATE_ACTION_COMPLETED"

    # 重命名相关事件
    RENAME_STARTED = "RENAME_STARTED"
    RENAME_PROGRESS = "RENAME_PROGRESS"
    RENAME_COMPLETED = "RENAME_COMPLETED"
    RENAME_PREVIEW_GENERATED = "RENAME_PREVIEW_GENERATED"

    # 垃圾文件相关事件
    JUNK_SCAN_STARTED = "JUNK_SCAN_STARTED"
    JUNK_SCAN_COMPLETED = "JUNK_SCAN_COMPLETED"
    JUNK_FILE_FOUND = "JUNK_FILE_FOUND"
    JUNK_CLEANUP_STARTED = "JUNK_CLEANUP_STARTED"
    JUNK_CLEANUP_COMPLETED = "JUNK_CLEANUP_COMPLETED"

    # 白名单相关事件
    WHITELIST_UPDATED = "WHITELIST_UPDATED"
    WHITELIST_CHECK_COMPLETED = "WHITELIST_CHECK_COMPLETED"

    # 数据库相关事件
    DATABASE_UPDATED = "DATABASE_UPDATED"
    DATABASE_ERROR = "DATABASE_ERROR"


# 系统事件类型定义
class SystemEventType(Enum):
    """系统事件类型枚举"""
    # 应用程序生命周期
    APPLICATION_STARTED = "APPLICATION_STARTED"
    APPLICATION_STOPPING = "APPLICATION_STOPPING"
    APPLICATION_ERROR = "APPLICATION_ERROR"

    # 任务管理
    TASK_CREATED = "TASK_CREATED"
    TASK_QUEUED = "TASK_QUEUED"
    TASK_STARTED = "TASK_STARTED"
    TASK_COMPLETED = "TASK_COMPLETED"
    TASK_FAILED = "TASK_FAILED"
    TASK_CANCELLED = "TASK_CANCELLED"

    # 资源管理
    MEMORY_WARNING = "MEMORY_WARNING"
    MEMORY_CRITICAL = "MEMORY_CRITICAL"
    DISK_SPACE_WARNING = "DISK_SPACE_WARNING"

    # 配置相关
    CONFIGURATION_CHANGED = "CONFIGURATION_CHANGED"
    CONFIGURATION_LOADED = "CONFIGURATION_LOADED"


def create_ui_event(event_type: UIEventType, source: str,
                   data: Optional[Dict[str, Any]] = None) -> EventData:
    """创建UI事件"""
    return EventData.create(event_type.value, source, data)


def create_business_event(event_type: BusinessEventType, source: str,
                         data: Optional[Dict[str, Any]] = None) -> EventData:
    """创建业务事件"""
    return EventData.create(event_type.value, source, data)


def create_system_event(event_type: SystemEventType, source: str,
                       data: Optional[Dict[str, Any]] = None,
                       priority: EventPriority = EventPriority.NORMAL) -> EventData:
    """创建系统事件"""
    return EventData.create(event_type.value, source, data, priority)
