#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
白名单面板模块

该模块负责创建和管理白名单面板, 包括:
1. 白名单规则的创建和管理
2. 查找白名单文件
3. 处理白名单文件

作者: AI助手
日期: 2023-06-01
版本: 1.0.0
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, messagebox, filedialog, simpledialog
import threading
import queue
import time
from pathlib import Path
import re
from typing import Any

# 导入自定义模块
from src.core.dependency_injection import resolve
from src.utils.logger import get_logger
from src.utils.format_utils import format_size, _normalize_path
from src.ui.interfaces import IPanel


class WhitelistPanel(IPanel):
    """
    白名单面板类
    
    创建和管理白名单面板
    """
    
    def __init__(self, parent, main_window, logger: Any = None):
        """
        初始化白名单面板
        
        参数:
            parent: 父容器
            main_window: 主窗口实例
            logger: 日志记录器，可选，如未提供则自动创建
        """
        self.parent = parent
        self.main_window = main_window
        # 如果没有提供logger，则自动创建一个
        if logger is None:
            self.logger = get_logger("WhitelistPanel")
        else:
            self.logger = logger
        self.root_frame = ttk.Frame(parent)
        
        # 创建选项卡控件
        self.tab_control = ttk.Notebook(self.root_frame)
        self.tab_control.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建规则管理选项卡
        self.rules_tab = ttk.Frame(self.tab_control)
        self.tab_control.add(self.rules_tab, text="规则管理")
        
        # 创建类型管理选项卡
        self.types_tab = ttk.Frame(self.tab_control)
        self.tab_control.add(self.types_tab, text="类型管理")
        
        # 创建风格管理选项卡
        self.style_tab = ttk.Frame(self.tab_control)
        self.tab_control.add(self.style_tab, text="风格管理")
        
        # 初始化规则管理选项卡内容
        self.init_rules_tab()
        
        # 初始化类型管理选项卡内容
        self.init_types_tab()
        
        # 初始化风格管理选项卡内容
        self.init_style_tab()
        
        # 初始化数据
        self.whitelist_rules = []
        self.whitelist_files = []
        self.folder_types = {}
        self.style_tags = {}  # 风格tag数据
        
        # 加载规则和类型配置
        self.load_rules()
    
    def init_rules_tab(self):
        """
        初始化规则管理选项卡内容
        """
        # 规则分组
        rules_group = ttk.LabelFrame(self.rules_tab, text="白名单规则")
        rules_group.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 目标目录框架
        target_dir_frame = ttk.Frame(rules_group)
        target_dir_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Label(target_dir_frame, text="目标目录:").pack(side=tk.LEFT, padx=(0, 5))
        self.target_dir_var = tk.StringVar()
        target_dir_entry = ttk.Entry(target_dir_frame, textvariable=self.target_dir_var)
        target_dir_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        browse_btn = ttk.Button(target_dir_frame, text="浏览...", command=self.browse_target_dir)
        browse_btn.pack(side=tk.LEFT)
        
        # 规则列表框架
        rules_list_frame = ttk.Frame(rules_group)
        rules_list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        rules_list_container = ttk.Frame(rules_list_frame)
        rules_list_container.pack(fill=tk.BOTH, expand=True)
        self.rules_listbox = tk.Listbox(rules_list_container, selectmode=tk.SINGLE)
        self.rules_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        rules_scrollbar = ttk.Scrollbar(rules_list_container, orient=tk.VERTICAL, command=self.rules_listbox.yview)
        rules_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.rules_listbox.configure(yscrollcommand=rules_scrollbar.set)
        self.rules_listbox.bind("<<ListboxSelect>>", self.on_rule_selected)
        
        # 规则编辑框架
        rule_edit_frame = ttk.LabelFrame(rules_group, text="规则编辑")
        rule_edit_frame.pack(fill=tk.BOTH, expand=False, padx=5, pady=5)
        rule_name_frame = ttk.Frame(rule_edit_frame)
        rule_name_frame.pack(fill=tk.X, pady=5)
        ttk.Label(rule_name_frame, text="规则名称:").pack(side=tk.LEFT, padx=(0, 5))
        self.rule_name_var = tk.StringVar()
        rule_name_entry = ttk.Entry(rule_name_frame, textvariable=self.rule_name_var)
        rule_name_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        rule_type_frame = ttk.Frame(rule_edit_frame)
        rule_type_frame.pack(fill=tk.X, pady=5)
        ttk.Label(rule_type_frame, text="规则类型:").pack(side=tk.LEFT, padx=(0, 5))
        self.rule_type_var = tk.StringVar(value="扩展名")
        rule_type_combo = ttk.Combobox(rule_type_frame, textvariable=self.rule_type_var, state="readonly")
        rule_type_combo["values"] = ("扩展名", "文件名", "正则表达式", "文件大小", "修改时间")
        rule_type_combo.pack(side=tk.LEFT, fill=tk.X, expand=True)
        rule_type_combo.bind("<<ComboboxSelected>>", self.on_rule_type_changed)
        self.rule_params_frame = ttk.Frame(rule_edit_frame)
        self.rule_params_frame.pack(fill=tk.X, pady=5)
        self.extension_frame = ttk.Frame(self.rule_params_frame)
        ttk.Label(self.extension_frame, text="扩展名 (用逗号分隔):").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        self.extension_var = tk.StringVar()
        ttk.Entry(self.extension_frame, textvariable=self.extension_var).grid(row=0, column=1, padx=5, pady=5, sticky="we")
        self.extension_frame.columnconfigure(1, weight=1)
        self.filename_frame = ttk.Frame(self.rule_params_frame)
        ttk.Label(self.filename_frame, text="文件名 (用逗号分隔):").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        self.filename_var = tk.StringVar()
        ttk.Entry(self.filename_frame, textvariable=self.filename_var).grid(row=0, column=1, padx=5, pady=5, sticky="we")
        self.filename_frame.columnconfigure(1, weight=1)
        self.regex_frame = ttk.Frame(self.rule_params_frame)
        ttk.Label(self.regex_frame, text="正则表达式:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        self.regex_var = tk.StringVar()
        ttk.Entry(self.regex_frame, textvariable=self.regex_var).grid(row=0, column=1, padx=5, pady=5, sticky="we")
        self.regex_frame.columnconfigure(1, weight=1)
        self.size_frame = ttk.Frame(self.rule_params_frame)
        ttk.Label(self.size_frame, text="比较类型:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        self.size_compare_var = tk.StringVar(value="大于")
        size_compare_combo = ttk.Combobox(self.size_frame, textvariable=self.size_compare_var, state="readonly")
        size_compare_combo["values"] = ("大于", "小于", "等于")
        size_compare_combo.grid(row=0, column=1, padx=5, pady=5, sticky="we")
        ttk.Label(self.size_frame, text="大小 (KB):").grid(row=1, column=0, padx=5, pady=5, sticky="w")
        self.size_value_var = tk.StringVar()
        ttk.Entry(self.size_frame, textvariable=self.size_value_var).grid(row=1, column=1, padx=5, pady=5, sticky="we")
        self.size_frame.columnconfigure(1, weight=1)
        self.time_frame = ttk.Frame(self.rule_params_frame)
        ttk.Label(self.time_frame, text="比较类型:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        self.time_compare_var = tk.StringVar(value="早于")
        time_compare_combo = ttk.Combobox(self.time_frame, textvariable=self.time_compare_var, state="readonly")
        time_compare_combo["values"] = ("早于", "晚于")
        time_compare_combo.grid(row=0, column=1, padx=5, pady=5, sticky="we")
        ttk.Label(self.time_frame, text="天数:").grid(row=1, column=0, padx=5, pady=5, sticky="w")
        self.time_value_var = tk.StringVar()
        ttk.Entry(self.time_frame, textvariable=self.time_value_var).grid(row=1, column=1, padx=5, pady=5, sticky="we")
        self.time_frame.columnconfigure(1, weight=1)
        self.extension_frame.pack(fill=tk.X)
        rule_buttons_frame = ttk.Frame(rule_edit_frame)
        rule_buttons_frame.pack(fill=tk.X, pady=5)
        self.add_rule_btn = ttk.Button(rule_buttons_frame, text="添加规则", command=self.add_rule)
        self.add_rule_btn.pack(side=tk.LEFT, padx=(0, 5))
        self.save_rule_btn = ttk.Button(rule_buttons_frame, text="保存规则", command=self.save_rule)
        self.save_rule_btn.pack(side=tk.LEFT, padx=5)

        # 添加查找白名单文件按钮
        self.find_whitelist_btn = ttk.Button(rule_buttons_frame, text="查找白名单文件", command=self.find_whitelist_files)
        self.find_whitelist_btn.pack(side=tk.LEFT, padx=5)
        
        # 结果区
        self.result_frame = ttk.LabelFrame(rules_group, text="白名单文件")
        self.result_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        # 结果树形视图
        result_tree_frame = ttk.Frame(self.result_frame)
        result_tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        # 结果树形视图，调整columns顺序为：name, whitelist_type, type, path, size, rule
        self.result_tree = ttk.Treeview(result_tree_frame, columns=("name", "whitelist_type", "type", "path", "size", "rule"), show="headings")
        self.result_tree.column("name", width=150, anchor="w")
        self.result_tree.column("whitelist_type", width=100, anchor="w")
        self.result_tree.column("type", width=100, anchor="w")
        self.result_tree.column("path", width=250, anchor="w")
        self.result_tree.column("size", width=100, anchor="e")
        self.result_tree.column("rule", width=150, anchor="w")
        self.result_tree.heading("name", text="文件名")
        self.result_tree.heading("whitelist_type", text="白名单类型")
        self.result_tree.heading("type", text="类型")
        self.result_tree.heading("path", text="路径")
        self.result_tree.heading("size", text="大小")
        self.result_tree.heading("rule", text="匹配规则")
        vsb = ttk.Scrollbar(result_tree_frame, orient="vertical", command=self.result_tree.yview)
        hsb = ttk.Scrollbar(result_tree_frame, orient="horizontal", command=self.result_tree.xview)
        self.result_tree.configure(yscrollcommand=vsb.set, xscrollcommand=hsb.set)
        vsb.pack(side=tk.RIGHT, fill=tk.Y)
        hsb.pack(side=tk.BOTTOM, fill=tk.X)
        self.result_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 结果操作按钮
        result_buttons_frame = ttk.Frame(self.result_frame)
        result_buttons_frame.pack(fill=tk.X, padx=5, pady=5)
        self.select_all_btn = ttk.Button(result_buttons_frame, text="全选", command=self.select_all)
        self.select_all_btn.pack(side=tk.LEFT, padx=(0, 5))
        self.deselect_all_btn = ttk.Button(result_buttons_frame, text="取消全选", command=self.deselect_all)
        self.deselect_all_btn.pack(side=tk.LEFT, padx=5)
        self.move_to_whitelist_btn = ttk.Button(result_buttons_frame, text="移动到白名单", command=self.move_to_whitelist)
        self.move_to_whitelist_btn.pack(side=tk.LEFT, padx=5)
        self.clear_results_btn = ttk.Button(result_buttons_frame, text="清除结果", command=self.clear_results)
        self.clear_results_btn.pack(side=tk.LEFT, padx=5)
        
    def init_types_tab(self):
        """
        初始化类型管理选项卡内容
        """
        # 类型管理框架
        types_group = ttk.LabelFrame(self.types_tab, text="文件夹类型管理")
        types_group.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 类型列表框架
        types_list_frame = ttk.Frame(types_group)
        types_list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 左侧类型列表
        left_frame = ttk.Frame(types_list_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        ttk.Label(left_frame, text="可用类型:").pack(fill=tk.X, pady=(0, 5))
        
        types_list_container = ttk.Frame(left_frame)
        types_list_container.pack(fill=tk.BOTH, expand=True)
        
        self.types_listbox = tk.Listbox(types_list_container, selectmode=tk.SINGLE)
        self.types_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        types_scrollbar = ttk.Scrollbar(types_list_container, orient=tk.VERTICAL, command=self.types_listbox.yview)
        types_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.types_listbox.configure(yscrollcommand=types_scrollbar.set)
        self.types_listbox.bind("<<ListboxSelect>>", self.on_type_selected)
        
        # 右侧类型详情
        right_frame = ttk.Frame(types_list_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        # 类型详情框架
        type_details_frame = ttk.LabelFrame(right_frame, text="类型详情")
        type_details_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 类型名称
        type_name_frame = ttk.Frame(type_details_frame)
        type_name_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(type_name_frame, text="类型名称:").pack(side=tk.LEFT, padx=(0, 5))
        
        self.type_name_var = tk.StringVar()
        type_name_entry = ttk.Entry(type_name_frame, textvariable=self.type_name_var)
        type_name_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # 类型启用状态
        type_enabled_frame = ttk.Frame(type_details_frame)
        type_enabled_frame.pack(fill=tk.X, pady=5)
        
        self.type_enabled_var = tk.BooleanVar(value=True)
        type_enabled_check = ttk.Checkbutton(type_enabled_frame, text="启用", variable=self.type_enabled_var)
        type_enabled_check.pack(side=tk.LEFT, padx=(0, 5))
        
        # 类型策略
        type_strategy_frame = ttk.Frame(type_details_frame)
        type_strategy_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(type_strategy_frame, text="处理策略:").pack(side=tk.LEFT, padx=(0, 5))
        
        self.type_strategy_var = tk.StringVar(value="normal")
        type_strategy_combo = ttk.Combobox(type_strategy_frame, textvariable=self.type_strategy_var, state="readonly")
        type_strategy_combo["values"] = ("normal", "skip", "readonly", "special")
        type_strategy_combo.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # 文件夹列表
        folders_frame = ttk.LabelFrame(type_details_frame, text="关联文件夹")
        folders_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        folders_list_container = ttk.Frame(folders_frame)
        folders_list_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.folders_listbox = tk.Listbox(folders_list_container)
        self.folders_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        folders_scrollbar = ttk.Scrollbar(folders_list_container, orient=tk.VERTICAL, command=self.folders_listbox.yview)
        folders_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.folders_listbox.configure(yscrollcommand=folders_scrollbar.set)
        
        # 文件夹操作按钮
        folders_buttons_frame = ttk.Frame(folders_frame)
        folders_buttons_frame.pack(fill=tk.X, pady=5)
        
        self.add_folder_btn = ttk.Button(folders_buttons_frame, text="添加文件夹", command=self.add_folder)
        self.add_folder_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.remove_folder_btn = ttk.Button(folders_buttons_frame, text="移除文件夹", command=self.remove_folder)
        self.remove_folder_btn.pack(side=tk.LEFT, padx=5)
        
        # 类型操作按钮
        type_buttons_frame = ttk.Frame(type_details_frame)
        type_buttons_frame.pack(fill=tk.X, pady=5)
        
        self.add_type_btn = ttk.Button(type_buttons_frame, text="添加类型", command=self.add_type)
        self.add_type_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.save_type_btn = ttk.Button(type_buttons_frame, text="保存类型", command=self.save_type)
        self.save_type_btn.pack(side=tk.LEFT, padx=5)
        
        self.delete_type_btn = ttk.Button(type_buttons_frame, text="删除类型", command=self.delete_type)
        self.delete_type_btn.pack(side=tk.LEFT, padx=5)
    
    def init_style_tab(self):
        """
        初始化风格管理选项卡内容
        """
        # 风格管理框架
        style_group = ttk.LabelFrame(self.style_tab, text="风格管理")
        style_group.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 风格列表框架
        style_list_frame = ttk.Frame(style_group)
        style_list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 左侧风格列表
        left_frame = ttk.Frame(style_list_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        ttk.Label(left_frame, text="可用风格:").pack(fill=tk.X, pady=(0, 5))
        
        style_list_container = ttk.Frame(left_frame)
        style_list_container.pack(fill=tk.BOTH, expand=True)
        
        self.style_listbox = tk.Listbox(style_list_container, selectmode=tk.SINGLE)
        self.style_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        style_scrollbar = ttk.Scrollbar(style_list_container, orient=tk.VERTICAL, command=self.style_listbox.yview)
        style_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.style_listbox.configure(yscrollcommand=style_scrollbar.set)
        self.style_listbox.bind("<<ListboxSelect>>", self.on_style_selected)
        
        # 右侧风格详情
        right_frame = ttk.Frame(style_list_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        # 风格详情框架
        style_details_frame = ttk.LabelFrame(right_frame, text="风格详情")
        style_details_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 风格名称
        style_name_frame = ttk.Frame(style_details_frame)
        style_name_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(style_name_frame, text="风格名称:").pack(side=tk.LEFT, padx=(0, 5))
        
        self.style_name_var = tk.StringVar()
        style_name_entry = ttk.Entry(style_name_frame, textvariable=self.style_name_var)
        style_name_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # 风格启用状态
        style_enabled_frame = ttk.Frame(style_details_frame)
        style_enabled_frame.pack(fill=tk.X, pady=5)
        
        self.style_enabled_var = tk.BooleanVar(value=True)
        style_enabled_check = ttk.Checkbutton(style_enabled_frame, text="启用", variable=self.style_enabled_var)
        style_enabled_check.pack(side=tk.LEFT, padx=(0, 5))
        
        # 风格描述
        style_description_frame = ttk.Frame(style_details_frame)
        style_description_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(style_description_frame, text="风格描述:").pack(side=tk.LEFT, padx=(0, 5))
        
        self.style_description_var = tk.StringVar()
        style_description_entry = ttk.Entry(style_description_frame, textvariable=self.style_description_var)
        style_description_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # 文件夹列表
        folders_frame = ttk.LabelFrame(style_details_frame, text="关联文件夹")
        folders_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        folders_list_container = ttk.Frame(folders_frame)
        folders_list_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.style_folders_listbox = tk.Listbox(folders_list_container)
        self.style_folders_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        folders_scrollbar = ttk.Scrollbar(folders_list_container, orient=tk.VERTICAL, command=self.style_folders_listbox.yview)
        folders_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.style_folders_listbox.configure(yscrollcommand=folders_scrollbar.set)
        
        # 文件夹操作按钮
        folders_buttons_frame = ttk.Frame(folders_frame)
        folders_buttons_frame.pack(fill=tk.X, pady=5)
        
        self.add_style_folder_btn = ttk.Button(folders_buttons_frame, text="添加文件夹", command=self.add_style_folder)
        self.add_style_folder_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.remove_style_folder_btn = ttk.Button(folders_buttons_frame, text="移除文件夹", command=self.remove_style_folder)
        self.remove_style_folder_btn.pack(side=tk.LEFT, padx=5)
        
        # 风格操作按钮
        style_buttons_frame = ttk.Frame(style_details_frame)
        style_buttons_frame.pack(fill=tk.X, pady=5)
        
        self.add_style_btn = ttk.Button(style_buttons_frame, text="添加风格", command=self.add_style)
        self.add_style_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.save_style_btn = ttk.Button(style_buttons_frame, text="保存风格", command=self.save_style)
        self.save_style_btn.pack(side=tk.LEFT, padx=5)
        
        self.delete_style_btn = ttk.Button(style_buttons_frame, text="删除风格", command=self.delete_style)
        self.delete_style_btn.pack(side=tk.LEFT, padx=5)
    
    def apply_theme(self, theme):
        pass
    def get_current_theme(self):
        return {}
    def load_data(self, data):
        pass
    def clear_data(self):
        pass
    def get_selected_items(self):
        return []
    def refresh(self):
        pass
    def update_duplicate_groups(self, data):
        pass
    def set_find_button_state(self, enabled: bool, text=None):
        pass
    def get_frame(self):
        return self.root_frame
    
    def create_widgets(self):
        pass
    def update_ui(self, data):
        pass

    def destroy(self):
        pass

    def browse_target_dir(self):
        """
        浏览白名单目标目录
        """
        # 选择目录
        target_dir = filedialog.askdirectory(title="选择白名单目标目录")
        if target_dir:
            self.target_dir_var.set(target_dir)
            self.target_dir = target_dir
    
    def on_rule_type_changed(self, event=None):
        """
        处理规则类型变更事件
        
        参数:
            event: 事件对象
        """
        # 获取当前规则类型
        rule_type = self.rule_type_var.get()
        
        # 隐藏所有规则参数框架
        for frame in [self.extension_frame, self.filename_frame, self.regex_frame, self.size_frame, self.time_frame]:
            frame.pack_forget()
        
        # 显示对应的规则参数框架
        if rule_type == "扩展名":
            self.extension_frame.pack(fill=tk.X)
        elif rule_type == "文件名":
            self.filename_frame.pack(fill=tk.X)
        elif rule_type == "正则表达式":
            self.regex_frame.pack(fill=tk.X)
        elif rule_type == "文件大小":
            self.size_frame.pack(fill=tk.X)
        elif rule_type == "修改时间":
            self.time_frame.pack(fill=tk.X)
    
    def on_rule_selected(self, event=None):
        """
        处理规则选择事件
        
        参数:
            event: 事件对象
        """
        # 获取选中的规则索引
        selected = self.rules_listbox.curselection()
        if not selected:
            return
        
        # 获取规则
        rule_index = selected[0]
        if rule_index < 0 or rule_index >= len(self.whitelist_rules):
            return
        
        rule = self.whitelist_rules[rule_index]
        
        # 更新规则编辑框
        self.rule_name_var.set(rule.get("name", ""))
        self.rule_type_var.set(rule.get("type", "扩展名"))
        
        # 更新规则参数
        params = rule.get("params", {})
        
        # 清除所有参数
        self.extension_var.set("")
        self.filename_var.set("")
        self.regex_var.set("")
        self.size_compare_var.set("大于")
        self.size_value_var.set("")
        self.time_compare_var.set("早于")
        self.time_value_var.set("")
        
        # 设置对应的参数
        rule_type = rule.get("type", "扩展名")
        if rule_type == "扩展名":
            extensions = params.get("extensions", [])
            self.extension_var.set(", ".join(extensions))
        elif rule_type == "文件名":
            filenames = params.get("filenames", [])
            self.filename_var.set(", ".join(filenames))
        elif rule_type == "正则表达式":
            self.regex_var.set(params.get("regex", ""))
        elif rule_type == "文件大小":
            self.size_compare_var.set(params.get("compare", "大于"))
            self.size_value_var.set(str(params.get("value", "")))
        elif rule_type == "修改时间":
            self.time_compare_var.set(params.get("compare", "早于"))
            self.time_value_var.set(str(params.get("days", "")))
        
        # 更新规则参数框架
        self.on_rule_type_changed()
    
    def add_rule(self):
        """
        添加规则
        """
        # 清除规则编辑框
        self.clear_rule()
        
        # 添加默认规则名称
        self.rule_name_var.set(f"规则 {len(self.whitelist_rules) + 1}")
    
    def save_rule(self):
        """
        保存规则
        """
        # 获取规则名称
        rule_name = self.rule_name_var.get()
        if not rule_name:
            messagebox.showwarning("警告", "规则名称不能为空")
            return
        
        # 获取规则类型
        rule_type = self.rule_type_var.get()
        
        # 获取规则参数
        params = {}
        if rule_type == "扩展名":
            extensions_str = self.extension_var.get().strip()
            if not extensions_str:
                messagebox.showwarning("警告", "扩展名不能为空")
                return
            
            # 解析扩展名
            extensions = [ext.strip().lower() for ext in extensions_str.split(",")]
            extensions = ["." + ext if not ext.startswith(".") else ext for ext in extensions if ext]
            
            if not extensions:
                messagebox.showwarning("警告", "无效的扩展名")
                return
            
            params = {"extensions": extensions}
        elif rule_type == "文件名":
            filenames_str = self.filename_var.get().strip()
            if not filenames_str:
                messagebox.showwarning("警告", "文件名不能为空")
                return
            
            # 解析文件名
            filenames = [name.strip() for name in filenames_str.split(",")]
            filenames = [name for name in filenames if name]
            
            if not filenames:
                messagebox.showwarning("警告", "无效的文件名")
                return
            
            params = {"filenames": filenames}
        elif rule_type == "正则表达式":
            regex = self.regex_var.get().strip()
            if not regex:
                messagebox.showwarning("警告", "正则表达式不能为空")
                return
            
            # 验证正则表达式
            try:
                re.compile(regex)
            except re.error:
                messagebox.showerror("错误", "无效的正则表达式")
                return
            
            params = {"regex": regex}
        elif rule_type == "文件大小":
            try:
                size_value = int(self.size_value_var.get())
                if size_value < 0:
                    raise ValueError("文件大小必须为非负整数")
            except ValueError as e:
                messagebox.showerror("错误", f"无效的文件大小: {str(e)}")
                return
            
            size_compare = self.size_compare_var.get()
            params = {"compare": size_compare, "value": size_value}
        elif rule_type == "修改时间":
            try:
                days = int(self.time_value_var.get())
                if days < 0:
                    raise ValueError("天数必须为非负整数")
            except ValueError as e:
                messagebox.showerror("错误", f"无效的天数: {str(e)}")
                return
            
            time_compare = self.time_compare_var.get()
            params = {"compare": time_compare, "days": days}
        
        # 创建规则
        rule = {"name": rule_name, "type": rule_type, "params": params}
        
        # 获取选中的规则索引
        selected = self.rules_listbox.curselection()
        if selected:
            # 更新现有规则
            rule_index = selected[0]
            if rule_index >= 0 and rule_index < len(self.whitelist_rules):
                self.whitelist_rules[rule_index] = rule
        else:
            # 添加新规则
            self.whitelist_rules.append(rule)
        
        # 更新规则列表
        self.update_rules_list()
        
        # 保存规则到文件
        self.main_window.rule_engine.save_whitelist_rules(self.whitelist_rules)
        
        # 显示提示
        self.main_window.log_message(f"已保存白名单规则: {rule_name}", "success")
    
    def delete_rule(self):
        """
        删除规则
        """
        # 获取选中的规则索引
        selected = self.rules_listbox.curselection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个规则")
            return
        
        # 获取规则索引
        rule_index = selected[0]
        if rule_index < 0 or rule_index >= len(self.whitelist_rules):
            return
        
        # 确认删除
        rule_name = self.whitelist_rules[rule_index].get("name", "")
        if not messagebox.askyesno("确认删除", f"确定要删除规则 {rule_name} 吗？"):
            return
        
        # 删除规则
        del self.whitelist_rules[rule_index]
        
        # 更新规则列表
        self.update_rules_list()
        
        # 清除规则编辑框
        self.clear_rule()
        
        # 保存规则到文件
        self.main_window.rule_engine.save_whitelist_rules(self.whitelist_rules)
        
        # 显示提示
        self.main_window.log_message(f"已删除白名单规则: {rule_name}", "success")
    
    def clear_rule(self):
        """
        清除规则编辑框
        """
        # 清除规则名称和类型
        self.rule_name_var.set("")
        self.rule_type_var.set("扩展名")
        
        # 清除规则参数
        self.extension_var.set("")
        self.filename_var.set("")
        self.regex_var.set("")
        self.size_compare_var.set("大于")
        self.size_value_var.set("")
        self.time_compare_var.set("早于")
        self.time_value_var.set("")
        
        # 更新规则参数框架
        self.on_rule_type_changed()
        
        # 清除选择
        self.rules_listbox.selection_clear(0, tk.END)
    
    def update_rules_list(self):
        """
        更新规则列表
        """
        # 清空列表
        self.rules_listbox.delete(0, tk.END)
        
        # 添加规则
        for rule in self.whitelist_rules:
            self.rules_listbox.insert(tk.END, rule.get("name", ""))
    
    def load_rules(self):
        """
        加载规则
        """
        # 从规则引擎加载规则
        self.whitelist_rules = self.main_window.rule_engine.get_whitelist_rules()
        
        # 更新规则列表
        self.update_rules_list()
        
        # 加载目标目录
        self.target_dir = self.main_window.rule_engine.get_whitelist_target_dir()
        self.target_dir_var.set(self.target_dir)
        
        # 加载文件夹类型配置
        self.load_folder_types()
    
    def save_settings(self):
        """
        保存设置
        """
        # 获取目标目录
        target_dir = self.target_dir_var.get().strip()
        if not target_dir:
            messagebox.showwarning("警告", "请先选择白名单目标目录")
            return
        
        # 检查目录是否存在
        if not os.path.isdir(target_dir):
            if messagebox.askyesno("目录不存在", f"目录 {target_dir} 不存在，是否创建？"):
                try:
                    os.makedirs(target_dir, exist_ok=True)
                except Exception as e:
                    messagebox.showerror("错误", f"无法创建目录: {str(e)}")
                    return
            else:
                return
        
        # 保存目标目录
        self.target_dir = target_dir
        self.main_window.rule_engine.save_whitelist_target_dir(target_dir)
        
        # 显示提示
        self.main_window.log_message(f"已保存白名单设置，目标目录: {target_dir}", "success")
    
    def find_whitelist_files(self):
        """
        查找白名单文件
        """
        # 检查是否有规则
        if not self.whitelist_rules:
            messagebox.showwarning("警告", "请先添加白名单规则")
            return
        
        # 添加任务到队列
        self.main_window.task_queue.put({
            "type": "find_whitelist_files",
            "data": {"rules": self.whitelist_rules}
        })
        
        # 清除结果
        self.clear_results()
        
        # 更新状态
        self.main_window.status_text.set("正在查找白名单文件...")
        self.main_window.progress_var.set(0)
        self.main_window.task_label.set("查找白名单文件")
        self.main_window.subtask_label.set("")
    
    def clear_results(self):
        """
        清除结果
        """
        # 清空结果树形视图
        for item in self.result_tree.get_children():
            self.result_tree.delete(item)
        
        # 清空数据
        self.whitelist_files = []
        
        # 更新状态
        self.main_window.status_text.set("就绪")
        self.main_window.progress_var.set(0)
        self.main_window.task_label.set("就绪")
        self.main_window.subtask_label.set("")
    
    def update_whitelist_files(self, data):
        """
        更新白名单文件列表
        
        参数:
            data (dict): 白名单文件数据
        """
        if not data:
            return
        
        # 获取白名单文件
        self.whitelist_files = data.get("whitelist_files", [])
        
        # 清空结果树形视图
        for item in self.result_tree.get_children():
            self.result_tree.delete(item)
        
        # 更新结果树形视图
        for file_info in self.whitelist_files:
            file_path = file_info.get("path", "")
            file_name = os.path.basename(file_path)
            file_size = file_info.get("size", 0)
            file_size_str = format_size(file_size)
            file_type = file_info.get("type", "")
            rule_name = file_info.get("rule", "")
            whitelist_type = file_info.get("whitelist_type", "")
            
            self.result_tree.insert(
                "", "end",
                values=(file_name, whitelist_type, file_type, file_path, file_size_str, rule_name)
            )
        
        # 更新状态
        self.main_window.status_text.set(f"找到 {len(self.whitelist_files)} 个白名单文件")
        self.main_window.progress_var.set(100)
        self.main_window.task_label.set("查找完成")
        self.main_window.subtask_label.set("")
        
        # 显示提示
        if self.whitelist_files:
            total_size = sum(file.get("size", 0) for file in self.whitelist_files)
            total_size_str = format_size(total_size)
            self.main_window.log_message(f"找到 {len(self.whitelist_files)} 个白名单文件，总大小 {total_size_str}", "success")
        else:
            self.main_window.log_message("没有找到白名单文件", "info")
    
    def select_all(self):
        """
        全选
        """
        # 选中所有项
        for item in self.result_tree.get_children():
            self.result_tree.selection_add(item)
    
    def deselect_all(self):
        """
        取消全选
        """
        # 取消选中所有项
        self.result_tree.selection_remove(self.result_tree.get_children())
    
    def move_to_whitelist(self):
        """
        移动到白名单
        """
        # 获取选中的文件
        selected = self.result_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个文件")
            return
        
        # 获取文件路径
        file_paths = []
        for item in selected:
            values = self.result_tree.item(item, "values")
            if values and len(values) > 1:
                file_paths.append(values[1])
        
        # 确认操作
        if not messagebox.askyesno("确认", f"确定要将选中的 {len(file_paths)} 个文件移动到白名单吗？"):
            return
        
        # 移动到白名单
        for file_path in file_paths:
            # 更新数据库
            if self.main_window.db_manager:
                self.main_window.db_manager.update_file_info(file_path, {"is_whitelist": True})
            
            # 更新文件信息
            for file_info in self.whitelist_files:
                if file_info.get("path") == file_path:
                    file_info["is_whitelist"] = True
        
        # 显示提示
        self.main_window.log_message(f"已将 {len(file_paths)} 个文件移动到白名单", "success")
    
    def on_type_selected(self, event=None):
        """
        处理类型选择事件
        
        参数:
            event: 事件对象
        """
        # 获取选中的类型
        selected = self.types_listbox.curselection()
        if not selected:
            return
        
        # 获取类型名称
        type_name = self.types_listbox.get(selected[0])
        
        # 从文件夹类型配置中获取类型信息
        if not hasattr(self, 'folder_types') or not isinstance(self.folder_types, dict):
            self.folder_types = {}
            
        type_info = self.folder_types.get(type_name, {})
        
        # 确保type_info是字典类型
        if not isinstance(type_info, dict):
            type_info = {}
        
        # 更新类型详情
        self.type_name_var.set(type_name)
        self.type_enabled_var.set(type_info.get('enabled', True))
        self.type_strategy_var.set(type_info.get('strategy', 'normal'))
        
        # 更新文件夹列表
        self.folders_listbox.delete(0, tk.END)
        folders = type_info.get('folders', [])
        if isinstance(folders, list):
            for folder in folders:
                self.folders_listbox.insert(tk.END, folder)
    
    def add_folder(self):
        """
        添加文件夹到当前类型
        """
        # 获取当前类型名称
        type_name = self.type_name_var.get()
        if not type_name:
            messagebox.showwarning("警告", "请先选择或输入一个类型名称")
            return
        
        # 选择文件夹
        folder_path = filedialog.askdirectory(title="选择要添加的文件夹")
        if not folder_path:
            return
        
        # 标准化路径
        folder_path = _normalize_path(folder_path)
        
        # 检查是否已存在
        for i in range(self.folders_listbox.size()):
            if self.folders_listbox.get(i) == folder_path:
                messagebox.showwarning("警告", f"文件夹 {folder_path} 已存在")
                return
        
        # 添加到列表
        self.folders_listbox.insert(tk.END, folder_path)
        
        # 更新类型信息
        self.update_type_info()
    
    def remove_folder(self):
        """
        从当前类型中移除文件夹
        """
        # 获取选中的文件夹
        selected = self.folders_listbox.curselection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个文件夹")
            return
        
        # 获取文件夹路径
        folder_path = self.folders_listbox.get(selected[0])
        
        # 确认移除
        if not messagebox.askyesno("确认", f"确定要移除文件夹 {folder_path} 吗？"):
            return
        
        # 从列表中移除
        self.folders_listbox.delete(selected[0])
        
        # 更新类型信息
        self.update_type_info()
    
    def add_type(self):
        """
        添加新类型
        """
        # 清空类型详情
        self.type_name_var.set("")
        self.type_enabled_var.set(True)
        self.type_strategy_var.set("normal")
        self.folders_listbox.delete(0, tk.END)
        
        # 获取新类型名称
        new_type = simpledialog.askstring("添加类型", "请输入新类型名称:")
        if not new_type:
            return
        
        # 检查是否已存在
        for i in range(self.types_listbox.size()):
            if self.types_listbox.get(i) == new_type:
                messagebox.showwarning("警告", f"类型 {new_type} 已存在")
                return
        
        # 添加到列表
        self.types_listbox.insert(tk.END, new_type)
        
        # 选中新类型
        self.types_listbox.selection_clear(0, tk.END)
        self.types_listbox.selection_set(tk.END)
        
        # 设置类型名称
        self.type_name_var.set(new_type)
        
        # 更新类型信息
        self.update_type_info()
    
    def save_type(self):
        """
        保存当前类型信息
        """
        # 获取类型名称
        type_name = self.type_name_var.get()
        if not type_name:
            messagebox.showwarning("警告", "请输入类型名称")
            return
        
        # 获取类型信息
        type_info = {
            'enabled': self.type_enabled_var.get(),
            'strategy': self.type_strategy_var.get(),
            'folders': []
        }
        
        # 获取文件夹列表
        for i in range(self.folders_listbox.size()):
            type_info['folders'].append(self.folders_listbox.get(i))
        
        # 更新类型信息
        if not hasattr(self, 'folder_types') or not isinstance(self.folder_types, dict):
            self.folder_types = {}
        
        # 检查是否需要重命名
        selected = self.types_listbox.curselection()
        if selected:
            old_name = self.types_listbox.get(selected[0])
            if old_name != type_name:
                # 需要重命名
                if type_name in self.folder_types:
                    # 新名称已存在
                    messagebox.showwarning("警告", f"类型 {type_name} 已存在")
                    return
                
                # 删除旧名称
                if old_name in self.folder_types:
                    del self.folder_types[old_name]
                
                # 更新列表项
                self.types_listbox.delete(selected[0])
                self.types_listbox.insert(selected[0], type_name)
                self.types_listbox.selection_set(selected[0])
        
        # 保存类型信息
        self.folder_types[type_name] = type_info
        
        # 保存到文件
        self.save_folder_types()
        
        # 显示提示
        self.main_window.log_message(f"已保存类型 {type_name}", "success")
    
    def delete_type(self):
        """
        删除当前类型
        """
        # 获取选中的类型
        selected = self.types_listbox.curselection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个类型")
            return
        
        # 获取类型名称
        type_name = self.types_listbox.get(selected[0])
        
        # 确认删除
        if not messagebox.askyesno("确认", f"确定要删除类型 {type_name} 吗？"):
            return
        
        # 从列表中删除
        self.types_listbox.delete(selected[0])
        
        # 从类型信息中删除
        if hasattr(self, 'folder_types') and isinstance(self.folder_types, dict) and type_name in self.folder_types:
            del self.folder_types[type_name]
        
        # 清空类型详情
        self.type_name_var.set("")
        self.type_enabled_var.set(True)
        self.type_strategy_var.set("normal")
        self.folders_listbox.delete(0, tk.END)
        
        # 保存到文件
        self.save_folder_types()
        
        # 显示提示
        self.main_window.log_message(f"已删除类型 {type_name}", "success")
    
    def update_type_info(self):
        """
        更新当前类型信息
        """
        # 获取类型名称
        type_name = self.type_name_var.get()
        if not type_name:
            return
        
        # 获取类型信息
        type_info = {
            'enabled': self.type_enabled_var.get(),
            'strategy': self.type_strategy_var.get(),
            'folders': []
        }
        
        # 获取文件夹列表
        for i in range(self.folders_listbox.size()):
            type_info['folders'].append(self.folders_listbox.get(i))
        
        # 更新类型信息
        if not hasattr(self, 'folder_types') or not isinstance(self.folder_types, dict):
            self.folder_types = {}
        
        self.folder_types[type_name] = type_info
    
    def _get_file_scanner(self):
        """安全获取FileScanner实例"""
        try:
            if hasattr(self.main_window, 'file_scanner') and self.main_window.file_scanner:
                return self.main_window.file_scanner
            else:
                # 尝试从依赖注入获取
                from src.core.dependency_injection import resolve
                from src.core.file_scanner import FileScanner
                return resolve(FileScanner)
        except Exception as e:
            self.logger.error(f"获取FileScanner实例失败: {e}")
            return None

    def load_folder_types(self):
        """
        安全加载文件夹类型配置
        """
        try:
            file_scanner = self._get_file_scanner()
            if not file_scanner:
                self.logger.error("无法获取FileScanner实例")
                self.folder_types = {}
                return

            # 安全调用方法
            if hasattr(file_scanner, 'load_folder_types'):
                try:
                    file_scanner.load_folder_types()
                except Exception as e:
                    self.logger.warning(f"调用load_folder_types方法失败: {e}")
            else:
                self.logger.warning("FileScanner没有load_folder_types方法，跳过加载")

            # 安全获取属性
            if hasattr(file_scanner, 'folder_types'):
                self.folder_types = file_scanner.folder_types.copy()
                self.logger.info(f"成功加载 {len(self.folder_types)} 个文件夹类型配置")
            else:
                self.logger.warning("FileScanner没有folder_types属性，使用空配置")
                self.folder_types = {}

            # 更新类型列表
            self.update_types_list()
            
        except Exception as e:
            self.logger.error(f"加载文件夹类型配置失败: {e}")
            messagebox.showerror("错误", f"加载文件夹类型配置失败: {e}")
    
    def save_folder_types(self):
        """
        保存文件夹类型配置
        """
        try:
            # 获取FileScanner实例
            file_scanner = None
            if hasattr(self.main_window, 'file_scanner') and self.main_window.file_scanner:
                file_scanner = self.main_window.file_scanner
            else:
                # 尝试从依赖注入获取
                from src.core.dependency_injection import resolve
                from src.core.file_scanner import FileScanner
                file_scanner = resolve(FileScanner)
            
            if not file_scanner:
                self.logger.error("无法获取FileScanner实例")
                return
            
            # 设置文件夹类型配置
            file_scanner.folder_types = self.folder_types.copy()
            
            # 保存文件夹类型配置
            file_scanner.save_folder_types()
            
        except Exception as e:
            self.logger.error(f"保存文件夹类型配置失败: {e}")
            messagebox.showerror("错误", f"保存文件夹类型配置失败: {e}")
    
    def update_types_list(self):
        """
        更新类型列表
        """
        # 清空类型列表
        self.types_listbox.delete(0, tk.END)
        
        # 添加类型
        if hasattr(self, 'folder_types') and isinstance(self.folder_types, dict):
            for type_name in sorted(self.folder_types.keys()):
                self.types_listbox.insert(tk.END, type_name)

    def on_style_selected(self, event=None):
        """
        处理风格选择事件
        
        参数:
            event: 事件对象
        """
        # 获取选中的风格
        selected = self.style_listbox.curselection()
        if not selected:
            return
        
        # 获取风格名称
        style_name = self.style_listbox.get(selected[0])
        
        # 从风格tag数据中获取风格信息
        if not hasattr(self, 'style_tags') or not isinstance(self.style_tags, dict):
            self.style_tags = {}
            
        style_info = self.style_tags.get(style_name, {})
        
        # 确保style_info是字典类型
        if not isinstance(style_info, dict):
            style_info = {}
        
        # 更新风格详情
        self.style_name_var.set(style_name)
        self.style_enabled_var.set(style_info.get('enabled', True))
        self.style_description_var.set(style_info.get('description', ''))
        
        # 更新文件夹列表
        self.style_folders_listbox.delete(0, tk.END)
        folders = style_info.get('folders', [])
        if isinstance(folders, list):
            for folder in folders:
                self.style_folders_listbox.insert(tk.END, folder)
    
    def add_style_folder(self):
        """
        添加文件夹到当前风格
        """
        # 获取当前风格名称
        style_name = self.style_name_var.get()
        if not style_name:
            messagebox.showwarning("警告", "请先选择或输入一个风格名称")
            return
        
        # 选择文件夹
        folder_path = filedialog.askdirectory(title="选择要添加的文件夹")
        if not folder_path:
            return
        
        # 标准化路径
        folder_path = _normalize_path(folder_path)
        
        # 检查是否已存在
        for i in range(self.style_folders_listbox.size()):
            if self.style_folders_listbox.get(i) == folder_path:
                messagebox.showwarning("警告", f"文件夹 {folder_path} 已存在")
                return
        
        # 添加到列表
        self.style_folders_listbox.insert(tk.END, folder_path)
        
        # 更新风格信息
        self.update_style_info()
    
    def remove_style_folder(self):
        """
        从当前风格中移除文件夹
        """
        # 获取选中的文件夹
        selected = self.style_folders_listbox.curselection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个文件夹")
            return
        
        # 获取文件夹路径
        folder_path = self.style_folders_listbox.get(selected[0])
        
        # 确认移除
        if not messagebox.askyesno("确认", f"确定要移除文件夹 {folder_path} 吗？"):
            return
        
        # 从列表中移除
        self.style_folders_listbox.delete(selected[0])
        
        # 更新风格信息
        self.update_style_info()
    
    def add_style(self):
        """
        添加新风格
        """
        # 清空风格详情
        self.style_name_var.set("")
        self.style_enabled_var.set(True)
        self.style_description_var.set("")
        self.style_folders_listbox.delete(0, tk.END)
        
        # 获取新风格名称
        new_style = simpledialog.askstring("添加风格", "请输入新风格名称:")
        if not new_style:
            return
        
        # 检查是否已存在
        for i in range(self.style_listbox.size()):
            if self.style_listbox.get(i) == new_style:
                messagebox.showwarning("警告", f"风格 {new_style} 已存在")
                return
        
        # 添加到列表
        self.style_listbox.insert(tk.END, new_style)
        
        # 选中新风格
        self.style_listbox.selection_clear(0, tk.END)
        self.style_listbox.selection_set(tk.END)
        
        # 设置风格名称
        self.style_name_var.set(new_style)
        
        # 更新风格信息
        self.update_style_info()
    
    def save_style(self):
        """
        保存当前风格信息
        """
        # 获取风格名称
        style_name = self.style_name_var.get()
        if not style_name:
            messagebox.showwarning("警告", "请输入风格名称")
            return
        
        # 获取风格信息
        style_info = {
            'enabled': self.style_enabled_var.get(),
            'description': self.style_description_var.get(),
            'folders': []
        }
        
        # 获取文件夹列表
        for i in range(self.style_folders_listbox.size()):
            style_info['folders'].append(self.style_folders_listbox.get(i))
        
        # 更新风格信息
        if not hasattr(self, 'style_tags') or not isinstance(self.style_tags, dict):
            self.style_tags = {}
        
        # 检查是否需要重命名
        selected = self.style_listbox.curselection()
        if selected:
            old_name = self.style_listbox.get(selected[0])
            if old_name != style_name:
                # 需要重命名
                if style_name in self.style_tags:
                    # 新名称已存在
                    messagebox.showwarning("警告", f"风格 {style_name} 已存在")
                    return
                
                # 删除旧名称
                if old_name in self.style_tags:
                    del self.style_tags[old_name]
                
                # 更新列表项
                self.style_listbox.delete(selected[0])
                self.style_listbox.insert(selected[0], style_name)
                self.style_listbox.selection_set(selected[0])
        
        # 保存风格信息
        self.style_tags[style_name] = style_info
        
        # 保存到文件
        self.save_style_tags()
        
        # 显示提示
        self.main_window.log_message(f"已保存风格 {style_name}", "success")
    
    def delete_style(self):
        """
        删除当前风格
        """
        # 获取选中的风格
        selected = self.style_listbox.curselection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个风格")
            return
        
        # 获取风格名称
        style_name = self.style_listbox.get(selected[0])
        
        # 确认删除
        if not messagebox.askyesno("确认", f"确定要删除风格 {style_name} 吗？"):
            return
        
        # 从列表中删除
        self.style_listbox.delete(selected[0])
        
        # 从风格信息中删除
        if hasattr(self, 'style_tags') and isinstance(self.style_tags, dict) and style_name in self.style_tags:
            del self.style_tags[style_name]
        
        # 清空风格详情
        self.style_name_var.set("")
        self.style_enabled_var.set(True)
        self.style_description_var.set("")
        self.style_folders_listbox.delete(0, tk.END)
        
        # 保存到文件
        self.save_style_tags()
        
        # 显示提示
        self.main_window.log_message(f"已删除风格 {style_name}", "success")
    
    def update_style_info(self):
        """
        更新当前风格信息
        """
        # 获取风格名称
        style_name = self.style_name_var.get()
        if not style_name:
            return
        
        # 获取风格信息
        style_info = {
            'enabled': self.style_enabled_var.get(),
            'description': self.style_description_var.get(),
            'folders': []
        }
        
        # 获取文件夹列表
        for i in range(self.style_folders_listbox.size()):
            style_info['folders'].append(self.style_folders_listbox.get(i))
        
        # 更新风格信息
        if not hasattr(self, 'style_tags') or not isinstance(self.style_tags, dict):
            self.style_tags = {}
        
        self.style_tags[style_name] = style_info
    
    def save_style_tags(self):
        """
        保存风格tag配置
        """
        try:
            # 获取FileScanner实例
            file_scanner = None
            if hasattr(self.main_window, 'file_scanner') and self.main_window.file_scanner:
                file_scanner = self.main_window.file_scanner
            else:
                # 尝试从依赖注入获取
                from src.core.dependency_injection import resolve
                from src.core.file_scanner import FileScanner
                file_scanner = resolve(FileScanner)
            
            if not file_scanner:
                self.logger.error("无法获取FileScanner实例")
                return
            
            # 设置风格tag配置
            file_scanner.style_tags = self.style_tags.copy()
            
            # 保存风格tag配置
            file_scanner.save_style_tags()
            
        except Exception as e:
            self.logger.error(f"保存风格tag配置失败: {e}")
            messagebox.showerror("错误", f"保存风格tag配置失败: {e}")

    def _normalize_path(self, path):
        import os
        return os.path.normpath(path).replace('\\', '/').rstrip('/')

    # 以文件操作为例
    def some_file_op(self, file_info):
        file_path = file_info.get('file_path') or file_info.get('path')
        if not file_path:
            return
        file_path = _normalize_path(file_path)
        file_info['file_path'] = file_path
        # ... 其余逻辑 ...


# 测试代码
if __name__ == "__main__":
    root = tk.Tk()
    root.geometry("800x600")
    
    # 创建主窗口模拟对象
    class MainWindowMock:
        def __init__(self):
            self.root = root
            self.status_text = tk.StringVar()
            self.progress_var = tk.DoubleVar()
            self.task_label = tk.StringVar()
            self.subtask_label = tk.StringVar()
            self.task_queue = queue.Queue()
            # mock event_system和async_manager属性
            self.event_system = object()
            self.async_manager = object()
            
            class RuleEngineMock:
                def get_whitelist_rules(self):
                    return []
                
                def save_whitelist_rules(self, rules):
                    pass
                
                def get_whitelist_target_dir(self):
                    return ""
                
                def save_whitelist_target_dir(self, target_dir):
                    pass
            
            self.rule_engine = RuleEngineMock()
            
            def log_message(self, message, level="info"):
                print(f"[{level}] {message}")
            
            self.log_message = log_message.__get__(self)
    
    main_window = MainWindowMock()
    
    # 创建白名单面板
    panel = WhitelistPanel(root, main_window)
    panel.get_frame().pack(fill=tk.BOTH, expand=True)
    
    root.mainloop()