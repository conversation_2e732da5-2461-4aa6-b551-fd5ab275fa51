# 字体大小设置功能使用示例

## 快速开始

### 1. 启动应用程序
```bash
python main.py
```

### 2. 打开设置面板
- 在应用程序主界面中，点击右侧的"设置"选项卡
- 选择"常规设置"标签页

### 3. 调整字体大小
- 在"界面设置"部分找到"字体大小"选项
- 从下拉列表中选择合适的字体大小（8-20）
- 点击"应用"按钮

### 4. 保存设置
- 字体大小会立即应用到整个界面
- 设置会自动保存到配置文件
- 下次启动应用程序时会使用保存的字体大小

## 推荐设置

### 高分辨率显示器（4K、2K）
- 推荐字体大小：14-16
- 适合字体大小：12-18

### 标准显示器（1080p）
- 推荐字体大小：10-12
- 适合字体大小：9-14

### 小屏幕显示器
- 推荐字体大小：8-10
- 适合字体大小：8-11

## 功能验证

### 测试步骤
1. 启动应用程序
2. 打开设置面板
3. 将字体大小从10调整为16
4. 观察界面变化：
   - 按钮文字变大
   - 标签文字变大
   - 输入框文字变大
   - 文件树文字变大
   - 列表框文字变大

### 预期效果
- 所有界面元素的文字都会变大
- 界面布局保持协调
- 没有文字重叠或显示异常

## 故障排除

### 问题1：字体大小调整后没有变化
**解决方案**：
1. 确保点击了"应用"按钮
2. 检查是否有错误提示
3. 重启应用程序

### 问题2：部分控件字体没有更新
**解决方案**：
1. 这是正常现象，某些控件需要重启才能完全更新
2. 重启应用程序后所有控件都会使用新字体大小

### 问题3：配置文件错误
**解决方案**：
1. 删除 `config/settings.yaml` 文件
2. 重启应用程序
3. 重新设置字体大小

## 技术细节

### 配置文件位置
```
config/settings.yaml
```

### 配置文件内容
```yaml
general:
  font_size: 10  # 字体大小设置
```

### 支持的字体大小
- 8, 9, 10, 11, 12, 14, 16, 18, 20

### 影响的界面元素
- TButton（按钮）
- TLabel（标签）
- TEntry（输入框）
- TCombobox（组合框）
- TListbox（列表框）
- Treeview（树形视图）
- TText（文本区域）

## 开发说明

### 添加新的字体大小
在 `src/ui/settings_panel.py` 中修改：
```python
font_size_combo["values"] = ("8", "9", "10", "11", "12", "14", "16", "18", "20", "24")
```

### 自定义字体族
在 `src/ui/factory.py` 中修改：
```python
style.configure("TButton", font=("Microsoft YaHei", font_size))
```

### 添加字体预览
可以在设置面板中添加预览区域，让用户在应用前看到效果。

---

*使用示例文档 - 2024-01-10* 