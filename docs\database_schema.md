# 智能文件管理器数据库结构设计

## 概述

本文档描述了智能文件管理器重构后的数据库结构设计，实现了以文件夹为核心的层次化文件树架构。

## 设计原则

1. **文件夹为核心**：将文件夹作为主要的组织单元
2. **层次化结构**：支持任意深度的目录层次
3. **高效查询**：通过合理的索引设计提升查询性能
4. **数据一致性**：通过哈希机制确保数据一致性
5. **向后兼容**：保持与现有代码的兼容性

## 数据库集合结构

### 1. folders 集合

文件夹信息的主要存储集合。

```javascript
{
  // 基本信息
  "folder_id": "folder_abc123_1640995200000000",  // 文件夹唯一标识符
  "path": "/normalized/folder/path",              // 标准化的完整路径
  "name": "folder_name",                          // 文件夹名称
  "parent_id": "parent_folder_id",                // 父文件夹ID（根目录为null）
  "depth": 2,                                     // 目录深度（根目录为1）
  
  // 层次关系
  "child_folder_ids": [                           // 子文件夹ID列表
    "child_folder_id_1",
    "child_folder_id_2"
  ],
  
  // 内容统计
  "files_count": 15,                              // 直接文件数量
  "subfolders_count": 3,                          // 子文件夹数量
  "total_size": 1024000,                          // 总大小（仅直接文件，字节）
  
  // 哈希和一致性
  "files_hash": "md5_hash_of_file_names",         // 文件名哈希值
  "content_hash": "md5_hash_of_file_names",       // 内容哈希（兼容现有实现）
  
  // 时间信息
  "created_time": "2024-01-01T12:00:00.000Z",    // 创建时间（ISO格式）
  "modified_time": "2024-01-01T12:30:00.000Z",   // 修改时间（ISO格式）
  "last_scan_time": "2024-01-01T13:00:00.000Z",  // 最后扫描时间
  "last_hash_update": "2024-01-01T13:00:00.000Z", // 最后哈希更新时间
  
  // 状态信息
  "scan_status": "completed",                     // 扫描状态：pending, scanning, completed, error
  "is_network_folder": false,                     // 是否为网络文件夹
  
  // 兼容性字段（保持与现有FolderInfo兼容）
  "direct_file_count": 15,                        // 直接文件数量（与files_count同步）
  "direct_folder_count": 3,                       // 直接文件夹数量（与subfolders_count同步）
  "total_file_count": 150,                        // 总文件数量（包括子目录）
  "total_folder_count": 30,                       // 总文件夹数量（包括子目录）
  
  // 优化信息
  "is_optimized": false,                          // 是否已优化
  "optimization_suggestions": [],                 // 优化建议列表
  
  // 元数据
  "metadata": {}                                  // 其他元数据
}
```

### 2. files 集合（扩展现有结构）

文件信息存储集合，在现有基础上添加新字段。

```javascript
{
  // 现有字段保持不变
  "file_id": "file_unique_id",
  "path": "/normalized/file/path.txt",
  "name": "file.txt",
  "size": 1024,
  "modified_time": "2024-01-01T12:00:00.000Z",
  "created_time": "2024-01-01T11:00:00.000Z",
  "extension": ".txt",
  "is_video": false,
  "hash": "file_md5_hash",
  "is_junk": false,
  "is_whitelist": false,
  "file_type": "document",
  "metadata": {},
  "parent_id": "parent_node_id",  // 现有字段
  "depth": 2,                     // 现有字段
  
  // 新增字段
  "folder_id": "parent_folder_uuid",              // 所属文件夹ID
  "relative_path": "subfolder/file.txt"           // 相对于扫描根目录的路径
}
```

### 3. tree_nodes 集合（可选，用于UI优化）

树节点信息，用于支持虚拟节点和懒加载。

```javascript
{
  "node_id": "node_unique_id",                    // 节点唯一标识符
  "path": "/normalized/path",                     // 节点路径
  "parent_node_id": "parent_node_id",             // 父节点ID
  "type": "folder",                               // 节点类型：folder, file, virtual
  "depth": 2,                                     // 节点深度
  "children_count": 25,                           // 子节点数量
  "is_virtual": false,                            // 是否为虚拟节点
  "load_status": "loaded",                        // 加载状态：pending, loading, loaded, error
  "folder_id": "corresponding_folder_id",         // 对应的文件夹ID（如果是文件夹节点）
  "created_time": "2024-01-01T12:00:00.000Z",
  "metadata": {}
}
```

## 索引设计

### folders 集合索引

```javascript
// 主要索引
db.folders.createIndex({"folder_id": 1}, {unique: true})           // 唯一索引
db.folders.createIndex({"path": 1}, {unique: true})                // 路径唯一索引

// 层次查询索引
db.folders.createIndex({"parent_id": 1, "depth": 1})               // 父子关系查询
db.folders.createIndex({"depth": 1, "scan_status": 1})             // 按深度和状态查询

// 哈希和一致性索引
db.folders.createIndex({"files_hash": 1})                          // 哈希查询
db.folders.createIndex({"last_scan_time": 1})                      // 扫描时间查询

// 网络文件夹索引
db.folders.createIndex({"is_network_folder": 1})                   // 网络文件夹查询

// 复合索引
db.folders.createIndex({"parent_id": 1, "name": 1})                // 父目录下的文件夹名查询
```

### files 集合索引（扩展现有）

```javascript
// 现有索引保持不变
db.files.createIndex({"file_id": 1}, {unique: true, sparse: true})
db.files.createIndex({"path": 1}, {unique: true})
db.files.createIndex({"hash": 1})
db.files.createIndex({"size": 1})
db.files.createIndex({"extension": 1})

// 新增索引
db.files.createIndex({"folder_id": 1})                             // 文件夹关联查询
db.files.createIndex({"folder_id": 1, "name": 1})                  // 文件夹内文件查询
db.files.createIndex({"relative_path": 1})                         // 相对路径查询
```

### tree_nodes 集合索引

```javascript
db.tree_nodes.createIndex({"node_id": 1}, {unique: true})
db.tree_nodes.createIndex({"path": 1}, {unique: true, sparse: true})
db.tree_nodes.createIndex({"parent_node_id": 1, "depth": 1})
db.tree_nodes.createIndex({"type": 1, "depth": 1})
db.tree_nodes.createIndex({"is_virtual": 1})
db.tree_nodes.createIndex({"load_status": 1})
```

## 查询模式

### 1. 获取文件夹的直接子文件夹

```javascript
db.folders.find({
  "parent_id": "target_folder_id",
  "depth": target_depth + 1
}).sort({"name": 1})
```

### 2. 获取文件夹内的所有文件

```javascript
db.files.find({
  "folder_id": "target_folder_id"
}).sort({"name": 1})
```

### 3. 验证文件夹内容一致性

```javascript
// 获取当前存储的哈希
const folder = db.folders.findOne({"folder_id": "target_folder_id"})
const storedHash = folder.files_hash

// 重新计算哈希并比较
// （通过应用程序逻辑实现）
```

### 4. 按深度分层加载

```javascript
db.folders.find({
  "depth": target_depth,
  "scan_status": "completed"
}).sort({"path": 1})
```

## 数据一致性保证

### 1. 哈希计算规则

- 基于文件夹内所有文件名（不包含路径）
- 文件名按字典序排序
- 使用MD5算法计算哈希
- 格式：`md5(sorted_file_names.join('|'))`

### 2. 更新策略

- 文件增加：重新计算父文件夹哈希
- 文件删除：重新计算父文件夹哈希
- 文件重命名：重新计算父文件夹哈希
- 文件移动：重新计算源和目标文件夹哈希

### 3. 一致性检查

- 定期验证存储的哈希与实际计算的哈希
- 发现不一致时触发重新扫描
- 支持增量更新和全量重建

## 迁移策略

### 阶段1：创建新集合和索引

1. 创建 `folders` 集合
2. 建立所有必要索引
3. 不影响现有功能

### 阶段2：数据迁移

1. 从现有数据提取文件夹信息
2. 计算文件夹哈希值
3. 建立父子关系
4. 更新 `files` 集合的新字段

### 阶段3：应用程序适配

1. 更新数据访问层
2. 修改文件树构建逻辑
3. 集成哈希验证机制

### 阶段4：清理和优化

1. 移除不再使用的字段
2. 优化查询性能
3. 完善监控和日志

## 性能考虑

### 1. 查询优化

- 使用复合索引减少查询时间
- 分页加载大量数据
- 缓存频繁访问的文件夹信息

### 2. 写入优化

- 批量更新减少数据库连接
- 异步处理哈希计算
- 使用事务保证数据一致性

### 3. 存储优化

- 合理设计字段类型
- 避免冗余数据存储
- 定期清理过期数据

## 监控和维护

### 1. 性能监控

- 查询响应时间
- 索引使用情况
- 缓存命中率

### 2. 数据质量监控

- 哈希一致性检查
- 孤立数据检测
- 关系完整性验证

### 3. 维护任务

- 定期重建索引
- 清理临时数据
- 备份关键数据
