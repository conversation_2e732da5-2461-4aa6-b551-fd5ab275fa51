
#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Smart File Manager 安全启动脚本
带有内存监控和崩溃恢复功能
"""

import os
import sys
import time
import traceback
import psutil
import gc
from pathlib import Path

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

def monitor_memory():
    """监控内存使用"""
    try:
        process = psutil.Process()
        memory_mb = process.memory_info().rss / 1024 / 1024
        print(f"当前内存使用: {memory_mb:.1f}MB")
        
        if memory_mb > 1000:  # 1GB限制
            print("警告: 内存使用过高，执行垃圾回收")
            gc.collect()
            
        return memory_mb
    except Exception as e:
        print(f"内存监控出错: {e}")
        return 0

def safe_start():
    """安全启动应用"""
    print("Smart File Manager 安全启动")
    print("=" * 40)
    
    # 初始内存检查
    initial_memory = monitor_memory()
    
    try:
        # 设置内存限制
        import resource
        # 限制内存使用为1.5GB
        resource.setrlimit(resource.RLIMIT_AS, (1536*1024*1024, 1536*1024*1024))
        print("已设置内存限制: 1.5GB")
    except:
        print("无法设置内存限制")
        
    try:
        print("正在启动应用...")
        
        # 导入并启动应用
        from src.app import main
        
        # 启动内存监控线程
        import threading
        def memory_monitor_thread():
            while True:
                time.sleep(30)  # 每30秒检查一次
                current_memory = monitor_memory()
                if current_memory > 1200:  # 1.2GB
                    print("严重警告: 内存使用过高，建议重启应用")
                    
        monitor_thread = threading.Thread(target=memory_monitor_thread, daemon=True)
        monitor_thread.start()
        
        # 启动主应用
        main()
        
    except MemoryError:
        print("错误: 内存不足，应用无法启动")
        print("建议:")
        print("1. 关闭其他应用程序释放内存")
        print("2. 减少要处理的文件数量")
        print("3. 重启计算机")
        
    except Exception as e:
        print(f"应用启动失败: {e}")
        print("错误详情:")
        traceback.print_exc()
        
        print("\n尝试恢复措施:")
        print("1. 检查是否有其他实例正在运行")
        print("2. 清理临时文件")
        print("3. 重启应用")
        
    finally:
        final_memory = monitor_memory()
        print(f"
内存使用统计:")
        print(f"启动时: {initial_memory:.1f}MB")
        print(f"结束时: {final_memory:.1f}MB")
        print(f"增长: {final_memory - initial_memory:.1f}MB")

if __name__ == '__main__':
    safe_start()
