# 智能文件管理器架构规则

## 🏗️ 架构设计原则

### ARCH-001: 分层架构规范
**优先级**: 🔴 最高

**架构层次**:
```
┌─────────────────────────────────────┐
│           UI表现层                   │
│    (Presentation Layer)             │
├─────────────────────────────────────┤
│           事件通信层                 │
│       (Event Layer)                 │
├─────────────────────────────────────┤
│          业务服务层                  │
│      (Service Layer)                │
├─────────────────────────────────────┤
│          数据访问层                  │
│       (Data Layer)                  │
└─────────────────────────────────────┘
```

**层次职责**:
- **UI表现层**: 用户界面展示、用户交互处理、事件发布
- **事件通信层**: 事件路由、事件分发、模块解耦
- **业务服务层**: 业务逻辑处理、数据验证、任务协调
- **数据访问层**: 数据存储、数据检索、数据一致性

**依赖规则**:
- ✅ **允许**: 上层依赖下层
- ❌ **禁止**: 下层依赖上层
- ❌ **禁止**: 跨层直接调用

### ARCH-002: 模块组织规范
**优先级**: 🔴 最高

**目录结构标准**:
```
src/
├── ui/                    # UI层
│   ├── core/             # 核心UI组件
│   │   ├── window_manager.py
│   │   ├── event_dispatcher.py
│   │   └── ui_state_manager.py
│   ├── panels/           # 功能面板
│   │   ├── scan_panel.py
│   │   ├── duplicate_panel.py
│   │   ├── rename_panel.py
│   │   ├── junk_panel.py
│   │   ├── whitelist_panel.py
│   │   └── settings_panel.py
│   ├── components/       # 共享组件
│   │   ├── data_table.py
│   │   ├── progress_display.py
│   │   ├── filter_bar.py
│   │   └── action_buttons.py
│   └── file_management/  # 文件管理UI
│       ├── directory_manager.py
│       ├── file_tree_display.py
│       └── file_tree_data.py
├── services/             # 业务服务层
│   ├── interfaces/       # 服务接口
│   │   ├── file_service.py
│   │   ├── duplicate_service.py
│   │   ├── rename_service.py
│   │   ├── junk_service.py
│   │   └── whitelist_service.py
│   ├── impl/            # 服务实现
│   │   ├── file_scan_service.py
│   │   ├── hash_calculation_service.py
│   │   ├── duplicate_detection_service.py
│   │   ├── rename_service_impl.py
│   │   ├── junk_file_service.py
│   │   └── whitelist_service_impl.py
│   └── async_tasks/     # 异步任务
│       ├── task_manager.py
│       ├── progress_tracker.py
│       └── resource_manager.py
├── data/                # 数据层
│   ├── models/          # 数据模型
│   │   ├── file_info.py
│   │   ├── scan_result.py
│   │   └── task_status.py
│   ├── dto/             # 数据传输对象
│   │   ├── scan_dto.py
│   │   ├── duplicate_dto.py
│   │   └── rename_dto.py
│   ├── repositories/    # 数据仓库
│   │   ├── file_repository.py
│   │   ├── config_repository.py
│   │   └── task_repository.py
│   └── db/              # 数据库管理
│       ├── database_service.py
│       ├── connection_manager.py
│       └── migration_manager.py
└── utils/               # 工具类
    ├── common/          # 通用工具
    ├── file/            # 文件工具
    └── async_utils/     # 异步工具
```

### ARCH-003: 依赖注入规范
**优先级**: 🟡 高

**依赖注入原则**:
- ✅ **必须**: 使用接口依赖，不依赖具体实现
- ✅ **必须**: 通过构造函数注入依赖
- ✅ **必须**: 配置依赖的生命周期
- ❌ **禁止**: 在代码中硬编码依赖关系
- ❌ **禁止**: 使用全局变量传递依赖

**依赖注入示例**:
```python
# ✅ 正确: 接口依赖
class FileScanService:
    def __init__(self, 
                 db_service: IDatabaseService,
                 event_bus: IEventBus,
                 logger: ILogger):
        self._db_service = db_service
        self._event_bus = event_bus
        self._logger = logger

# ✅ 正确: 依赖配置
def configure_dependencies():
    container = DIContainer()
    
    # 注册接口和实现
    container.register(IDatabaseService, DatabaseService, lifetime=Singleton)
    container.register(IEventBus, EventBus, lifetime=Singleton)
    container.register(IFileScanService, FileScanService, lifetime=Transient)
    
    return container
```

### ARCH-004: 事件驱动架构规范
**优先级**: 🔴 最高

**事件总线架构**:
```python
# 事件总线接口
class IEventBus:
    def publish(self, event_type: str, event_data: EventData) -> None:
        pass
    
    def subscribe(self, event_type: str, handler: Callable, subscriber_id: str = None) -> None:
        pass
    
    def unsubscribe(self, event_type: str, subscriber_id: str = None) -> None:
        pass
```

**事件流向规范**:
```
UI层 ──发布──→ 事件总线 ──分发──→ 业务服务层
  ↑                              ↓
  └──订阅←── 事件总线 ←──发布──────┘
```

**事件处理规范**:
- ✅ **必须**: 事件处理器异步执行
- ✅ **必须**: 事件处理具有幂等性
- ✅ **必须**: 事件处理有超时控制
- ❌ **禁止**: 事件处理中执行阻塞操作
- ❌ **禁止**: 事件处理中抛出未捕获异常

### ARCH-005: 数据流架构规范
**优先级**: 🟡 高

**数据流向**:
```
用户操作 → UI层 → 事件 → 业务服务层 → 数据访问层 → 数据库
    ↑                                              ↓
    └── UI更新 ← 事件 ← 业务服务层 ← 数据访问层 ←────┘
```

**数据传递规范**:
- ✅ **必须**: 使用DTO对象传递数据
- ✅ **必须**: 数据验证在边界处进行
- ✅ **必须**: 数据转换在适当层次进行
- ❌ **禁止**: 跨层传递内部数据结构
- ❌ **禁止**: 在传递过程中修改数据

## 🔧 架构实施指南

### 模块拆分策略
1. **识别职责边界**: 分析现有代码的职责混合点
2. **定义接口契约**: 为每个模块定义清晰的接口
3. **渐进式重构**: 逐步拆分，保持功能完整性
4. **测试驱动**: 为每个拆分的模块编写测试

### 依赖管理策略
1. **接口优先**: 先定义接口，再实现具体类
2. **配置集中**: 将依赖配置集中管理
3. **生命周期管理**: 明确每个依赖的生命周期
4. **循环依赖检测**: 定期检查和消除循环依赖

### 事件设计策略
1. **事件分类**: 区分UI事件、业务事件、系统事件
2. **事件版本化**: 为事件定义版本，支持向后兼容
3. **事件持久化**: 关键事件需要持久化存储
4. **事件重放**: 支持事件重放用于调试和恢复

## 📏 架构质量度量

### 耦合度指标
- **模块间依赖数量**: 每个模块的依赖数量应 < 5
- **循环依赖数量**: 应为 0
- **接口依赖比例**: 应 > 80%

### 内聚度指标
- **单一职责符合度**: 每个模块职责应单一明确
- **功能相关性**: 模块内功能相关性应 > 80%
- **代码复用率**: 共享组件使用率应 > 60%

### 可维护性指标
- **模块大小**: 单个模块代码行数应 < 500行
- **方法复杂度**: 单个方法圈复杂度应 < 10
- **测试覆盖率**: 应 > 80%

## 🚨 架构违规检测

### 自动检测规则
```python
# 检测跨层调用
def check_layer_violation():
    # 检查UI层是否直接调用数据层
    # 检查数据层是否依赖业务层
    pass

# 检测循环依赖
def check_circular_dependency():
    # 分析模块依赖图
    # 检测循环依赖路径
    pass

# 检测接口依赖
def check_interface_dependency():
    # 检查是否依赖具体实现
    # 统计接口依赖比例
    pass
```

### 人工审查要点
- [ ] 模块职责是否单一明确
- [ ] 依赖关系是否合理
- [ ] 事件设计是否恰当
- [ ] 数据流向是否清晰
- [ ] 接口设计是否稳定

## 📚 相关文档
- [编程规则](CODING_RULES.md)
- [性能规则](PERFORMANCE_RULES.md)
- [重构指南](../guidelines/REFACTORING_GUIDE.md)
