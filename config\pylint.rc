[MASTER]
# 指定要加载的插件
load-plugins=pylint.extensions.docparams,
             pylint.extensions.docstyle,
             pylint.extensions.mccabe

# 指定要分析的文件或目录
# 可以通过命令行参数覆盖
# init-hook='import sys; sys.path.append("src")'

# 使用多进程加速检查
jobs=4

# 持久化检查结果
persistent=yes

# 缓存大小
cache-size=500

[MESSAGES CONTROL]
# 启用的检查类别
enable=all

# 禁用的检查项
disable=missing-docstring,          # 缺少文档字符串
        too-few-public-methods,     # 公共方法太少
        too-many-arguments,         # 参数太多(在某些情况下是合理的)
        too-many-locals,            # 局部变量太多
        too-many-branches,          # 分支太多
        too-many-statements,        # 语句太多
        duplicate-code,             # 重复代码(在重构期间可能存在)
        fixme,                      # TODO/FIXME注释
        line-too-long,              # 行太长(由black格式化工具处理)
        invalid-name,               # 无效名称(某些情况下需要特殊命名)
        import-error,               # 导入错误(在开发环境中可能出现)
        no-member,                  # 没有成员(动态属性可能触发)
        unused-argument,            # 未使用参数(接口实现中可能需要)
        broad-except,               # 过于宽泛的异常捕获(某些情况下是必要的)
        logging-fstring-interpolation  # f-string在日志中的使用

[REPORTS]
# 设置输出格式
output-format=colorized

# 包含消息ID
include-ids=yes

# 报告模板
msg-template={path}:{line}:{column}: {msg_id}: {msg} ({symbol})

# 生成报告
reports=yes

# 评估表达式
evaluation=10.0 - ((float(5 * error + warning + refactor + convention) / statement) * 10)

[REFACTORING]
# 最大返回值数量
max-returns=6

# 最大分支数量
max-branches=12

# 最大语句数量
max-statements=50

# 最大参数数量
max-args=5

# 最大局部变量数量
max-locals=15

# 最大属性数量
max-attributes=7

# 最大公共方法数量
max-public-methods=20

# 最小公共方法数量
min-public-methods=2

[FORMAT]
# 最大行长度
max-line-length=100

# 忽略长行的正则表达式
ignore-long-lines=^\s*(# )?<?https?://\S+>?$

# 单行if语句
single-line-if=no

# 缩进字符串
indent-string='    '

# 缩进后续行
indent-after-paren=4

# 期望的行结束格式
expected-line-ending-format=LF

[LOGGING]
# 日志模块名称
logging-modules=logging

[MISCELLANEOUS]
# 注释中的标记，报告为警告
notes=FIXME,XXX,TODO,BUG,HACK

[SIMILARITIES]
# 最小相似行数
min-similarity-lines=4

# 忽略注释
ignore-comments=yes

# 忽略文档字符串
ignore-docstrings=yes

# 忽略导入
ignore-imports=no

[SPELLING]
# 拼写检查字典
spelling-dict=

# 拼写检查私有字典文件
spelling-private-dict-file=

# 存储未知单词的文件
spelling-store-unknown-words=no

[TYPECHECK]
# 忽略的模块列表
ignored-modules=

# 忽略的类列表
ignored-classes=optparse.Values,thread._local,_thread._local

# 生成的成员列表
generated-members=

# 上下文管理器
contextmanager-decorators=contextlib.contextmanager

# 忽略缺失成员的模块
missing-member-hint=yes

# 缺失成员提示距离
missing-member-hint-distance=1

# 缺失成员最大提示数
missing-member-max-choices=1

[VARIABLES]
# 告诉pylint哪些变量名是可以的
good-names=i,j,k,ex,Run,_,id,db,ui,x,y,z

# 坏的变量名
bad-names=foo,bar,baz,toto,tutu,tata

# 变量名的正则表达式
variable-rgx=[a-z_][a-z0-9_]{2,30}$

# 常量名的正则表达式
const-rgx=(([A-Z_][A-Z0-9_]*)|(__.*__))$

# 属性名的正则表达式
attr-rgx=[a-z_][a-z0-9_]{2,30}$

# 参数名的正则表达式
argument-rgx=[a-z_][a-z0-9_]{2,30}$

# 类属性名的正则表达式
class-attribute-rgx=([A-Za-z_][A-Za-z0-9_]{2,30}|(__.*__))$

# 内联变量名的正则表达式
inlinevar-rgx=[A-Za-z_][A-Za-z0-9_]*$

# 类名的正则表达式
class-rgx=[A-Z_][a-zA-Z0-9]+$

# 模块名的正则表达式
module-rgx=(([a-z_][a-z0-9_]*)|([A-Z][a-zA-Z0-9]+))$

# 方法名的正则表达式
method-rgx=[a-z_][a-z0-9_]{2,30}$

# 函数名的正则表达式
function-rgx=[a-z_][a-z0-9_]{2,30}$

# 不需要文档字符串的函数名
no-docstring-rgx=^_

# 允许重新定义的内置函数
redefining-builtins-modules=six.moves,past.builtins,future.builtins,builtins,io

[CLASSES]
# 有效的元类名称列表
valid-metaclass-classmethod-first-arg=mcs

# 有效的类方法第一个参数名称列表
valid-classmethod-first-arg=cls

# 排除受保护访问警告的成员名称列表
exclude-protected=_asdict,_fields,_replace,_source,_make

[DESIGN]
# 最大参数数量
max-args=5

# 最大局部变量数量
max-locals=15

# 最大返回语句数量
max-returns=6

# 最大分支数量
max-branches=12

# 最大语句数量
max-statements=50

# 最大父类数量
max-parents=7

# 最大属性数量
max-attributes=7

# 最小公共方法数量
min-public-methods=2

# 最大公共方法数量
max-public-methods=20

# 最大布尔表达式数量
max-bool-expr=5

[IMPORTS]
# 已弃用的模块列表
deprecated-modules=optparse,tkinter.tix

# 导入图
import-graph=

# 外部导入图
ext-import-graph=

# 内部导入图
int-import-graph=

# 已知的标准库模块
known-standard-library=

# 已知的第三方模块
known-third-party=

# 分析回退模块
analyse-fallback-blocks=no

[EXCEPTIONS]
# 可以在except语句中使用的异常
overgeneral-exceptions=Exception
