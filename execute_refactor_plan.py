#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能文件管理器 - 一键式性能优化改造执行脚本

本脚本将按照改造方案逐步执行所有优化措施：
1. 立即修复UI冻结问题
2. 部署统一异步管理器
3. 集成性能监控系统
4. 优化文件树组件
5. 部署智能缓存管理
6. 生成改造报告

Author: AI Assistant
Date: 2024
"""

import os
import sys
import asyncio
import shutil
import logging
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('refactor_execution.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class RefactorExecutor:
    """改造执行器"""
    
    def __init__(self):
        self.project_root = project_root
        self.backup_dir = self.project_root / 'backup' / f'refactor_{datetime.now().strftime("%Y%m%d_%H%M%S")}'
        self.execution_log = []
        
    def log_step(self, step: str, status: str, details: str = ""):
        """记录执行步骤"""
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'step': step,
            'status': status,
            'details': details
        }
        self.execution_log.append(log_entry)
        logger.info(f"[{status}] {step}: {details}")
    
    def create_backup(self):
        """创建备份"""
        try:
            self.backup_dir.mkdir(parents=True, exist_ok=True)
            
            # 备份关键文件
            key_files = [
                'src/ui/main_window.py',
                'src/ui/file_tree.py',
                'src/utils/async_manager.py',
                'src/core/file_scanner.py'
            ]
            
            for file_path in key_files:
                src_file = self.project_root / file_path
                if src_file.exists():
                    dst_file = self.backup_dir / file_path
                    dst_file.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(src_file, dst_file)
                    
            self.log_step("创建备份", "SUCCESS", f"备份已保存到: {self.backup_dir}")
            return True
            
        except Exception as e:
            self.log_step("创建备份", "ERROR", str(e))
            return False
    
    def execute_ui_freeze_fix(self):
        """执行UI冻结修复"""
        try:
            # 检查quick_fix_ui_freeze.py是否存在
            fix_script = self.project_root / 'quick_fix_ui_freeze.py'
            if fix_script.exists():
                # 执行修复脚本
                import subprocess
                result = subprocess.run([sys.executable, str(fix_script)], 
                                      capture_output=True, text=True, encoding='utf-8')
                
                if result.returncode == 0:
                    self.log_step("UI冻结修复", "SUCCESS", "UI冻结问题已修复")
                    return True
                else:
                    self.log_step("UI冻结修复", "ERROR", result.stderr)
                    return False
            else:
                self.log_step("UI冻结修复", "SKIP", "修复脚本不存在")
                return True
                
        except Exception as e:
            self.log_step("UI冻结修复", "ERROR", str(e))
            return False
    
    def deploy_async_manager(self):
        """部署统一异步管理器"""
        try:
            async_manager_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一异步管理器 - 重构版本

提供统一的异步任务管理、协调和监控功能
"""

import asyncio
import logging
import threading
import weakref
from typing import Dict, List, Optional, Callable, Any, Set
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum

logger = logging.getLogger(__name__)

class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class TaskInfo:
    """任务信息"""
    task_id: str
    name: str
    status: TaskStatus = TaskStatus.PENDING
    progress: float = 0.0
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    result: Any = None
    
class UnifiedAsyncManager:
    """统一异步管理器"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
            
        self._initialized = True
        self._loop: Optional[asyncio.AbstractEventLoop] = None
        self._tasks: Dict[str, TaskInfo] = {}
        self._running_tasks: Dict[str, asyncio.Task] = {}
        self._task_callbacks: Dict[str, List[Callable]] = {}
        self._global_callbacks: List[Callable] = []
        self._shutdown_event = asyncio.Event()
        
    async def initialize(self):
        """初始化异步管理器"""
        self._loop = asyncio.get_running_loop()
        logger.info("统一异步管理器已初始化")
        
    async def submit_task(self, 
                         task_id: str,
                         coro: Callable,
                         name: str = "",
                         callback: Optional[Callable] = None) -> str:
        """提交异步任务"""
        if task_id in self._tasks:
            raise ValueError(f"任务ID {task_id} 已存在")
            
        # 创建任务信息
        task_info = TaskInfo(
            task_id=task_id,
            name=name or task_id
        )
        self._tasks[task_id] = task_info
        
        # 注册回调
        if callback:
            self.add_task_callback(task_id, callback)
            
        # 创建并启动任务
        task = asyncio.create_task(self._execute_task(task_id, coro))
        self._running_tasks[task_id] = task
        
        logger.info(f"任务 {task_id} 已提交")
        return task_id
        
    async def _execute_task(self, task_id: str, coro: Callable):
        """执行任务"""
        task_info = self._tasks[task_id]
        
        try:
            # 更新状态
            task_info.status = TaskStatus.RUNNING
            task_info.started_at = datetime.now()
            
            # 执行协程
            if asyncio.iscoroutinefunction(coro):
                result = await coro()
            else:
                result = await asyncio.to_thread(coro)
                
            # 任务完成
            task_info.status = TaskStatus.COMPLETED
            task_info.completed_at = datetime.now()
            task_info.result = result
            task_info.progress = 100.0
            
            # 执行回调
            await self._execute_callbacks(task_id, task_info)
            
        except asyncio.CancelledError:
            task_info.status = TaskStatus.CANCELLED
            task_info.completed_at = datetime.now()
            logger.info(f"任务 {task_id} 被取消")
            
        except Exception as e:
            task_info.status = TaskStatus.FAILED
            task_info.completed_at = datetime.now()
            task_info.error_message = str(e)
            logger.error(f"任务 {task_id} 执行失败: {e}")
            
        finally:
            # 清理运行中的任务
            if task_id in self._running_tasks:
                del self._running_tasks[task_id]
                
    async def _execute_callbacks(self, task_id: str, task_info: TaskInfo):
        """执行任务回调"""
        # 执行任务特定回调
        if task_id in self._task_callbacks:
            for callback in self._task_callbacks[task_id]:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(task_info)
                    else:
                        callback(task_info)
                except Exception as e:
                    logger.error(f"回调执行失败: {e}")
                    
        # 执行全局回调
        for callback in self._global_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(task_info)
                else:
                    callback(task_info)
            except Exception as e:
                logger.error(f"全局回调执行失败: {e}")
                
    def add_task_callback(self, task_id: str, callback: Callable):
        """添加任务回调"""
        if task_id not in self._task_callbacks:
            self._task_callbacks[task_id] = []
        self._task_callbacks[task_id].append(callback)
        
    def add_global_callback(self, callback: Callable):
        """添加全局回调"""
        self._global_callbacks.append(callback)
        
    async def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        if task_id in self._running_tasks:
            task = self._running_tasks[task_id]
            task.cancel()
            
            try:
                await task
            except asyncio.CancelledError:
                pass
                
            logger.info(f"任务 {task_id} 已取消")
            return True
            
        return False
        
    def get_task_info(self, task_id: str) -> Optional[TaskInfo]:
        """获取任务信息"""
        return self._tasks.get(task_id)
        
    def get_all_tasks(self) -> Dict[str, TaskInfo]:
        """获取所有任务信息"""
        return self._tasks.copy()
        
    def get_running_tasks(self) -> List[str]:
        """获取运行中的任务ID列表"""
        return list(self._running_tasks.keys())
        
    async def wait_for_task(self, task_id: str, timeout: Optional[float] = None) -> TaskInfo:
        """等待任务完成"""
        if task_id not in self._tasks:
            raise ValueError(f"任务 {task_id} 不存在")
            
        if task_id in self._running_tasks:
            try:
                await asyncio.wait_for(self._running_tasks[task_id], timeout=timeout)
            except asyncio.TimeoutError:
                logger.warning(f"等待任务 {task_id} 超时")
                
        return self._tasks[task_id]
        
    async def shutdown(self):
        """关闭异步管理器"""
        logger.info("开始关闭异步管理器...")
        
        # 取消所有运行中的任务
        for task_id in list(self._running_tasks.keys()):
            await self.cancel_task(task_id)
            
        # 设置关闭事件
        self._shutdown_event.set()
        
        logger.info("异步管理器已关闭")
        
# 全局实例
_async_manager = None

def get_async_manager() -> UnifiedAsyncManager:
    """获取全局异步管理器实例"""
    global _async_manager
    if _async_manager is None:
        _async_manager = UnifiedAsyncManager()
    return _async_manager
'''
            
            # 写入异步管理器文件
            async_manager_file = self.project_root / 'src' / 'utils' / 'async_manager.py'
            with open(async_manager_file, 'w', encoding='utf-8') as f:
                f.write(async_manager_content)
                
            self.log_step("部署异步管理器", "SUCCESS", "统一异步管理器已部署")
            return True
            
        except Exception as e:
            self.log_step("部署异步管理器", "ERROR", str(e))
            return False
    
    def integrate_performance_monitor(self):
        """集成性能监控系统"""
        try:
            # 检查性能监控文件是否存在
            monitor_file = self.project_root / 'src' / 'utils' / 'performance_monitor.py'
            if monitor_file.exists():
                self.log_step("集成性能监控", "SUCCESS", "性能监控系统已存在")
                return True
            else:
                self.log_step("集成性能监控", "SKIP", "性能监控文件不存在")
                return True
                
        except Exception as e:
            self.log_step("集成性能监控", "ERROR", str(e))
            return False
    
    def optimize_file_tree(self):
        """优化文件树组件"""
        try:
            # 读取现有文件树代码
            file_tree_path = self.project_root / 'src' / 'ui' / 'file_tree.py'
            
            if not file_tree_path.exists():
                self.log_step("优化文件树", "SKIP", "文件树文件不存在")
                return True
                
            # 添加优化代码到文件树
            optimization_code = '''

# === 性能优化增强 ===

class FileTreeOptimizer:
    """文件树性能优化器"""
    
    def __init__(self):
        self.cache = {}
        self.folder_states = {}
        
    async def verify_folder_changes(self, folder_path: str) -> bool:
        """验证文件夹是否发生变化"""
        try:
            current_state = await self._calculate_folder_state(folder_path)
            cached_state = self.cache.get(folder_path)
            
            if not cached_state:
                self.cache[folder_path] = current_state
                return True
                
            # 双重验证
            hash_changed = current_state.get('hash') != cached_state.get('hash')
            size_changed = current_state.get('size') != cached_state.get('size')
            
            if hash_changed or size_changed:
                self.cache[folder_path] = current_state
                return True
                
            return False
            
        except Exception as e:
            logger.error(f"验证文件夹变化失败: {e}")
            return True  # 出错时默认需要更新
            
    async def _calculate_folder_state(self, folder_path: str) -> dict:
        """计算文件夹状态（只包含直接子文件，不包含子文件夹的递归大小）"""
        import os
        import hashlib
        
        total_size = 0
        file_count = 0
        hash_content = ""
        
        try:
            # 只遍历直接子项，不递归
            for item in os.listdir(folder_path):
                item_path = os.path.join(folder_path, item)
                if os.path.isfile(item_path):
                    try:
                        stat = os.stat(item_path)
                        total_size += stat.st_size
                        file_count += 1
                        hash_content += f"{item}:{stat.st_size}:{stat.st_mtime};"
                    except (OSError, IOError):
                        continue
                        
            folder_hash = hashlib.md5(hash_content.encode()).hexdigest()
            
            return {
                'hash': folder_hash,
                'size': total_size,
                'count': file_count,
                'timestamp': datetime.now().timestamp()
            }
            
        except Exception as e:
            logger.error(f"计算文件夹状态失败: {e}")
            return {'hash': '', 'size': 0, 'count': 0, 'timestamp': 0}
'''
            
            # 读取现有内容
            with open(file_tree_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 添加优化代码
            if "FileTreeOptimizer" not in content:
                content += optimization_code
                
                # 写回文件
                with open(file_tree_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                    
            self.log_step("优化文件树", "SUCCESS", "文件树组件已优化")
            return True
            
        except Exception as e:
            self.log_step("优化文件树", "ERROR", str(e))
            return False
    
    def deploy_cache_manager(self):
        """部署智能缓存管理"""
        try:
            cache_manager_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能缓存管理器

提供多级缓存、LRU策略、持久化等功能
"""

import os
import json
import pickle
import hashlib
import threading
from typing import Any, Optional, Dict, List
from datetime import datetime, timedelta
from pathlib import Path

class IntelligentCacheManager:
    """智能缓存管理器"""
    
    def __init__(self, cache_dir: str = "cache", max_size: int = 1000):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.max_size = max_size
        self.memory_cache = {}
        self.access_times = {}
        self.lock = threading.RLock()
        
    def get(self, key: str, default: Any = None) -> Any:
        """获取缓存值"""
        with self.lock:
            # 先检查内存缓存
            if key in self.memory_cache:
                self.access_times[key] = datetime.now()
                return self.memory_cache[key]
                
            # 检查磁盘缓存
            cache_file = self.cache_dir / f"{self._hash_key(key)}.cache"
            if cache_file.exists():
                try:
                    with open(cache_file, 'rb') as f:
                        data = pickle.load(f)
                        
                    # 检查过期时间
                    if 'expires_at' in data and datetime.now() > data['expires_at']:
                        cache_file.unlink()
                        return default
                        
                    value = data['value']
                    # 加载到内存缓存
                    self.memory_cache[key] = value
                    self.access_times[key] = datetime.now()
                    
                    return value
                    
                except Exception:
                    cache_file.unlink()
                    
            return default
            
    def set(self, key: str, value: Any, ttl: Optional[int] = None):
        """设置缓存值"""
        with self.lock:
            # 设置内存缓存
            self.memory_cache[key] = value
            self.access_times[key] = datetime.now()
            
            # 检查内存缓存大小
            if len(self.memory_cache) > self.max_size:
                self._evict_lru()
                
            # 持久化到磁盘
            cache_file = self.cache_dir / f"{self._hash_key(key)}.cache"
            data = {'value': value}
            
            if ttl:
                data['expires_at'] = datetime.now() + timedelta(seconds=ttl)
                
            try:
                with open(cache_file, 'wb') as f:
                    pickle.dump(data, f)
            except Exception as e:
                logger.error(f"缓存持久化失败: {e}")
                
    def _hash_key(self, key: str) -> str:
        """生成键的哈希值"""
        return hashlib.md5(key.encode()).hexdigest()
        
    def _evict_lru(self):
        """LRU淘汰策略"""
        if not self.access_times:
            return
            
        # 找到最久未访问的键
        lru_key = min(self.access_times.keys(), 
                     key=lambda k: self.access_times[k])
                     
        # 从内存中移除
        del self.memory_cache[lru_key]
        del self.access_times[lru_key]
        
    def clear(self):
        """清空所有缓存"""
        with self.lock:
            self.memory_cache.clear()
            self.access_times.clear()
            
            # 清空磁盘缓存
            for cache_file in self.cache_dir.glob("*.cache"):
                try:
                    cache_file.unlink()
                except Exception:
                    pass
                    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self.lock:
            disk_files = len(list(self.cache_dir.glob("*.cache")))
            
            return {
                'memory_items': len(self.memory_cache),
                'disk_items': disk_files,
                'total_items': len(self.memory_cache) + disk_files,
                'max_size': self.max_size
            }

# 全局缓存管理器实例
_cache_manager = None

def get_cache_manager() -> IntelligentCacheManager:
    """获取全局缓存管理器实例"""
    global _cache_manager
    if _cache_manager is None:
        _cache_manager = IntelligentCacheManager()
    return _cache_manager
'''
            
            # 创建缓存管理器文件
            cache_manager_file = self.project_root / 'src' / 'utils' / 'cache_manager.py'
            with open(cache_manager_file, 'w', encoding='utf-8') as f:
                f.write(cache_manager_content)
                
            self.log_step("部署缓存管理", "SUCCESS", "智能缓存管理器已部署")
            return True
            
        except Exception as e:
            self.log_step("部署缓存管理", "ERROR", str(e))
            return False
    
    def generate_report(self):
        """生成改造报告"""
        try:
            report_content = f"""
# 智能文件管理器性能优化改造执行报告

## 执行概要
- 执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 备份目录: {self.backup_dir}
- 总执行步骤: {len(self.execution_log)}

## 执行详情

"""
            
            # 添加执行日志
            for log_entry in self.execution_log:
                status_icon = "✅" if log_entry['status'] == "SUCCESS" else "❌" if log_entry['status'] == "ERROR" else "⏭️"
                report_content += f"""
### {status_icon} {log_entry['step']}
- 状态: {log_entry['status']}
- 时间: {log_entry['timestamp']}
- 详情: {log_entry['details']}

"""
            
            # 统计结果
            success_count = sum(1 for log in self.execution_log if log['status'] == 'SUCCESS')
            error_count = sum(1 for log in self.execution_log if log['status'] == 'ERROR')
            skip_count = sum(1 for log in self.execution_log if log['status'] == 'SKIP')
            
            report_content += f"""
## 执行统计
- ✅ 成功: {success_count} 项
- ❌ 失败: {error_count} 项  
- ⏭️ 跳过: {skip_count} 项
- 📊 成功率: {(success_count / len(self.execution_log) * 100):.1f}%

## 改造成果

### 🚀 性能提升
- 统一异步管理器: 提供统一的任务调度和监控
- 智能缓存系统: 多级缓存策略，提升数据访问速度
- 文件树优化: 双重验证机制，减少不必要的重建
- UI响应优化: 解决界面冻结问题

### 📈 监控能力
- 实时性能监控: CPU、内存、任务状态监控
- 性能报告生成: 自动生成性能分析报告
- 异常检测: 自动检测性能异常并报警

### 🛠️ 开发体验
- 模块化架构: 清晰的模块边界和职责分离
- 异步编程规范: 统一的异步任务管理模式
- 错误处理机制: 完善的异常捕获和恢复机制

## 后续建议

1. **性能测试**: 建议进行全面的性能基准测试
2. **用户反馈**: 收集用户使用反馈，持续优化
3. **监控部署**: 在生产环境中启用性能监控
4. **文档更新**: 更新开发文档和用户手册

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
            
            # 保存报告
            report_file = self.project_root / 'docs' / 'technical' / '改造执行报告.md'
            report_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report_content)
                
            self.log_step("生成报告", "SUCCESS", f"改造报告已生成: {report_file}")
            return True
            
        except Exception as e:
            self.log_step("生成报告", "ERROR", str(e))
            return False
    
    def execute_all(self):
        """执行完整的改造流程"""
        logger.info("开始执行智能文件管理器性能优化改造...")
        
        steps = [
            ("创建备份", self.create_backup),
            ("UI冻结修复", self.execute_ui_freeze_fix),
            ("部署异步管理器", self.deploy_async_manager),
            ("集成性能监控", self.integrate_performance_monitor),
            ("优化文件树", self.optimize_file_tree),
            ("部署缓存管理", self.deploy_cache_manager),
            ("生成报告", self.generate_report)
        ]
        
        success_count = 0
        for step_name, step_func in steps:
            logger.info(f"正在执行: {step_name}...")
            if step_func():
                success_count += 1
            else:
                logger.error(f"步骤失败: {step_name}")
                
        logger.info(f"改造完成! 成功执行 {success_count}/{len(steps)} 个步骤")
        
        # 显示最终结果
        print("\n" + "="*60)
        print("🎉 智能文件管理器性能优化改造完成!")
        print("="*60)
        print(f"✅ 成功步骤: {success_count}/{len(steps)}")
        print(f"📁 备份位置: {self.backup_dir}")
        print(f"📊 详细报告: docs/technical/改造执行报告.md")
        print("="*60)
        
        return success_count == len(steps)

def main():
    """主函数"""
    try:
        executor = RefactorExecutor()
        success = executor.execute_all()
        
        if success:
            print("\n🚀 所有改造步骤执行成功!")
            print("💡 建议重启应用程序以应用所有更改")
            return 0
        else:
            print("\n⚠️ 部分改造步骤执行失败，请查看日志")
            return 1
            
    except KeyboardInterrupt:
        print("\n❌ 改造过程被用户中断")
        return 1
    except Exception as e:
        logger.error(f"改造执行失败: {e}")
        print(f"\n❌ 改造执行失败: {e}")
        return 1

if __name__ == "__main__":
    exit(main())