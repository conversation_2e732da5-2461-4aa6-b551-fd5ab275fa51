#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库兼容性管理器

该模块实现数据库结构升级和向后兼容性管理:
- DatabaseCompatibilityManager: 数据库兼容性管理类
- 支持版本检查、结构升级、数据迁移
- 确保向后兼容性

作者: AI助手
日期: 2024-01-01
版本: 2.0.0
"""

import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime
from pathlib import Path
import hashlib
from src.utils.logger import get_logger
from src.data.models import FolderInfo, TreeNodeInfo


class DatabaseCompatibilityManager:
    """
    数据库向后兼容性管理器
    
    负责数据库结构升级、版本管理和数据迁移
    """

    def __init__(self, db_manager):
        """
        初始化兼容性管理器
        
        参数:
            db_manager: 数据库管理器实例
        """
        self.db_manager = db_manager
        self.logger = get_logger("DatabaseCompatibilityManager")
        self.current_version = "2.0.0"
        self.min_supported_version = "1.5.0"
        
        # 版本升级映射
        self.upgrade_handlers = {
            "1.5.0": self._upgrade_from_1_5_0,
            "1.6.0": self._upgrade_from_1_6_0,
            "1.9.0": self._upgrade_from_1_9_0
        }

    async def ensure_compatibility(self) -> bool:
        """
        确保数据库兼容性
        
        返回:
            bool: 是否成功确保兼容性
        """
        try:
            self.logger.info("开始检查数据库兼容性...")
            
            # 检查数据库版本
            db_version = await self._get_database_version()
            self.logger.info(f"当前数据库版本: {db_version or '未知'}")
            
            if not db_version:
                # 新数据库，直接创建新结构
                self.logger.info("检测到新数据库，创建最新结构...")
                await self._create_new_schema()
                await self._set_database_version(self.current_version)
                return True
            
            if db_version == self.current_version:
                # 版本匹配，检查结构完整性
                self.logger.info("数据库版本匹配，检查结构完整性...")
                return await self._verify_schema_integrity()
            
            # 需要升级
            if self._is_version_supported(db_version):
                self.logger.info(f"开始从版本 {db_version} 升级到 {self.current_version}...")
                return await self._upgrade_database(db_version)
            else:
                self.logger.error(f"不支持的数据库版本: {db_version}")
                return False
                
        except Exception as e:
            self.logger.error(f"数据库兼容性检查失败: {e}")
            return False

    async def _get_database_version(self) -> Optional[str]:
        """
        获取数据库版本
        
        返回:
            Optional[str]: 数据库版本号
        """
        try:
            # 检查是否存在版本信息集合
            collections = await self.db_manager.async_db.list_collection_names()
            if "db_version" not in collections:
                return None
            
            version_doc = await self.db_manager.async_db.db_version.find_one({})
            return version_doc.get("version") if version_doc else None
            
        except Exception as e:
            self.logger.warning(f"获取数据库版本失败: {e}")
            return None

    async def _set_database_version(self, version: str) -> bool:
        """
        设置数据库版本
        
        参数:
            version: 版本号
        返回:
            bool: 是否成功
        """
        try:
            await self.db_manager.async_db.db_version.replace_one(
                {},
                {
                    "version": version,
                    "updated_time": datetime.now(),
                    "schema_hash": await self._calculate_schema_hash()
                },
                upsert=True
            )
            self.logger.info(f"数据库版本已设置为: {version}")
            return True
            
        except Exception as e:
            self.logger.error(f"设置数据库版本失败: {e}")
            return False

    def _is_version_supported(self, version: str) -> bool:
        """
        检查版本是否支持升级
        
        参数:
            version: 版本号
        返回:
            bool: 是否支持
        """
        try:
            # 简单的版本比较（假设版本格式为 x.y.z）
            current_parts = [int(x) for x in self.current_version.split(".")]
            min_parts = [int(x) for x in self.min_supported_version.split(".")]
            version_parts = [int(x) for x in version.split(".")]
            
            return version_parts >= min_parts and version_parts <= current_parts
            
        except Exception:
            return False

    async def _create_new_schema(self) -> bool:
        """
        创建新的数据库结构
        
        返回:
            bool: 是否成功
        """
        try:
            self.logger.info("创建新的数据库结构...")
            
            # 创建folders集合索引
            await self._create_folders_indexes()
            
            # 创建tree_nodes集合索引
            await self._create_tree_nodes_indexes()
            
            # 更新files集合结构
            await self._update_files_collection_schema()
            
            self.logger.info("新数据库结构创建完成")
            return True
            
        except Exception as e:
            self.logger.error(f"创建新数据库结构失败: {e}")
            return False

    async def _create_folders_indexes(self) -> bool:
        """
        创建folders集合索引
        
        返回:
            bool: 是否成功
        """
        try:
            folders_collection = self.db_manager.async_db.folders
            
            # 创建索引
            indexes = [
                (["folder_id"], {"unique": True}),  # 唯一索引
                (["path"], {"unique": True}),       # 路径唯一索引
                (["depth", "parent_folder_id"], {}), # 复合索引
                (["content_hash"], {}),             # hash索引
                (["is_network_folder"], {}),        # 网络文件夹索引
                (["scan_status"], {}),              # 扫描状态索引
                (["last_scan_time"], {})            # 扫描时间索引
            ]
            
            for keys, options in indexes:
                await folders_collection.create_index(keys, **options)
                self.logger.debug(f"创建folders集合索引: {keys}")
            
            self.logger.info("folders集合索引创建完成")
            return True
            
        except Exception as e:
            self.logger.error(f"创建folders集合索引失败: {e}")
            return False

    async def _create_tree_nodes_indexes(self) -> bool:
        """
        创建tree_nodes集合索引
        
        返回:
            bool: 是否成功
        """
        try:
            tree_nodes_collection = self.db_manager.async_db.tree_nodes
            
            # 创建索引
            indexes = [
                (["node_id"], {"unique": True}),    # 唯一索引
                (["path"], {"unique": True, "sparse": True}), # 路径索引（虚拟节点可能无路径）
                (["parent_node_id", "depth"], {}),  # 复合索引
                (["type", "depth"], {}),            # 类型深度索引
                (["is_virtual"], {}),               # 虚拟节点索引
                (["load_status"], {})               # 加载状态索引
            ]
            
            for keys, options in indexes:
                await tree_nodes_collection.create_index(keys, **options)
                self.logger.debug(f"创建tree_nodes集合索引: {keys}")
            
            self.logger.info("tree_nodes集合索引创建完成")
            return True
            
        except Exception as e:
            self.logger.error(f"创建tree_nodes集合索引失败: {e}")
            return False

    async def _update_files_collection_schema(self) -> bool:
        """
        更新files集合结构（保持向后兼容）
        
        返回:
            bool: 是否成功
        """
        try:
            files_collection = self.db_manager.async_db.files
            
            # 添加新字段的索引
            new_indexes = [
                (["folder_id"], {}),        # 文件夹ID索引
                (["tree_node_id"], {}),     # 树节点ID索引
                (["relative_path"], {})     # 相对路径索引
            ]
            
            for keys, options in new_indexes:
                try:
                    await files_collection.create_index(keys, **options)
                    self.logger.debug(f"创建files集合新索引: {keys}")
                except Exception as e:
                    # 索引可能已存在，忽略错误
                    self.logger.debug(f"索引 {keys} 可能已存在: {e}")
            
            self.logger.info("files集合结构更新完成")
            return True
            
        except Exception as e:
            self.logger.error(f"更新files集合结构失败: {e}")
            return False

    async def _verify_schema_integrity(self) -> bool:
        """
        验证数据库结构完整性
        
        返回:
            bool: 结构是否完整
        """
        try:
            self.logger.info("验证数据库结构完整性...")
            
            # 检查必要的集合是否存在
            collections = await self.db_manager.async_db.list_collection_names()
            required_collections = ["files", "folders", "tree_nodes", "db_version"]
            
            for collection in required_collections:
                if collection not in collections:
                    self.logger.warning(f"缺少必要的集合: {collection}")
                    # 尝试创建缺少的结构
                    if collection == "folders":
                        await self._create_folders_indexes()
                    elif collection == "tree_nodes":
                        await self._create_tree_nodes_indexes()
                    elif collection == "db_version":
                        await self._set_database_version(self.current_version)
            
            self.logger.info("数据库结构完整性验证完成")
            return True
            
        except Exception as e:
            self.logger.error(f"数据库结构完整性验证失败: {e}")
            return False

    async def _upgrade_database(self, from_version: str) -> bool:
        """
        升级数据库
        
        参数:
            from_version: 源版本
        返回:
            bool: 是否成功
        """
        try:
            self.logger.info(f"开始数据库升级: {from_version} -> {self.current_version}")
            
            # 执行相应的升级处理器
            if from_version in self.upgrade_handlers:
                success = await self.upgrade_handlers[from_version]()
                if not success:
                    self.logger.error(f"升级处理器执行失败: {from_version}")
                    return False
            
            # 确保最新结构
            await self._create_new_schema()
            
            # 更新版本信息
            await self._set_database_version(self.current_version)
            
            self.logger.info("数据库升级完成")
            return True
            
        except Exception as e:
            self.logger.error(f"数据库升级失败: {e}")
            return False

    async def _upgrade_from_1_5_0(self) -> bool:
        """
        从版本1.5.0升级
        
        返回:
            bool: 是否成功
        """
        try:
            self.logger.info("执行从1.5.0版本的升级...")
            
            # 迁移现有文件数据到新结构
            await self._migrate_files_to_new_structure()
            
            return True
            
        except Exception as e:
            self.logger.error(f"从1.5.0版本升级失败: {e}")
            return False

    async def _upgrade_from_1_6_0(self) -> bool:
        """
        从版本1.6.0升级
        
        返回:
            bool: 是否成功
        """
        try:
            self.logger.info("执行从1.6.0版本的升级...")
            
            # 添加特定的升级逻辑
            await self._migrate_files_to_new_structure()
            
            return True
            
        except Exception as e:
            self.logger.error(f"从1.6.0版本升级失败: {e}")
            return False

    async def _upgrade_from_1_9_0(self) -> bool:
        """
        从版本1.9.0升级
        
        返回:
            bool: 是否成功
        """
        try:
            self.logger.info("执行从1.9.0版本的升级...")
            
            # 添加特定的升级逻辑
            await self._migrate_files_to_new_structure()
            
            return True
            
        except Exception as e:
            self.logger.error(f"从1.9.0版本升级失败: {e}")
            return False

    async def _migrate_files_to_new_structure(self) -> bool:
        """
        迁移现有文件数据到新结构
        
        返回:
            bool: 是否成功
        """
        try:
            self.logger.info("开始迁移文件数据到新结构...")
            
            # 获取所有现有文件
            files_collection = self.db_manager.async_db.files
            folders_collection = self.db_manager.async_db.folders
            tree_nodes_collection = self.db_manager.async_db.tree_nodes
            
            # 批量处理文件
            batch_size = 1000
            processed_count = 0
            folder_cache = {}  # 文件夹缓存
            
            async for file_doc in files_collection.find({}):
                try:
                    file_path = file_doc.get("path") or file_doc.get("file_path")
                    if not file_path:
                        continue
                    
                    # 获取文件夹路径
                    folder_path = str(Path(file_path).parent)
                    
                    # 创建或获取文件夹信息
                    if folder_path not in folder_cache:
                        folder_info = await self._create_or_get_folder(folder_path, folders_collection)
                        if folder_info:
                            folder_cache[folder_path] = folder_info
                    
                    # 更新文件记录
                    if folder_path in folder_cache:
                        folder_info = folder_cache[folder_path]
                        relative_path = Path(file_path).name
                        
                        await files_collection.update_one(
                            {"_id": file_doc["_id"]},
                            {
                                "$set": {
                                    "folder_id": folder_info["folder_id"],
                                    "relative_path": relative_path
                                }
                            }
                        )
                    
                    processed_count += 1
                    if processed_count % batch_size == 0:
                        self.logger.info(f"已处理 {processed_count} 个文件")
                        
                except Exception as e:
                    self.logger.warning(f"处理文件失败 {file_doc.get('path', 'unknown')}: {e}")
                    continue
            
            self.logger.info(f"文件数据迁移完成，共处理 {processed_count} 个文件")
            return True
            
        except Exception as e:
            self.logger.error(f"文件数据迁移失败: {e}")
            return False

    async def _create_or_get_folder(self, folder_path: str, folders_collection) -> Optional[Dict[str, Any]]:
        """
        创建或获取文件夹信息
        
        参数:
            folder_path: 文件夹路径
            folders_collection: 文件夹集合
        返回:
            Optional[Dict[str, Any]]: 文件夹信息
        """
        try:
            # 检查是否已存在
            existing_folder = await folders_collection.find_one({"path": folder_path})
            if existing_folder:
                return existing_folder
            
            # 创建新文件夹信息
            folder_info = FolderInfo.from_path(folder_path)
            folder_dict = folder_info.to_dict()
            
            # 插入数据库
            await folders_collection.insert_one(folder_dict)
            
            return folder_dict
            
        except Exception as e:
            self.logger.warning(f"创建文件夹信息失败 {folder_path}: {e}")
            return None

    async def _calculate_schema_hash(self) -> str:
        """
        计算数据库结构哈希
        
        返回:
            str: 结构哈希值
        """
        try:
            # 获取所有集合的索引信息
            collections = await self.db_manager.async_db.list_collection_names()
            schema_info = {}
            
            for collection_name in collections:
                if collection_name.startswith("system."):
                    continue
                    
                collection = self.db_manager.async_db[collection_name]
                indexes = await collection.list_indexes().to_list(length=None)
                schema_info[collection_name] = [idx.get("key", {}) for idx in indexes]
            
            # 计算哈希
            schema_str = str(sorted(schema_info.items()))
            return hashlib.md5(schema_str.encode()).hexdigest()
            
        except Exception as e:
            self.logger.warning(f"计算结构哈希失败: {e}")
            return "unknown"