#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
统一进度管理系统 - 重构版本

该模块统一管理所有进度跟踪和状态更新，消除重复代码：
1. 统一的进度跟踪器和状态管理器
2. 标准化的进度回调和事件通知逻辑
3. 优化的进度显示和用户反馈机制
4. 统一的任务生命周期管理和状态同步

重构特性：
- 消除了多个重复的进度跟踪器实现
- 统一了进度回调函数和事件处理
- 标准化了任务状态管理和进度显示
- 提供了统一的进度监控和报告机制

作者: SmartFileManger开发团队
日期: 2025-07-27
版本: 3.0.0 (重构版)
"""

import time
import asyncio
import threading
from typing import Dict, List, Any, Optional, Callable, Union
from dataclasses import dataclass, field
from enum import Enum
from abc import ABC, abstractmethod

from ..utils.logger import get_logger

logger = get_logger(__name__)


class UnifiedTaskType(Enum):
    """统一任务类型枚举"""
    FILE_SCAN = "file_scan"
    FILE_TREE_LOAD = "file_tree_load"
    DUPLICATE_FIND = "duplicate_find"
    JUNK_FIND = "junk_find"
    WHITELIST_FIND = "whitelist_find"
    HASH_CALCULATION = "hash_calculation"
    FILE_OPERATION = "file_operation"
    DATABASE_OPERATION = "database_operation"
    QUERY_OPERATION = "query_operation"
    GENERAL = "general"


class UnifiedTaskStatus(Enum):
    """统一任务状态枚举"""
    NOT_STARTED = "not_started"
    INITIALIZING = "initializing"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    TIMEOUT = "timeout"


@dataclass
class UnifiedProgressInfo:
    """统一进度信息"""
    task_id: str
    task_type: UnifiedTaskType
    task_name: str
    status: UnifiedTaskStatus = UnifiedTaskStatus.NOT_STARTED
    progress: float = 0.0  # 0-100
    current: int = 0
    total: int = 0
    current_item: str = ""
    status_message: str = ""
    details: str = ""
    stage: str = ""
    start_time: float = field(default_factory=time.time)
    last_update: float = field(default_factory=time.time)
    end_time: Optional[float] = None
    pause_time: Optional[float] = None
    total_pause_duration: float = 0.0
    estimated_remaining: float = 0.0
    processing_rate: float = 0.0
    error_message: str = ""
    
    @property
    def elapsed_time(self) -> float:
        """计算已用时间（排除暂停时间）"""
        if self.end_time:
            return self.end_time - self.start_time - self.total_pause_duration
        return time.time() - self.start_time - self.total_pause_duration
    
    @property
    def is_active(self) -> bool:
        """是否为活跃状态"""
        return self.status in [UnifiedTaskStatus.RUNNING, UnifiedTaskStatus.INITIALIZING]
    
    @property
    def is_finished(self) -> bool:
        """是否已完成"""
        return self.status in [
            UnifiedTaskStatus.COMPLETED, 
            UnifiedTaskStatus.FAILED, 
            UnifiedTaskStatus.CANCELLED,
            UnifiedTaskStatus.TIMEOUT
        ]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'task_id': self.task_id,
            'task_type': self.task_type.value,
            'task_name': self.task_name,
            'status': self.status.value,
            'progress': self.progress,
            'current': self.current,
            'total': self.total,
            'current_item': self.current_item,
            'status_message': self.status_message,
            'details': self.details,
            'stage': self.stage,
            'elapsed_time': self.elapsed_time,
            'estimated_remaining': self.estimated_remaining,
            'processing_rate': self.processing_rate,
            'error_message': self.error_message
        }


class ProgressCallbackInterface(ABC):
    """进度回调接口"""
    
    @abstractmethod
    def on_progress_update(self, progress_info: UnifiedProgressInfo) -> None:
        """进度更新回调"""
        pass
    
    @abstractmethod
    def on_task_complete(self, progress_info: UnifiedProgressInfo) -> None:
        """任务完成回调"""
        pass


class UnifiedProgressSystem:
    """
    统一进度管理系统
    
    提供标准化的进度跟踪、状态管理和回调通知功能
    """
    
    def __init__(self, enable_debounce: bool = True, update_interval: float = 0.1):
        """
        初始化统一进度系统
        
        参数:
            enable_debounce: 是否启用防抖
            update_interval: 更新间隔（秒）
        """
        self.enable_debounce = enable_debounce
        self.update_interval = update_interval
        
        # 任务存储
        self._tasks: Dict[str, UnifiedProgressInfo] = {}
        self._callbacks: Dict[str, List[ProgressCallbackInterface]] = {}
        self._global_callbacks: List[ProgressCallbackInterface] = []
        
        # 防抖控制
        self._last_update_times: Dict[str, float] = {}
        
        # 线程安全
        self._lock = threading.RLock()
        
        logger.info(f"统一进度系统初始化完成，防抖: {enable_debounce}, 更新间隔: {update_interval}s")

    # ==================== 任务管理 ====================
    
    def register_task(self, task_id: str, task_type: UnifiedTaskType, 
                     task_name: str, total: int = 0) -> UnifiedProgressInfo:
        """
        注册新任务
        
        参数:
            task_id: 任务ID
            task_type: 任务类型
            task_name: 任务名称
            total: 总项目数
            
        返回:
            UnifiedProgressInfo: 任务进度信息
        """
        with self._lock:
            progress_info = UnifiedProgressInfo(
                task_id=task_id,
                task_type=task_type,
                task_name=task_name,
                total=total,
                status=UnifiedTaskStatus.NOT_STARTED
            )
            
            self._tasks[task_id] = progress_info
            self._callbacks[task_id] = []
            
            logger.info(f"注册任务: {task_id} - {task_name}")
            self._notify_callbacks(progress_info)
            
            return progress_info

    def start_task(self, task_id: str) -> bool:
        """
        开始任务
        
        参数:
            task_id: 任务ID
            
        返回:
            bool: 是否成功开始
        """
        with self._lock:
            if task_id not in self._tasks:
                logger.error(f"任务不存在: {task_id}")
                return False
            
            task = self._tasks[task_id]
            task.status = UnifiedTaskStatus.RUNNING
            task.start_time = time.time()
            task.last_update = task.start_time
            
            logger.info(f"开始任务: {task_id}")
            self._notify_callbacks(task)
            
            return True

    def update_progress(self, task_id: str, current: Optional[int] = None, 
                       progress: Optional[float] = None, current_item: str = "",
                       status_message: str = "", details: str = "", 
                       stage: str = "") -> bool:
        """
        更新任务进度
        
        参数:
            task_id: 任务ID
            current: 当前完成数量
            progress: 当前进度百分比(0-100)
            current_item: 当前处理项目
            status_message: 状态消息
            details: 详细信息
            stage: 当前阶段
            
        返回:
            bool: 是否成功更新
        """
        with self._lock:
            if task_id not in self._tasks:
                logger.warning(f"任务不存在: {task_id}")
                return False
            
            task = self._tasks[task_id]
            
            # 检查防抖
            if self.enable_debounce and self._should_debounce(task_id):
                return True
            
            # 更新进度信息
            if current is not None:
                task.current = current
                if task.total > 0:
                    task.progress = (current / task.total) * 100
            
            if progress is not None:
                task.progress = max(0, min(100, progress))
            
            if current_item:
                task.current_item = current_item
            
            if status_message:
                task.status_message = status_message
            
            if details:
                task.details = details
            
            if stage:
                task.stage = stage
            
            # 更新时间和速率
            current_time = time.time()
            time_diff = current_time - task.last_update
            
            if time_diff > 0 and current is not None:
                items_processed = current - (task.current if current else 0)
                task.processing_rate = items_processed / time_diff
                
                # 估算剩余时间
                if task.processing_rate > 0 and task.total > 0:
                    remaining_items = task.total - task.current
                    task.estimated_remaining = remaining_items / task.processing_rate
            
            task.last_update = current_time
            self._last_update_times[task_id] = current_time
            
            # 通知回调
            self._notify_callbacks(task)
            
            return True

    def complete_task(self, task_id: str, success: bool = True, 
                     message: str = "") -> bool:
        """
        完成任务
        
        参数:
            task_id: 任务ID
            success: 是否成功完成
            message: 完成消息
            
        返回:
            bool: 是否成功完成
        """
        with self._lock:
            if task_id not in self._tasks:
                logger.warning(f"任务不存在: {task_id}")
                return False
            
            task = self._tasks[task_id]
            task.status = UnifiedTaskStatus.COMPLETED if success else UnifiedTaskStatus.FAILED
            task.progress = 100.0 if success else task.progress
            task.end_time = time.time()
            
            if message:
                task.status_message = message
            
            if not success and message:
                task.error_message = message
            
            logger.info(f"任务完成: {task_id} - 成功: {success}")
            
            # 通知回调
            self._notify_callbacks(task)
            for callback in self._callbacks.get(task_id, []):
                callback.on_task_complete(task)
            for callback in self._global_callbacks:
                callback.on_task_complete(task)
            
            return True

    def cancel_task(self, task_id: str, reason: str = "") -> bool:
        """
        取消任务
        
        参数:
            task_id: 任务ID
            reason: 取消原因
            
        返回:
            bool: 是否成功取消
        """
        with self._lock:
            if task_id not in self._tasks:
                logger.warning(f"任务不存在: {task_id}")
                return False
            
            task = self._tasks[task_id]
            task.status = UnifiedTaskStatus.CANCELLED
            task.end_time = time.time()
            
            if reason:
                task.error_message = reason
                task.status_message = f"已取消: {reason}"
            
            logger.info(f"任务取消: {task_id} - 原因: {reason}")
            self._notify_callbacks(task)
            
            return True

    # ==================== 回调管理 ====================
    
    def add_task_callback(self, task_id: str, callback: ProgressCallbackInterface) -> None:
        """添加任务特定回调"""
        with self._lock:
            if task_id not in self._callbacks:
                self._callbacks[task_id] = []
            self._callbacks[task_id].append(callback)

    def add_global_callback(self, callback: ProgressCallbackInterface) -> None:
        """添加全局回调"""
        with self._lock:
            self._global_callbacks.append(callback)

    def remove_task_callback(self, task_id: str, callback: ProgressCallbackInterface) -> None:
        """移除任务特定回调"""
        with self._lock:
            if task_id in self._callbacks and callback in self._callbacks[task_id]:
                self._callbacks[task_id].remove(callback)

    def remove_global_callback(self, callback: ProgressCallbackInterface) -> None:
        """移除全局回调"""
        with self._lock:
            if callback in self._global_callbacks:
                self._global_callbacks.remove(callback)

    # ==================== 查询方法 ====================
    
    def get_task(self, task_id: str) -> Optional[UnifiedProgressInfo]:
        """获取任务信息"""
        with self._lock:
            return self._tasks.get(task_id)

    def get_all_tasks(self) -> Dict[str, UnifiedProgressInfo]:
        """获取所有任务信息"""
        with self._lock:
            return self._tasks.copy()

    def get_active_tasks(self) -> Dict[str, UnifiedProgressInfo]:
        """获取活跃任务"""
        with self._lock:
            return {tid: task for tid, task in self._tasks.items() if task.is_active}

    def get_finished_tasks(self) -> Dict[str, UnifiedProgressInfo]:
        """获取已完成任务"""
        with self._lock:
            return {tid: task for tid, task in self._tasks.items() if task.is_finished}

    # ==================== 内部方法 ====================
    
    def _should_debounce(self, task_id: str) -> bool:
        """检查是否应该防抖"""
        if not self.enable_debounce:
            return False
        
        last_update = self._last_update_times.get(task_id, 0)
        return time.time() - last_update < self.update_interval

    def _notify_callbacks(self, progress_info: UnifiedProgressInfo) -> None:
        """通知回调函数"""
        try:
            # 任务特定回调
            for callback in self._callbacks.get(progress_info.task_id, []):
                callback.on_progress_update(progress_info)
            
            # 全局回调
            for callback in self._global_callbacks:
                callback.on_progress_update(progress_info)
                
        except Exception as e:
            logger.error(f"回调通知失败: {e}")

    def clear_finished_tasks(self, older_than_seconds: float = 3600) -> int:
        """清理已完成的任务"""
        with self._lock:
            current_time = time.time()
            to_remove = []
            
            for task_id, task in self._tasks.items():
                if (task.is_finished and task.end_time and 
                    current_time - task.end_time > older_than_seconds):
                    to_remove.append(task_id)
            
            for task_id in to_remove:
                del self._tasks[task_id]
                if task_id in self._callbacks:
                    del self._callbacks[task_id]
                if task_id in self._last_update_times:
                    del self._last_update_times[task_id]
            
            logger.info(f"清理了 {len(to_remove)} 个已完成任务")
            return len(to_remove)


# ==================== 适配器类 ====================

class LegacyProgressAdapter(ProgressCallbackInterface):
    """
    旧版进度管理器适配器

    用于将旧版进度回调适配到新的统一系统
    """

    def __init__(self, legacy_callback: Optional[Callable] = None):
        """
        初始化适配器

        参数:
            legacy_callback: 旧版回调函数
        """
        self.legacy_callback = legacy_callback

    def on_progress_update(self, progress_info: UnifiedProgressInfo) -> None:
        """进度更新回调适配"""
        if self.legacy_callback:
            try:
                # 适配不同的旧版回调格式
                if hasattr(self.legacy_callback, '__code__'):
                    arg_count = self.legacy_callback.__code__.co_argcount

                    if arg_count == 1:
                        # 单参数回调：progress_info
                        self.legacy_callback(progress_info.to_dict())
                    elif arg_count == 2:
                        # 双参数回调：progress, message
                        self.legacy_callback(progress_info.progress, progress_info.status_message)
                    elif arg_count >= 3:
                        # 多参数回调：progress, message, details, ...
                        self.legacy_callback(
                            progress_info.progress,
                            progress_info.status_message,
                            progress_info.details,
                            progress_info.current,
                            progress_info.total
                        )
                else:
                    # 默认调用方式
                    self.legacy_callback(progress_info.progress, progress_info.status_message)

            except Exception as e:
                logger.error(f"旧版进度回调适配失败: {e}")

    def on_task_complete(self, progress_info: UnifiedProgressInfo) -> None:
        """任务完成回调适配"""
        # 对于旧版回调，任务完成时也调用进度更新
        self.on_progress_update(progress_info)


class UnifiedProgressManagerAdapter:
    """
    统一进度管理器适配器

    提供与旧版UnifiedProgressManager兼容的接口
    """

    def __init__(self):
        """初始化适配器"""
        self.progress_system = get_unified_progress_system()

    def register_task(self, task_id: str, task_type: Any, task_name: str,
                     total: int = 0, callback: Optional[Callable] = None) -> Any:
        """注册任务 - 兼容旧版接口"""
        # 转换任务类型
        if hasattr(task_type, 'value'):
            unified_type = self._convert_task_type(task_type.value)
        else:
            unified_type = self._convert_task_type(str(task_type))

        # 注册任务
        progress_info = self.progress_system.register_task(task_id, unified_type, task_name, total)

        # 添加回调
        if callback:
            adapter = LegacyProgressAdapter(callback)
            self.progress_system.add_task_callback(task_id, adapter)

        return progress_info

    def start_task(self, task_id: str) -> bool:
        """开始任务 - 兼容旧版接口"""
        return self.progress_system.start_task(task_id)

    def update_progress(self, task_id: str, current: Optional[int] = None,
                       progress: Optional[float] = None, current_item: str = "",
                       status_message: str = "", details: str = "") -> bool:
        """更新进度 - 兼容旧版接口"""
        return self.progress_system.update_progress(
            task_id=task_id,
            current=current,
            progress=progress,
            current_item=current_item,
            status_message=status_message,
            details=details
        )

    def complete_task(self, task_id: str, success: bool = True, message: str = "") -> bool:
        """完成任务 - 兼容旧版接口"""
        return self.progress_system.complete_task(task_id, success, message)

    def cancel_task(self, task_id: str, reason: str = "") -> bool:
        """取消任务 - 兼容旧版接口"""
        return self.progress_system.cancel_task(task_id, reason)

    def get_task(self, task_id: str) -> Optional[Any]:
        """获取任务 - 兼容旧版接口"""
        return self.progress_system.get_task(task_id)

    def _convert_task_type(self, task_type_str: str) -> UnifiedTaskType:
        """转换任务类型"""
        type_mapping = {
            'file_scan': UnifiedTaskType.FILE_SCAN,
            'file_tree_load': UnifiedTaskType.FILE_TREE_LOAD,
            'duplicate_find': UnifiedTaskType.DUPLICATE_FIND,
            'junk_find': UnifiedTaskType.JUNK_FIND,
            'whitelist_find': UnifiedTaskType.WHITELIST_FIND,
            'hash_calculation': UnifiedTaskType.HASH_CALCULATION,
            'file_operation': UnifiedTaskType.FILE_OPERATION,
            'database_operation': UnifiedTaskType.DATABASE_OPERATION,
            'query_operation': UnifiedTaskType.QUERY_OPERATION,
        }
        return type_mapping.get(task_type_str, UnifiedTaskType.GENERAL)


# ==================== 全局实例 ====================

# 创建全局统一进度系统实例
_unified_progress_system = None
_unified_progress_manager_adapter = None

def get_unified_progress_system() -> UnifiedProgressSystem:
    """获取全局统一进度系统实例"""
    global _unified_progress_system
    if _unified_progress_system is None:
        _unified_progress_system = UnifiedProgressSystem()
    return _unified_progress_system

def get_unified_progress_manager() -> UnifiedProgressManagerAdapter:
    """获取统一进度管理器适配器实例（向后兼容）"""
    global _unified_progress_manager_adapter
    if _unified_progress_manager_adapter is None:
        _unified_progress_manager_adapter = UnifiedProgressManagerAdapter()
    return _unified_progress_manager_adapter


# ==================== 向后兼容性函数 ====================

def get_progress_manager():
    """向后兼容的进度管理器获取函数"""
    return get_unified_progress_manager()
