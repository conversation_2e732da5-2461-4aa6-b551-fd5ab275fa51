# 文件树优化配置文件
# Tree Optimization Configuration

# 树优化参数
tree_optimization:
  # 最大扫描深度
  max_depth: 10
  
  # 批处理大小
  batch_size: 1000
  
  # 缓存大小（节点数量）
  cache_size: 10000
  
  # 网络文件夹超时时间（秒）
  network_timeout: 30
  
  # 智能展开阈值（子项数量）
  smart_expand_threshold: 100
  
  # 虚拟节点启用
  enable_virtual_nodes: true
  
  # 内容哈希验证
  enable_content_hash: true
  
  # 双重验证模式
  dual_verification: true

# 数据库配置
database:
  # 连接池大小
  connection_pool_size: 10
  
  # 连接超时时间（秒）
  connection_timeout: 30
  
  # 查询超时时间（秒）
  query_timeout: 60
  
  # 批量操作大小
  bulk_operation_size: 500
  
  # 索引创建超时（秒）
  index_creation_timeout: 300
  
  # 启用查询缓存
  enable_query_cache: true
  
  # 缓存过期时间（秒）
  cache_expiry: 3600

# 性能优化配置
performance:
  # 异步处理启用
  enable_async: true
  
  # 工作线程数量
  worker_threads: 4
  
  # 内存使用限制（MB）
  memory_limit: 1024
  
  # 磁盘缓存启用
  enable_disk_cache: true
  
  # 磁盘缓存大小（MB）
  disk_cache_size: 512
  
  # 压缩启用
  enable_compression: true
  
  # 预加载启用
  enable_preload: true
  
  # 预加载深度
  preload_depth: 3

# UI配置
ui:
  # 延迟加载启用
  enable_lazy_loading: true
  
  # 虚拟滚动启用
  enable_virtual_scrolling: true
  
  # 节点展开动画时间（毫秒）
  expand_animation_duration: 300
  
  # 最大显示节点数
  max_display_nodes: 5000
  
  # 搜索结果限制
  search_result_limit: 1000
  
  # 自动刷新间隔（秒）
  auto_refresh_interval: 300

# 网络文件夹配置
network_folders:
  # 启用网络文件夹支持
  enable_network_support: true
  
  # 网络扫描超时（秒）
  scan_timeout: 60
  
  # 重试次数
  retry_count: 3
  
  # 重试间隔（秒）
  retry_interval: 5
  
  # 缓存网络路径
  cache_network_paths: true
  
  # 网络路径缓存时间（秒）
  network_cache_duration: 1800

# 监控和日志配置
monitoring:
  # 启用性能监控
  enable_performance_monitoring: true
  
  # 监控间隔（秒）
  monitoring_interval: 60
  
  # 日志级别 (DEBUG, INFO, WARNING, ERROR)
  log_level: INFO
  
  # 启用详细日志
  enable_verbose_logging: false
  
  # 日志文件大小限制（MB）
  log_file_size_limit: 100
  
  # 保留日志文件数量
  log_file_retention: 10

# 兼容性配置
compatibility:
  # 数据库版本
  database_version: "2.0.0"
  
  # 支持的最低版本
  minimum_supported_version: "1.5.0"
  
  # 启用向后兼容
  enable_backward_compatibility: true
  
  # 自动迁移
  enable_auto_migration: true
  
  # 迁移备份
  enable_migration_backup: true

# 安全配置
security:
  # 启用路径验证
  enable_path_validation: true
  
  # 允许的路径前缀
  allowed_path_prefixes:
    - "C:\\"
    - "D:\\"
    - "E:\\"
    - "\\\\"  # 网络路径
  
  # 禁止的路径模式
  forbidden_path_patterns:
    - "**/System32/**"
    - "**/Windows/System/**"
    - "**/.git/**"
    - "**/node_modules/**"
  
  # 最大路径长度
  max_path_length: 260
  
  # 启用文件类型过滤
  enable_file_type_filtering: true