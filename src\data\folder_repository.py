#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
文件夹数据访问层

该模块实现文件夹数据的数据库访问功能:
- FolderRepository: 文件夹数据访问类
- 支持CRUD操作、批量操作和复杂查询
- 集成现有数据库管理器
- 实现异步操作和错误处理

作者: AI助手
日期: 2024-01-01
版本: 1.0.0
"""

import asyncio
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime
from src.utils.logger import get_logger
from src.data.dto.folder_dto import FolderDTO
from src.core.tree_optimization_manager import FolderInfo
from src.utils.format_utils import _normalize_path

logger = get_logger(__name__)


class FolderRepository:
    """
    文件夹数据访问层
    
    提供文件夹数据的数据库访问功能，支持新的文件夹为核心的架构
    """
    
    def __init__(self, db_manager):
        """
        初始化文件夹数据访问层
        
        参数:
            db_manager: 数据库管理器实例
        """
        self.db_manager = db_manager
        self.logger = logger
        
        # 集合名称
        self.folders_collection_name = "folders"
        self.files_collection_name = "files"
        
        # 确保索引存在
        asyncio.create_task(self._ensure_indexes())
    
    async def _ensure_indexes(self) -> None:
        """确保必要的索引存在"""
        try:
            if not hasattr(self.db_manager, 'async_db'):
                self.logger.warning("数据库管理器不支持异步操作，跳过索引创建")
                return
            
            folders_collection = self.db_manager.async_db[self.folders_collection_name]
            
            # 创建folders集合索引
            indexes = [
                (["folder_id"], {"unique": True}),                    # 唯一索引
                (["path"], {"unique": True}),                         # 路径唯一索引
                (["parent_id", "depth"], {}),                         # 父子关系查询
                (["depth", "scan_status"], {}),                       # 按深度和状态查询
                (["files_hash"], {}),                                 # 哈希查询
                (["last_scan_time"], {}),                             # 扫描时间查询
                (["is_network_folder"], {}),                          # 网络文件夹查询
                (["parent_id", "name"], {}),                          # 父目录下的文件夹名查询
            ]
            
            for keys, options in indexes:
                try:
                    await folders_collection.create_index(keys, **options)
                    self.logger.debug(f"创建folders集合索引: {keys}")
                except Exception as e:
                    # 索引可能已存在，忽略错误
                    self.logger.debug(f"索引 {keys} 可能已存在: {e}")
            
            self.logger.info("folders集合索引创建完成")
            
        except Exception as e:
            self.logger.error(f"创建索引失败: {e}")
    
    async def create_folder(self, folder_dto: FolderDTO) -> bool:
        """
        创建文件夹记录
        
        参数:
            folder_dto: 文件夹DTO对象
        
        返回:
            是否创建成功
        """
        try:
            if not hasattr(self.db_manager, 'async_db'):
                return await self._create_folder_sync(folder_dto)
            
            folders_collection = self.db_manager.async_db[self.folders_collection_name]
            
            # 转换为字典格式
            folder_data = folder_dto.to_dict()
            
            # 使用upsert操作
            result = await folders_collection.replace_one(
                {"folder_id": folder_dto.folder_id},
                folder_data,
                upsert=True
            )
            
            success = result.acknowledged
            if success:
                self.logger.debug(f"创建文件夹记录成功: {folder_dto.path}")
            else:
                self.logger.error(f"创建文件夹记录失败: {folder_dto.path}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"创建文件夹记录异常 {folder_dto.path}: {e}")
            return False
    
    async def _create_folder_sync(self, folder_dto: FolderDTO) -> bool:
        """
        同步方式创建文件夹记录（兼容性方法）
        
        参数:
            folder_dto: 文件夹DTO对象
        
        返回:
            是否创建成功
        """
        try:
            # 使用同步数据库操作
            collection = self.db_manager.db[self.folders_collection_name]
            folder_data = folder_dto.to_dict()
            
            result = collection.replace_one(
                {"folder_id": folder_dto.folder_id},
                folder_data,
                upsert=True
            )
            
            return result.acknowledged
            
        except Exception as e:
            self.logger.error(f"同步创建文件夹记录失败 {folder_dto.path}: {e}")
            return False
    
    async def get_folder_by_id(self, folder_id: str) -> Optional[FolderDTO]:
        """
        根据ID获取文件夹
        
        参数:
            folder_id: 文件夹ID
        
        返回:
            文件夹DTO对象，不存在时返回None
        """
        try:
            if not hasattr(self.db_manager, 'async_db'):
                return await self._get_folder_by_id_sync(folder_id)
            
            folders_collection = self.db_manager.async_db[self.folders_collection_name]
            
            folder_data = await folders_collection.find_one({"folder_id": folder_id})
            
            if folder_data:
                return FolderDTO.from_dict(folder_data)
            
            return None
            
        except Exception as e:
            self.logger.error(f"根据ID获取文件夹失败 {folder_id}: {e}")
            return None
    
    async def _get_folder_by_id_sync(self, folder_id: str) -> Optional[FolderDTO]:
        """
        同步方式根据ID获取文件夹
        
        参数:
            folder_id: 文件夹ID
        
        返回:
            文件夹DTO对象，不存在时返回None
        """
        try:
            collection = self.db_manager.db[self.folders_collection_name]
            folder_data = collection.find_one({"folder_id": folder_id})
            
            if folder_data:
                return FolderDTO.from_dict(folder_data)
            
            return None
            
        except Exception as e:
            self.logger.error(f"同步获取文件夹失败 {folder_id}: {e}")
            return None
    
    async def get_folder_by_path(self, folder_path: str) -> Optional[FolderDTO]:
        """
        根据路径获取文件夹
        
        参数:
            folder_path: 文件夹路径
        
        返回:
            文件夹DTO对象，不存在时返回None
        """
        try:
            folder_path = _normalize_path(folder_path)
            
            if not hasattr(self.db_manager, 'async_db'):
                return await self._get_folder_by_path_sync(folder_path)
            
            folders_collection = self.db_manager.async_db[self.folders_collection_name]
            
            folder_data = await folders_collection.find_one({"path": folder_path})
            
            if folder_data:
                return FolderDTO.from_dict(folder_data)
            
            return None
            
        except Exception as e:
            self.logger.error(f"根据路径获取文件夹失败 {folder_path}: {e}")
            return None
    
    async def _get_folder_by_path_sync(self, folder_path: str) -> Optional[FolderDTO]:
        """
        同步方式根据路径获取文件夹
        
        参数:
            folder_path: 文件夹路径
        
        返回:
            文件夹DTO对象，不存在时返回None
        """
        try:
            collection = self.db_manager.db[self.folders_collection_name]
            folder_data = collection.find_one({"path": folder_path})
            
            if folder_data:
                return FolderDTO.from_dict(folder_data)
            
            return None
            
        except Exception as e:
            self.logger.error(f"同步根据路径获取文件夹失败 {folder_path}: {e}")
            return None
    
    async def get_child_folders(self, parent_id: Optional[str], 
                              depth: Optional[int] = None) -> List[FolderDTO]:
        """
        获取子文件夹列表
        
        参数:
            parent_id: 父文件夹ID（None表示根文件夹）
            depth: 指定深度（可选）
        
        返回:
            子文件夹DTO列表
        """
        try:
            if not hasattr(self.db_manager, 'async_db'):
                return await self._get_child_folders_sync(parent_id, depth)
            
            folders_collection = self.db_manager.async_db[self.folders_collection_name]
            
            # 构建查询条件
            query = {"parent_id": parent_id}
            if depth is not None:
                query["depth"] = depth
            
            # 执行查询
            cursor = folders_collection.find(query).sort("name", 1)
            folders_data = await cursor.to_list(length=None)
            
            # 转换为DTO对象
            folders = []
            for folder_data in folders_data:
                try:
                    folder_dto = FolderDTO.from_dict(folder_data)
                    folders.append(folder_dto)
                except Exception as e:
                    self.logger.warning(f"转换文件夹数据失败: {e}")
                    continue
            
            return folders
            
        except Exception as e:
            self.logger.error(f"获取子文件夹失败 parent_id={parent_id}, depth={depth}: {e}")
            return []

    async def _get_child_folders_sync(self, parent_id: Optional[str],
                                    depth: Optional[int] = None) -> List[FolderDTO]:
        """
        同步方式获取子文件夹列表

        参数:
            parent_id: 父文件夹ID
            depth: 指定深度

        返回:
            子文件夹DTO列表
        """
        try:
            collection = self.db_manager.db[self.folders_collection_name]

            query = {"parent_id": parent_id}
            if depth is not None:
                query["depth"] = depth

            folders_data = list(collection.find(query).sort("name", 1))

            folders = []
            for folder_data in folders_data:
                try:
                    folder_dto = FolderDTO.from_dict(folder_data)
                    folders.append(folder_dto)
                except Exception as e:
                    self.logger.warning(f"转换文件夹数据失败: {e}")
                    continue

            return folders

        except Exception as e:
            self.logger.error(f"同步获取子文件夹失败: {e}")
            return []

    async def update_folder_hash(self, folder_id: str, new_hash: str,
                               update_time: Optional[datetime] = None) -> bool:
        """
        更新文件夹哈希值

        参数:
            folder_id: 文件夹ID
            new_hash: 新的哈希值
            update_time: 更新时间（默认为当前时间）

        返回:
            是否更新成功
        """
        try:
            update_time = update_time or datetime.now()

            if not hasattr(self.db_manager, 'async_db'):
                return await self._update_folder_hash_sync(folder_id, new_hash, update_time)

            folders_collection = self.db_manager.async_db[self.folders_collection_name]

            # 更新哈希值和时间
            update_data = {
                "files_hash": new_hash,
                "content_hash": new_hash,  # 保持兼容性
                "last_hash_update": update_time.isoformat()
            }

            result = await folders_collection.update_one(
                {"folder_id": folder_id},
                {"$set": update_data}
            )

            success = result.modified_count > 0
            if success:
                self.logger.debug(f"更新文件夹哈希成功: {folder_id}")
            else:
                self.logger.warning(f"文件夹哈希未更新（可能不存在）: {folder_id}")

            return success

        except Exception as e:
            self.logger.error(f"更新文件夹哈希失败 {folder_id}: {e}")
            return False

    async def _update_folder_hash_sync(self, folder_id: str, new_hash: str,
                                     update_time: datetime) -> bool:
        """
        同步方式更新文件夹哈希值

        参数:
            folder_id: 文件夹ID
            new_hash: 新的哈希值
            update_time: 更新时间

        返回:
            是否更新成功
        """
        try:
            collection = self.db_manager.db[self.folders_collection_name]

            update_data = {
                "files_hash": new_hash,
                "content_hash": new_hash,
                "last_hash_update": update_time.isoformat()
            }

            result = collection.update_one(
                {"folder_id": folder_id},
                {"$set": update_data}
            )

            return result.modified_count > 0

        except Exception as e:
            self.logger.error(f"同步更新文件夹哈希失败 {folder_id}: {e}")
            return False

    async def batch_create_folders(self, folders: List[FolderDTO]) -> int:
        """
        批量创建文件夹记录

        参数:
            folders: 文件夹DTO列表

        返回:
            成功创建的数量
        """
        try:
            if not folders:
                return 0

            if not hasattr(self.db_manager, 'async_db'):
                return await self._batch_create_folders_sync(folders)

            folders_collection = self.db_manager.async_db[self.folders_collection_name]

            # 准备批量操作
            from pymongo import ReplaceOne
            operations = []
            for folder in folders:
                operation = ReplaceOne(
                    {"folder_id": folder.folder_id},
                    folder.to_dict(),
                    upsert=True
                )
                operations.append(operation)

            # 执行批量操作
            if operations:
                result = await folders_collection.bulk_write(operations)
                success_count = result.upserted_count + result.modified_count
                self.logger.info(f"批量创建文件夹完成: {success_count}/{len(folders)}")
                return success_count

            return 0

        except Exception as e:
            self.logger.error(f"批量创建文件夹失败: {e}")
            return 0

    async def _batch_create_folders_sync(self, folders: List[FolderDTO]) -> int:
        """
        同步方式批量创建文件夹记录

        参数:
            folders: 文件夹DTO列表

        返回:
            成功创建的数量
        """
        try:
            collection = self.db_manager.db[self.folders_collection_name]

            from pymongo import ReplaceOne
            operations = []
            for folder in folders:
                operation = ReplaceOne(
                    {"folder_id": folder.folder_id},
                    folder.to_dict(),
                    upsert=True
                )
                operations.append(operation)

            if operations:
                result = collection.bulk_write(operations)
                return result.upserted_count + result.modified_count

            return 0

        except Exception as e:
            self.logger.error(f"同步批量创建文件夹失败: {e}")
            return 0

    async def delete_folder(self, folder_id: str) -> bool:
        """
        删除文件夹记录

        参数:
            folder_id: 文件夹ID

        返回:
            是否删除成功
        """
        try:
            if not hasattr(self.db_manager, 'async_db'):
                return await self._delete_folder_sync(folder_id)

            folders_collection = self.db_manager.async_db[self.folders_collection_name]

            result = await folders_collection.delete_one({"folder_id": folder_id})

            success = result.deleted_count > 0
            if success:
                self.logger.debug(f"删除文件夹记录成功: {folder_id}")
            else:
                self.logger.warning(f"文件夹记录未删除（可能不存在）: {folder_id}")

            return success

        except Exception as e:
            self.logger.error(f"删除文件夹记录失败 {folder_id}: {e}")
            return False

    async def _delete_folder_sync(self, folder_id: str) -> bool:
        """
        同步方式删除文件夹记录

        参数:
            folder_id: 文件夹ID

        返回:
            是否删除成功
        """
        try:
            collection = self.db_manager.db[self.folders_collection_name]
            result = collection.delete_one({"folder_id": folder_id})
            return result.deleted_count > 0

        except Exception as e:
            self.logger.error(f"同步删除文件夹记录失败 {folder_id}: {e}")
            return False

    async def get_folders_by_depth(self, depth: int,
                                 scan_status: Optional[str] = None) -> List[FolderDTO]:
        """
        按深度获取文件夹列表

        参数:
            depth: 目录深度
            scan_status: 扫描状态过滤（可选）

        返回:
            文件夹DTO列表
        """
        try:
            if not hasattr(self.db_manager, 'async_db'):
                return await self._get_folders_by_depth_sync(depth, scan_status)

            folders_collection = self.db_manager.async_db[self.folders_collection_name]

            query = {"depth": depth}
            if scan_status:
                query["scan_status"] = scan_status

            cursor = folders_collection.find(query).sort("path", 1)
            folders_data = await cursor.to_list(length=None)

            folders = []
            for folder_data in folders_data:
                try:
                    folder_dto = FolderDTO.from_dict(folder_data)
                    folders.append(folder_dto)
                except Exception as e:
                    self.logger.warning(f"转换文件夹数据失败: {e}")
                    continue

            return folders

        except Exception as e:
            self.logger.error(f"按深度获取文件夹失败 depth={depth}: {e}")
            return []

    async def _get_folders_by_depth_sync(self, depth: int,
                                       scan_status: Optional[str] = None) -> List[FolderDTO]:
        """
        同步方式按深度获取文件夹列表

        参数:
            depth: 目录深度
            scan_status: 扫描状态过滤

        返回:
            文件夹DTO列表
        """
        try:
            collection = self.db_manager.db[self.folders_collection_name]

            query = {"depth": depth}
            if scan_status:
                query["scan_status"] = scan_status

            folders_data = list(collection.find(query).sort("path", 1))

            folders = []
            for folder_data in folders_data:
                try:
                    folder_dto = FolderDTO.from_dict(folder_data)
                    folders.append(folder_dto)
                except Exception as e:
                    self.logger.warning(f"转换文件夹数据失败: {e}")
                    continue

            return folders

        except Exception as e:
            self.logger.error(f"同步按深度获取文件夹失败: {e}")
            return []
