# 智能文件管理器系统流程重构计划

## 目标
- 优化应用程序主流程，提升可维护性、可扩展性和健壮性。
- 明确各模块职责，理顺依赖关系，提升架构现代化水平。
- 便于后续功能扩展和自动化测试。

---

## 阶段一：主流程梳理与解耦

### 1.1 明确主入口main函数职责
- [x] 仅负责应用生命周期调度，不做具体业务逻辑
    - 2024-06-09：已完成。main函数只负责调度，所有业务流程封装到SmartFileManagerApp.start()，提升了主入口的清晰度和可维护性。

### 1.2 SmartFileManagerApp类职责梳理
- [x] 明确初始化、运行、清理、关闭等生命周期方法的边界
    - 2024-06-09：已完成。为__init__、initialize、run、cleanup、shutdown、start方法补充职责注释，职责边界清晰，便于维护和扩展。
- [x] 日志、依赖注入、UI、事件系统等初始化流程解耦
    - 2024-06-09：已完成。所有日志、事件系统、异步管理器、UI工厂等服务全部通过全局依赖注入容器注册和resolve获取，彻底移除各模块直接工厂函数调用，架构现代化。

### 1.3 依赖注入容器优化
- [x] 统一注册/解析接口，减少全局变量依赖
    - 2024-07-12：已完成。实现统一的register方法，支持不同生命周期，并优化了register便捷函数，使接口更一致。
- [x] 支持更灵活的服务注册与替换
    - 2024-07-12：已完成。新增replace_service方法支持服务替换，增加作用域服务支持，实现线程安全的服务管理。

---

## 阶段二：模块化与接口驱动

### 2.1 UI工厂与主窗口解耦
- [x] UI工厂与主窗口解耦，便于后续切换不同UI实现
    - 2024-06-09：已完成。主窗口只依赖IUIFactory接口，UI工厂创建主窗口时只传递接口类型，类型安全、解耦彻底。

### 2.2 事件系统与异步管理器解耦
- [x] 事件系统、异步任务管理器通过接口注入，便于单元测试
    - 2024-06-09：已完成。主窗口构造函数通过参数注入event_system和async_manager，便于Mock和灵活替换。

### 2.3 日志系统模块化
- [x] 日志初始化与配置独立，支持多环境配置
    - 2024-06-09：已完成。支持config/logging_config.yaml多环境配置，环境变量自动切换，日志格式灵活。

---

## 阶段三：异常与资源管理

### 3.1 全局异常处理优化
- [x] 统一异常捕获与日志记录，提升用户体验
    - 2024-06-09：已完成。全局异常处理分级日志、追踪ID、友好弹窗，主流程异常统一处理。

### 3.2 资源清理流程梳理
- [x] 明确各类资源（窗口、线程、服务等）清理顺序与方法
    - 2024-06-09：已完成。资源清理严格按“服务→UI”顺序，判空、置空、详细日志，便于扩展。

---

## 阶段四：自动化与测试

### 4.1 自动化重构与同步
- [x] 每完成一项，自动更新本md文件进度
    - 2024-07-12：已完成。实现了update_progress.py脚本，支持通过命令行参数自动更新任务状态和进度摘要。
- [x] 每项重构后自动运行相关测试，确保无回归
    - 2024-07-12：已完成。实现了run_tests.py脚本，支持运行指定模块或全量测试，并生成测试报告检查回归。

---

## 进度追踪

- [x] 阶段一（1.1、1.2、1.3已完成）
- [x] 阶段二（2.1、2.2、2.3已完成）
- [x] 阶段三（3.1、3.2已完成）
- [x] 阶段四（4.1已完成）

> 每完成一项，勾选对应任务，并简要记录变更说明。 
- [x] MainWindow所有实例化点参数补全，兼容依赖注入
- [x] FileScanner白名单pattern类型修正，全部编译为正则对象
- [x] format_file_size格式化修正，0字节返回“0 B”
- [x] RuleEngine所有规则项访问用get，避免KeyError
- [x] RuleEngine大小写转换默认值与测试期望统一 