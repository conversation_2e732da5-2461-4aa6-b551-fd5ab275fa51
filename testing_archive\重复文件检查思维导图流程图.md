# 重复文件检查思维导图流程图

## 🧠 思维导图结构

```
重复文件检查系统
├── 🎯 用户操作层
│   ├── 点击"查找重复文件"按钮
│   ├── 设置最小文件大小 (KB)
│   ├── 选择文件类型过滤
│   │   ├── 图片 (.jpg, .png, .gif...)
│   │   ├── 视频 (.mp4, .avi, .mkv...)
│   │   ├── 音频 (.mp3, .wav, .flac...)
│   │   ├── 文档 (.pdf, .doc, .txt...)
│   │   ├── 压缩文件 (.zip, .rar, .7z...)
│   │   └── 自定义扩展名
│   └── 点击"开始查找"
│
├── 🔍 验证检查层
│   ├── 参数验证
│   │   ├── 最小文件大小格式检查
│   │   ├── 自定义扩展名格式验证
│   │   └── 数值范围验证
│   ├── 数据库连接检查
│   │   ├── 数据库管理器初始化状态
│   │   ├── MongoDB连接状态
│   │   └── 连接健康检查
│   └── Hash值完整性检查
│       ├── 查询缺少Hash值的文件数量
│       ├── 显示Hash值状态
│       └── 用户选择处理方式
│
├── ⚙️ Hash值计算层
│   ├── 用户选择
│   │   ├── 立即计算所有Hash值
│   │   ├── 跳过计算，只查找有Hash值的文件
│   │   └── 取消操作
│   ├── Hash值计算流程
│   │   ├── 扫描数据库文件
│   │   ├── 分批处理文件
│   │   ├── 读取文件内容
│   │   ├── 计算MD5哈希值
│   │   ├── 更新数据库记录
│   │   └── 更新进度显示
│   ├── 中断处理
│   │   ├── 用户点击中断按钮
│   │   ├── 停止文件读取
│   │   ├── 清理文件句柄
│   │   ├── 保存当前进度
│   │   └── 显示中断消息
│   └── 完成处理
│       ├── Hash值计算完成
│       ├── 更新数据库统计
│       └── 通知UI更新
│
├── 🗄️ 数据库查询层
│   ├── 构建查询条件
│   │   ├── Hash值不为空条件
│   │   ├── 最小文件大小过滤
│   │   └── 扩展名过滤条件
│   ├── MongoDB聚合管道
│   │   ├── $match: 过滤条件
│   │   ├── $group: 按Hash值分组
│   │   └── $match: 过滤重复组
│   ├── 查询执行
│   │   ├── 执行聚合命令
│   │   ├── 处理查询结果
│   │   └── 错误处理
│   └── 结果处理
│       ├── 解析重复文件组
│       ├── 应用扩展名过滤
│       └── 计算统计信息
│
├── 📊 结果处理层
│   ├── 数据统计
│   │   ├── 重复文件组数量
│   │   ├── 总重复文件数量
│   │   ├── 重复文件总大小
│   │   └── 可节省空间计算
│   ├── 结果格式化
│   │   ├── 文件大小格式化
│   │   ├── 路径标准化
│   │   └── 时间格式化
│   └── 结果发送
│       ├── 发送进度消息
│       ├── 发送重复文件组数据
│       └── 发送完成消息
│
├── 🖥️ UI更新层
│   ├── 进度更新
│   │   ├── 更新进度条
│   │   ├── 更新任务标签
│   │   ├── 更新子任务标签
│   │   └── 更新状态栏消息
│   ├── 重复文件组显示
│   │   ├── 清空现有结果
│   │   ├── 插入重复文件组
│   │   ├── 显示组统计信息
│   │   └── 设置选择事件
│   ├── 文件列表显示
│   │   ├── 清空文件列表
│   │   ├── 插入文件信息
│   │   ├── 显示文件详情
│   │   └── 设置操作按钮
│   └── 状态更新
│       ├── 更新按钮状态
│       ├── 更新状态指示灯
│       └── 更新统计信息
│
└── 🔧 操作功能层
    ├── 文件操作
    │   ├── 打开文件
    │   ├── 打开文件夹
    │   ├── 删除文件
    │   └── 移动文件
    ├── 批量操作
    │   ├── 保留最新文件
    │   ├── 保留最旧文件
    │   ├── 删除选中文件
    │   └── 移动选中文件
    └── 辅助功能
        ├── 刷新结果
        ├── 导出结果
        └── 清除结果
```

## 🎨 视觉化思维导图

```
                    ┌─────────────────────────────────────────────────────────────┐
                    │                    重复文件检查系统                          │
                    └─────────────────────────────────────────────────────────────┘
                                        │
                    ┌───────────────────┼───────────────────┐
                    │                   │                   │
            ┌───────▼──────┐    ┌───────▼──────┐    ┌───────▼──────┐
            │  用户操作层   │    │  验证检查层   │    │ Hash值计算层  │
            └──────────────┘    └──────────────┘    └──────────────┘
                    │                   │                   │
        ┌───────────┼───────────┐ ┌─────┼─────┐     ┌───────┼───────┐
        │           │           │ │     │     │     │       │       │
    ┌───▼───┐   ┌───▼───┐   ┌───▼─┐ ┌─▼─┐ ┌─▼─┐ ┌─▼─┐ ┌─▼─┐ ┌─▼─┐
    │ 设置  │   │ 选择  │   │ 点击 │ │参数│ │数据库│ │Hash│ │用户│ │计算│
    │ 参数  │   │ 类型  │   │ 查找 │ │验证│ │连接 │ │检查│ │选择│ │流程│
    └───────┘   └───────┘   └─────┘ └───┘ └───┘ └───┘ └───┘ └───┘ └───┘
        │           │           │     │     │     │     │     │     │
        └───────────┼───────────┘     └─────┼─────┘     └─────┼─────┘
                    │                       │                 │
            ┌───────▼──────┐        ┌───────▼──────┐   ┌──────▼──────┐
            │  数据库查询层 │        │  结果处理层   │   │   UI更新层   │
            └──────────────┘        └──────────────┘   └──────────────┘
                    │                       │                 │
        ┌───────────┼───────────┐ ┌─────────┼─────────┐ ┌─────┼─────┐
        │           │           │ │         │         │ │     │     │
    ┌───▼───┐   ┌───▼───┐   ┌───▼─┐ ┌─▼─┐ ┌─▼─┐ ┌─▼─┐ ┌─▼─┐ ┌─▼─┐
    │ 构建  │   │ MongoDB│   │ 查询 │ │数据│ │结果│ │发送│ │进度│ │重复│
    │ 查询  │   │ 聚合   │   │      │ │    │ │化  │ │    │ │    │ │显示│
    │ 条件  │   │ 管道   │   │      │ │    │ │    │ │    │ │    │ │    │
    └───────┘   └───────┘   └─────┘ └───┘ └───┘ └───┘ └───┘ └───┘ └───┘
        │           │           │     │     │     │     │     │     │
        └───────────┼───────────┘     └─────┼─────┘     └─────┼─────┘
                    │                       │                 │
            ┌───────▼──────┐        ┌───────▼──────┐   ┌──────▼──────┐
            │  操作功能层   │        │  错误处理层   │   │  性能优化层   │
            └──────────────┘        └──────────────┘   └──────────────┘
                    │                       │                 │
        ┌───────────┼───────────┐ ┌─────────┼─────────┐ ┌─────┼─────┐
        │           │           │ │         │         │ │     │     │
    ┌───▼───┐   ┌───▼───┐   ┌───▼─┐ ┌─▼─┐ ┌─▼─┐ ┌─▼─┐ ┌─▼─┐ ┌─▼─┐
    │ 文件  │   │ 批量  │   │ 辅助 │ │连接│ │查询│ │计算│ │索引│ │缓存│
    │ 操作  │   │ 操作  │   │ 功能 │ │错误│ │错误│ │错误│ │优化│ │优化│
    └───────┘   └───────┘   └─────┘ └───┘ └───┘ └───┘ └───┘ └───┘ └───┘
```

## 🔄 流程交互图

```
用户操作层 ──────┐
    │           │
    ▼           │
验证检查层 ──────┤
    │           │
    ▼           │
Hash值计算层 ────┤
    │           │
    ▼           │
数据库查询层 ────┤
    │           │
    ▼           │
结果处理层 ──────┤
    │           │
    ▼           │
UI更新层 ────────┤
    │           │
    ▼           │
操作功能层 ──────┘
```

## 🎯 关键节点说明

### 🎯 **用户操作层**
- **入口点**: 用户点击"查找重复文件"按钮
- **参数设置**: 最小文件大小、文件类型过滤
- **触发条件**: 用户主动操作

### 🔍 **验证检查层**
- **参数验证**: 确保输入参数格式正确
- **数据库连接**: 检查数据库可用性
- **Hash值检查**: 评估Hash值完整性状态

### ⚙️ **Hash值计算层**
- **用户选择**: 计算、跳过或取消
- **计算流程**: 分批处理文件Hash值
- **中断处理**: 优雅停止和恢复机制

### 🗄️ **数据库查询层**
- **查询构建**: 根据条件构建MongoDB查询
- **聚合管道**: 使用MongoDB聚合功能
- **结果处理**: 解析和过滤查询结果

### 📊 **结果处理层**
- **数据统计**: 计算重复文件统计信息
- **结果格式化**: 标准化数据格式
- **消息发送**: 向UI层发送结果数据

### 🖥️ **UI更新层**
- **进度更新**: 实时显示操作进度
- **结果显示**: 更新重复文件列表
- **状态更新**: 更新界面状态信息

### 🔧 **操作功能层**
- **文件操作**: 单个文件的操作功能
- **批量操作**: 批量处理重复文件
- **辅助功能**: 刷新、导出等辅助功能

## 🚀 优化建议

### 1. **并行处理**
```
用户操作层 ──┐
    │       │
验证检查层 ──┼─→ 并行执行
    │       │
Hash值计算层 ─┘
```

### 2. **缓存机制**
```
数据库查询层 ──→ 结果缓存 ──→ UI更新层
    │              │
    └─→ 缓存命中 ──┘
```

### 3. **异步处理**
```
用户操作 ──→ 异步任务队列 ──→ 后台处理 ──→ 结果回调
```

---

这个思维导图风格的流程图清晰地展示了重复文件检查系统的各个层次和组件，以及它们之间的关系。每个层次都有明确的职责和功能，便于理解和优化。 