# Smart File Manager - Cursor Rules (Updated)

## 项目概述
这是一个基于Python的智能文件管理器，采用异步架构和模块化设计，支持文件扫描、重复文件检测、批量重命名等功能。已完成路径参数与字段统一重构，确保跨平台一致性和代码可维护性。

## 技术栈
- **语言**: Python 3.8+
- **GUI框架**: Tkinter
- **异步框架**: asyncio
- **数据库**: MongoDB
- **依赖注入**: 自定义DI容器
- **测试框架**: pytest

## 项目结构
```
smartfileManger/
├── src/                    # 主要源代码
│   ├── core/              # 核心业务逻辑
│   ├── ui/                # 用户界面组件
│   ├── data/              # 数据访问层
│   ├── utils/             # 工具类和辅助函数
│   └── tests/             # 测试文件
├── config/                # 配置文件
├── reusable_widgets/      # 可复用UI组件
├── docs/                  # 文档
└── logs/                  # 日志文件
```

## 编码规范

### 1. 命名规范
- **类名**: 使用PascalCase (如 `FileScanner`, `MainWindow`)
- **函数/方法名**: 使用snake_case (如 `scan_directory`, `batch_rename_files`)
- **变量名**: 使用snake_case (如 `file_path`, `config_dir`)
- **常量**: 使用UPPER_SNAKE_CASE (如 `MAX_FILE_SIZE`, `DEFAULT_TIMEOUT`)
- **文件名**: 使用snake_case (如 `file_scanner.py`, `main_window.py`)

### 2. 路径参数与字段统一规范 ⭐ 重要更新
- **统一字段名**: 所有文件路径相关字段必须使用以下命名：
  - `file_path`: 单一文件路径
  - `new_file_path`: 重命名/移动后的新路径
  - `target_file_path`: 目标路径（如移动/复制目标）
  - `file_paths`: 文件路径列表
- **路径标准化**: 所有路径操作必须调用 `_normalize_path` 函数
- **数据库字段**: 数据库存储统一使用 `path` 字段
- **兼容性**: 入口处可兼容旧字段名，但内部处理必须使用统一字段

### 3. 导入规范
- 使用绝对导入: `from src.core.file_scanner import FileScanner`
- 避免循环导入，使用延迟导入或依赖注入
- 按以下顺序组织导入:
  1. 标准库导入
  2. 第三方库导入
  3. 本地模块导入

### 4. 异步编程规范
- 所有长时间运行的操作必须使用异步方法
- 异步方法名以 `_async` 结尾
- 必须支持中断机制 (`interrupt_event`)
- 必须捕获 `asyncio.CancelledError`
- 使用 `UnifiedTaskManager` 统一管理异步任务

### 5. 错误处理
- 使用自定义异常类继承自 `Exception`
- 异步方法必须捕获并处理异常
- 记录详细的错误日志
- 提供有意义的错误消息

### 6. 日志规范
- 使用结构化日志记录
- 不同级别使用不同日志方法: `logger.debug()`, `logger.info()`, `logger.warning()`, `logger.error()`
- 包含足够的上下文信息

## 架构模式

### 1. 依赖注入
- 使用 `DIContainer` 管理依赖
- 避免直接实例化，通过容器解析依赖
- 支持单例和工厂模式

### 2. 事件驱动
- 使用 `EventSystem` 进行组件间通信
- 定义明确的事件类型和数据结构
- 支持异步事件处理

### 3. 分层架构
- **UI层**: 负责用户界面和交互
- **业务层**: 核心业务逻辑
- **数据层**: 数据访问和持久化
- **工具层**: 通用工具和辅助功能

## 异步任务管理

### 1. 统一任务管理器
```python
# 使用 UnifiedTaskManager 管理所有异步任务
from src.utils.unified_task_manager import UnifiedTaskManager

# 提交任务
task_id = manager.submit_task(coroutine, task_name="file_scan")

# 检查状态
status = manager.get_task_status(task_id)

# 中断任务
manager.interrupt_task(task_id)
```

### 2. 异步方法模板
```python
async def batch_process_async(items, callback=None, interrupt_event=None):
    """异步批量处理模板"""
    results = {'success': 0, 'failed': 0, 'total': len(items)}
    try:
        for i, item in enumerate(items):
            # 检查中断信号
            if interrupt_event and interrupt_event.is_set():
                raise asyncio.CancelledError("任务被外部中断")
            
            # 处理逻辑
            result = await process_item(item)
            if result:
                results['success'] += 1
            else:
                results['failed'] += 1
                
            # 进度回调
            if callback:
                callback((i + 1) / len(items) * 100, f"处理中: {i+1}/{len(items)}")
                
    except asyncio.CancelledError:
        logger.warning("批量任务被取消")
        results['status'] = 'cancelled'
    except Exception as e:
        logger.error(f"批量任务发生错误: {e}", exc_info=True)
        results['status'] = 'failed'
    
    return results
```

## UI开发规范

### 1. 线程安全
- 所有UI操作必须在主线程执行
- 使用 `root.after()` 调度UI更新
- 避免在后台线程直接操作UI组件

### 2. 组件设计
- 继承自 `tk.Frame` 或 `tk.Toplevel`
- 实现统一的接口方法
- 支持主题切换和字体大小调整

### 3. 测试规范
```python
@pytest.mark.timeout(10)
def test_ui_component():
    """UI组件测试模板"""
    root = tk.Tk()
    try:
        # 创建组件
        component = Component(root)
        component.pack()
        
        # 测试功能
        # ...
        
        # 清理
        root.after(1000, lambda: (root.quit(), root.destroy()))
        root.mainloop()
    finally:
        if root.winfo_exists():
            root.destroy()
```

## 数据库操作

### 1. 连接管理
- 使用连接池管理数据库连接
- 实现健康检查和重连机制
- 支持事务和批量操作

### 2. 数据模型
- 使用数据类定义模型结构
- 实现序列化和反序列化方法
- 支持数据验证和转换

### 3. 路径字段规范 ⭐ 重要更新
```python
# 正确的数据库操作示例
def batch_upsert_folder_files(self, folder_file_path: str, file_infos: List[Dict[str, Any]]):
    for file_info in file_infos:
        # 确保使用统一的path字段
        normalized_path = _normalize_path(file_info['path'])  # 不是file_path
        file_info['path'] = normalized_path
        # ... 其他处理
```

## 测试规范

### 1. 单元测试
- 每个模块都要有对应的测试文件
- 测试覆盖率不低于80%
- 使用 `pytest` 作为测试框架

### 2. 集成测试
- 测试模块间的交互
- 测试完整的业务流程
- 使用真实的数据库连接

### 3. 性能测试
- 测试大数据量处理性能
- 测试并发操作性能
- 监控内存和CPU使用情况

### 4. 路径相关测试 ⭐ 重要更新
```python
def test_path_normalization():
    """测试路径标准化"""
    from src.utils.format_utils import _normalize_path
    
    # 测试Windows路径
    assert _normalize_path("C:\\Users\\<USER>\\file.txt") == "C:/Users/<USER>/file.txt"
    
    # 测试Unix路径
    assert _normalize_path("/home/<USER>/file.txt") == "/home/<USER>/file.txt"
    
    # 测试混合路径
    assert _normalize_path("C:\\Users\\<USER>\\") == "C:/Users/<USER>"
```

## 配置管理

### 1. 配置文件
- 使用YAML格式存储配置
- 支持环境变量覆盖
- 实现配置验证和默认值

### 2. 配置加载
```python
from src.utils.config_loader import ConfigLoader

config_loader = ConfigLoader(config_dir)
settings = config_loader.get_config('settings')
```

## 文档规范

### 1. 代码注释
- 所有公共方法必须有文档字符串
- 使用Google风格的文档字符串
- 包含参数、返回值和异常说明

### 2. 架构文档
- 维护架构图和设计文档
- 记录重要的设计决策
- 更新API文档

## 性能优化

### 1. 内存管理
- 及时释放不需要的资源
- 使用生成器处理大文件
- 避免内存泄漏

### 2. 并发优化
- 合理使用线程池和进程池
- 避免线程竞争和死锁
- 优化I/O密集型操作

## 安全考虑

### 1. 文件操作
- 验证文件路径安全性
- 限制文件操作权限
- 防止路径遍历攻击

### 2. 数据验证
- 验证所有用户输入
- 使用参数化查询防止SQL注入
- 加密敏感数据

## 部署和维护

### 1. 日志管理
- 配置日志轮转
- 设置合适的日志级别
- 监控错误和异常

### 2. 监控和告警
- 监控系统资源使用
- 设置性能告警
- 记录关键业务指标

## 代码审查要点

1. **路径字段一致性**: 检查是否使用统一的路径字段名
2. **路径标准化**: 确保所有路径都经过 `_normalize_path` 处理
3. **异步代码**: 检查是否正确处理中断和异常
4. **线程安全**: 确保UI操作在主线程执行
5. **资源管理**: 检查是否正确释放资源
6. **错误处理**: 验证异常处理是否完整
7. **性能影响**: 评估代码对性能的影响
8. **可维护性**: 检查代码的可读性和可维护性

## 常见问题解决

### 1. 路径字段不匹配
- 确保数据库操作使用 `path` 字段
- 检查 `file_scanner.py` 中的字段名
- 验证 `db_manager.py` 中的字段访问

### 2. 循环导入
- 使用延迟导入
- 重构模块依赖关系
- 使用依赖注入

### 3. 内存泄漏
- 检查事件监听器是否正确移除
- 验证异步任务是否正确清理
- 监控长时间运行的对象

### 4. 性能问题
- 使用性能分析工具
- 优化数据库查询
- 减少不必要的I/O操作

## 重构完成状态 ⭐ 重要更新

### 已完成的重构工作
- ✅ UI层全链路路径参数与字段统一重构完成
- ✅ 后端全链路路径参数与字段统一重构完成
- ✅ 工具层路径参数重构完成
- ✅ 所有单元测试通过（42个测试）
- ✅ 集成测试修复完成
- ✅ 扫描功能恢复正常

### 当前最佳实践
- 使用 `_normalize_path` 函数标准化所有路径
- 数据库字段统一使用 `path`
- 文件扫描器使用统一的字段名
- 所有测试覆盖路径相关场景

遵循这些规则将确保代码质量、可维护性和团队协作效率。路径重构已完成，项目已恢复到正常工作状态。

## 方法接口规范与重复实现防控（重要补充）

### 1. 方法/类接口规范
- 所有核心功能（如文件扫描、查重、数据库操作、哈希计算、进度回调、任务管理等）必须通过唯一权威接口对外暴露。
- 禁止在不同模块、类、文件中实现功能重复的方法或类。
- 通用功能（如路径处理、哈希计算、数据库聚合、进度回调、日志记录等）必须抽象为工具函数或基类，统一放在utils/core/data等专用模块，并全项目复用。
- 业务层、UI层、测试层等调用通用功能时，必须通过唯一入口调用，不得复制粘贴实现。
- 方法/类的参数、返回值类型、异常处理、回调接口等必须文档化，接口变更需同步更新所有调用方和开发文档。

### 2. 代码审查与CI要求
- 代码审查时必须重点检查是否有重复实现、接口不一致、参数/返回值类型不统一等问题。
- CI自动检测（如lint、pytest、mypy等）应覆盖接口一致性和重复实现检查。
- 发现重复实现、接口不一致等问题，必须在合并前修复。

### 3. 变更流程
- 任何通用方法/类的接口变更，必须：
  1. 先修改权威实现
  2. 全项目范围内同步更新所有调用方
  3. 更新相关开发文档和示例
  4. 补充/修正测试用例，确保无回归

### 4. 责任归属
- 代码owner和reviewer对接口规范和去重负有直接责任。
- 发现重复实现、接口不一致等问题，任何成员有权提出并要求修复。

### 5. 典型反例（禁止）
- 在不同文件/类中实现功能相同的扫描、查重、聚合、哈希等方法。
- 仅因参数略有不同就复制粘贴方法体。
- 不同步更新接口变更导致调用方报错。

### 6. 典型正例（推荐）
- 所有路径处理统一调用src.utils.format_utils._normalize_path。
- 所有哈希计算统一调用src.core.hash_calculator.HashCalculator。
- 所有数据库查重统一调用src.data.db_manager.build_duplicate_files_pipeline。
- 所有进度回调、异常处理、日志记录等统一调用权威实现。

---