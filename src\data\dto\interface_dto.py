#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
接口数据传输对象

定义用于服务接口返回的标准化DTO类，提升类型安全性和可维护性
遵循INTERFACE-001和INTERFACE-002规范
"""

import time
from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any
from pathlib import Path

from .base_dto import BaseDTO


@dataclass(frozen=True)
class DirectoryInfo(BaseDTO):
    """
    目录信息DTO
    
    用于get_directory_info()方法的返回值，替代原始dict
    """
    path: str
    file_count: int
    total_size: int
    exists: bool
    readable: bool
    last_modified: float = 0.0
    error_message: Optional[str] = None
    
    def validate(self) -> List[str]:
        """验证目录信息"""
        errors = []
        if not self.path:
            errors.append("路径不能为空")
        if self.file_count < 0:
            errors.append("文件数量不能为负数")
        if self.total_size < 0:
            errors.append("总大小不能为负数")
        return errors
    
    def format_size(self) -> str:
        """格式化文件大小显示"""
        if self.total_size < 1024:
            return f"{self.total_size} B"
        elif self.total_size < 1024 * 1024:
            return f"{self.total_size / 1024:.1f} KB"
        elif self.total_size < 1024 * 1024 * 1024:
            return f"{self.total_size / (1024 * 1024):.1f} MB"
        else:
            return f"{self.total_size / (1024 * 1024 * 1024):.1f} GB"
    
    @property
    def is_accessible(self) -> bool:
        """目录是否可访问"""
        return self.exists and self.readable and not self.error_message
    
    @classmethod
    def create_error(cls, path: str, error_message: str) -> 'DirectoryInfo':
        """创建错误信息的目录信息"""
        return cls(
            path=path,
            file_count=0,
            total_size=0,
            exists=False,
            readable=False,
            error_message=error_message
        )
    
    @classmethod
    def from_path(cls, directory_path: str) -> 'DirectoryInfo':
        """从路径创建目录信息"""
        try:
            dir_path = Path(directory_path)
            if not dir_path.exists() or not dir_path.is_dir():
                return cls.create_error(directory_path, "目录不存在或不是有效目录")
            
            file_count = 0
            total_size = 0
            
            for item in dir_path.iterdir():
                if item.is_file():
                    file_count += 1
                    total_size += item.stat().st_size
            
            return cls(
                path=str(dir_path),
                file_count=file_count,
                total_size=total_size,
                exists=True,
                readable=dir_path.stat().st_mode & 0o444 != 0,
                last_modified=dir_path.stat().st_mtime
            )
        
        except Exception as e:
            return cls.create_error(directory_path, str(e))


@dataclass(frozen=True)
class DetectionStatistics(BaseDTO):
    """
    检测统计信息DTO
    
    用于get_detection_statistics()方法的返回值，替代原始dict
    """
    task_id: str
    total_files_checked: int
    total_duplicates_found: int
    total_space_wasted: int
    duplicate_groups_count: int
    check_duration: float
    average_group_size: float
    largest_group_size: int
    scan_start_time: float = field(default_factory=time.time)
    
    def validate(self) -> List[str]:
        """验证统计信息"""
        errors = []
        if not self.task_id:
            errors.append("任务ID不能为空")
        if self.total_files_checked < 0:
            errors.append("检查文件数不能为负数")
        if self.total_duplicates_found < 0:
            errors.append("重复文件数不能为负数")
        if self.check_duration < 0:
            errors.append("检查时长不能为负数")
        return errors
    
    @property
    def duplicate_rate(self) -> float:
        """重复文件比率"""
        if self.total_files_checked == 0:
            return 0.0
        return self.total_duplicates_found / self.total_files_checked
    
    @property
    def space_efficiency(self) -> float:
        """空间效率（可节省的空间比例）"""
        total_space = self.total_space_wasted + self._estimated_useful_space()
        if total_space == 0:
            return 1.0
        return 1.0 - (self.total_space_wasted / total_space)
    
    @property
    def files_per_second(self) -> float:
        """每秒处理文件数"""
        if self.check_duration == 0:
            return 0.0
        return self.total_files_checked / self.check_duration
    
    def _estimated_useful_space(self) -> int:
        """估算有用空间（简单估算）"""
        # 假设平均文件大小为1MB，非重复文件占用的空间
        unique_files = self.total_files_checked - self.total_duplicates_found
        return unique_files * 1024 * 1024  # 1MB per file
    
    def format_duration(self) -> str:
        """格式化检查时长"""
        if self.check_duration < 60:
            return f"{self.check_duration:.1f}秒"
        elif self.check_duration < 3600:
            minutes = int(self.check_duration // 60)
            seconds = int(self.check_duration % 60)
            return f"{minutes}分{seconds}秒"
        else:
            hours = int(self.check_duration // 3600)
            minutes = int((self.check_duration % 3600) // 60)
            return f"{hours}小时{minutes}分"
    
    def format_space_wasted(self) -> str:
        """格式化浪费空间显示"""
        if self.total_space_wasted < 1024:
            return f"{self.total_space_wasted} B"
        elif self.total_space_wasted < 1024 * 1024:
            return f"{self.total_space_wasted / 1024:.1f} KB"
        elif self.total_space_wasted < 1024 * 1024 * 1024:
            return f"{self.total_space_wasted / (1024 * 1024):.1f} MB"
        else:
            return f"{self.total_space_wasted / (1024 * 1024 * 1024):.1f} GB"


@dataclass(frozen=True)
class FileCollection(BaseDTO):
    """
    文件集合DTO
    
    用于返回文件列表的方法，替代原始List[str]
    """
    files: List[str]
    total_count: int
    total_size: int = 0
    collection_time: float = field(default_factory=time.time)
    source_directory: Optional[str] = None
    recursive: bool = True
    
    def validate(self) -> List[str]:
        """验证文件集合"""
        errors = []
        if self.total_count < 0:
            errors.append("文件总数不能为负数")
        if len(self.files) != self.total_count:
            errors.append(f"文件列表长度({len(self.files)})与总数({self.total_count})不匹配")
        if self.total_size < 0:
            errors.append("总大小不能为负数")
        return errors
    
    def filter_by_extension(self, extensions: List[str]) -> 'FileCollection':
        """按扩展名过滤文件"""
        extensions_lower = [ext.lower() for ext in extensions]
        filtered_files = []
        filtered_size = 0
        
        for file_path in self.files:
            path = Path(file_path)
            if path.suffix.lower() in extensions_lower:
                filtered_files.append(file_path)
                try:
                    filtered_size += path.stat().st_size
                except:
                    pass  # 忽略无法访问的文件
        
        return FileCollection(
            files=filtered_files,
            total_count=len(filtered_files),
            total_size=filtered_size,
            source_directory=self.source_directory,
            recursive=self.recursive
        )
    
    def sort_by_size(self, descending: bool = True) -> 'FileCollection':
        """按大小排序文件"""
        file_sizes = []
        for file_path in self.files:
            try:
                size = Path(file_path).stat().st_size
                file_sizes.append((file_path, size))
            except:
                file_sizes.append((file_path, 0))
        
        file_sizes.sort(key=lambda x: x[1], reverse=descending)
        sorted_files = [file_path for file_path, _ in file_sizes]
        
        return FileCollection(
            files=sorted_files,
            total_count=self.total_count,
            total_size=self.total_size,
            source_directory=self.source_directory,
            recursive=self.recursive
        )
    
    def get_extensions_summary(self) -> Dict[str, int]:
        """获取扩展名统计"""
        extensions = {}
        for file_path in self.files:
            ext = Path(file_path).suffix.lower()
            if not ext:
                ext = "(无扩展名)"
            extensions[ext] = extensions.get(ext, 0) + 1
        return extensions
    
    @classmethod
    def from_directory(cls, directory: str, recursive: bool = True) -> 'FileCollection':
        """从目录创建文件集合"""
        files = []
        total_size = 0
        dir_path = Path(directory)
        
        try:
            if recursive:
                for file_path in dir_path.rglob("*"):
                    if file_path.is_file():
                        files.append(str(file_path))
                        try:
                            total_size += file_path.stat().st_size
                        except:
                            pass
            else:
                for file_path in dir_path.iterdir():
                    if file_path.is_file():
                        files.append(str(file_path))
                        try:
                            total_size += file_path.stat().st_size
                        except:
                            pass
        except Exception:
            pass  # 返回空集合
        
        return cls(
            files=files,
            total_count=len(files),
            total_size=total_size,
            source_directory=directory,
            recursive=recursive
        )


@dataclass(frozen=True)
class ProcessingResult(BaseDTO):
    """
    处理结果DTO
    
    用于返回处理操作的结果，替代原始tuple
    """
    success: bool
    item: Optional[Any] = None
    error_message: Optional[str] = None
    processing_time: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def validate(self) -> List[str]:
        """验证处理结果"""
        errors = []
        if self.processing_time < 0:
            errors.append("处理时间不能为负数")
        if not self.success and not self.error_message:
            errors.append("失败的处理结果必须提供错误信息")
        return errors
    
    @classmethod
    def success_result(cls, item: Any, processing_time: float = 0.0, 
                      metadata: Optional[Dict[str, Any]] = None) -> 'ProcessingResult':
        """创建成功结果"""
        return cls(
            success=True,
            item=item,
            processing_time=processing_time,
            metadata=metadata or {}
        )
    
    @classmethod
    def error_result(cls, error_message: str, processing_time: float = 0.0,
                    metadata: Optional[Dict[str, Any]] = None) -> 'ProcessingResult':
        """创建错误结果"""
        return cls(
            success=False,
            error_message=error_message,
            processing_time=processing_time,
            metadata=metadata or {}
        )


@dataclass(frozen=True)
class RenameHistoryInfo(BaseDTO):
    """
    重命名历史信息DTO

    用于get_rename_history()方法的返回值，替代原始List[Dict[str, Any]]
    """
    entries: List[Dict[str, Any]]  # 历史条目列表
    total_count: int
    date_range_start: Optional[float] = None
    date_range_end: Optional[float] = None

    def validate(self) -> List[str]:
        """验证历史信息"""
        errors = []
        if self.total_count < 0:
            errors.append("历史条目总数不能为负数")
        if len(self.entries) != self.total_count:
            errors.append(f"历史条目列表长度({len(self.entries)})与总数({self.total_count})不匹配")
        return errors

    def filter_by_date_range(self, start_time: float, end_time: float) -> 'RenameHistoryInfo':
        """按日期范围过滤历史"""
        filtered_entries = [
            entry for entry in self.entries
            if start_time <= entry.get('timestamp', 0) <= end_time
        ]
        return RenameHistoryInfo(
            entries=filtered_entries,
            total_count=len(filtered_entries),
            date_range_start=start_time,
            date_range_end=end_time
        )

    def get_success_rate(self) -> float:
        """获取成功率"""
        if not self.entries:
            return 0.0
        successful = sum(1 for entry in self.entries if entry.get('success', False))
        return successful / len(self.entries)


@dataclass(frozen=True)
class RuleTestResult(BaseDTO):
    """
    规则测试结果DTO

    用于test_rule()方法的返回值，替代原始Dict[str, str]
    """
    original_names: List[str]
    new_names: List[str]
    name_mappings: Dict[str, str]
    success_count: int
    error_count: int
    errors: List[str] = field(default_factory=list)

    def validate(self) -> List[str]:
        """验证测试结果"""
        errors = []
        if len(self.original_names) != len(self.new_names):
            errors.append("原文件名和新文件名列表长度不匹配")
        if self.success_count < 0:
            errors.append("成功数量不能为负数")
        if self.error_count < 0:
            errors.append("错误数量不能为负数")
        return errors

    @property
    def success_rate(self) -> float:
        """成功率"""
        total = self.success_count + self.error_count
        if total == 0:
            return 0.0
        return self.success_count / total

    @classmethod
    def from_mappings(cls, name_mappings: Dict[str, str],
                     errors: Optional[List[str]] = None) -> 'RuleTestResult':
        """从名称映射创建测试结果"""
        original_names = list(name_mappings.keys())
        new_names = list(name_mappings.values())
        errors = errors or []

        return cls(
            original_names=original_names,
            new_names=new_names,
            name_mappings=name_mappings,
            success_count=len(name_mappings),
            error_count=len(errors),
            errors=errors
        )


@dataclass(frozen=True)
class RuleTemplateInfo(BaseDTO):
    """
    规则模板信息DTO

    用于get_rule_templates()方法的返回值，替代原始List[Dict[str, Any]]
    """
    templates: List[Dict[str, Any]]
    total_count: int
    categories: List[str] = field(default_factory=list)

    def validate(self) -> List[str]:
        """验证模板信息"""
        errors = []
        if self.total_count < 0:
            errors.append("模板总数不能为负数")
        if len(self.templates) != self.total_count:
            errors.append(f"模板列表长度({len(self.templates)})与总数({self.total_count})不匹配")
        return errors

    def filter_by_category(self, category: str) -> 'RuleTemplateInfo':
        """按类别过滤模板"""
        filtered_templates = [
            template for template in self.templates
            if template.get('category') == category
        ]
        return RuleTemplateInfo(
            templates=filtered_templates,
            total_count=len(filtered_templates),
            categories=[category]
        )

    def get_template_names(self) -> List[str]:
        """获取所有模板名称"""
        return [template.get('name', '') for template in self.templates]


@dataclass(frozen=True)
class NamingPatternInfo(BaseDTO):
    """
    命名模式信息DTO

    用于detect_naming_pattern()方法的返回值，替代原始Dict[str, Any]
    """
    pattern_type: str
    confidence: float
    pattern_description: str
    detected_elements: Dict[str, Any] = field(default_factory=dict)
    sample_files: List[str] = field(default_factory=list)
    suggested_rules: List[str] = field(default_factory=list)

    def validate(self) -> List[str]:
        """验证模式信息"""
        errors = []
        if not self.pattern_type:
            errors.append("模式类型不能为空")
        if not 0.0 <= self.confidence <= 1.0:
            errors.append("置信度必须在0.0到1.0之间")
        if not self.pattern_description:
            errors.append("模式描述不能为空")
        return errors

    @property
    def is_high_confidence(self) -> bool:
        """是否高置信度"""
        return self.confidence >= 0.8

    @property
    def is_reliable(self) -> bool:
        """是否可靠"""
        return self.confidence >= 0.6 and len(self.sample_files) >= 3


@dataclass(frozen=True)
class OperationPreviewInfo(BaseDTO):
    """
    操作预览信息DTO

    用于preview_operations()方法的返回值，替代原始List[Dict[str, Any]]
    """
    previews: List[Dict[str, Any]]
    total_count: int
    estimated_total_duration: float = 0.0
    total_size: int = 0
    conflict_count: int = 0
    error_count: int = 0

    def validate(self) -> List[str]:
        """验证预览信息"""
        errors = []
        if self.total_count < 0:
            errors.append("预览总数不能为负数")
        if len(self.previews) != self.total_count:
            errors.append(f"预览列表长度({len(self.previews)})与总数({self.total_count})不匹配")
        if self.estimated_total_duration < 0:
            errors.append("估算总时长不能为负数")
        return errors

    def get_conflict_previews(self) -> List[Dict[str, Any]]:
        """获取有冲突的预览"""
        return [p for p in self.previews if p.get('has_conflict', False)]

    def get_error_previews(self) -> List[Dict[str, Any]]:
        """获取有错误的预览"""
        return [p for p in self.previews if p.get('error_message')]

    def format_total_duration(self) -> str:
        """格式化总时长"""
        if self.estimated_total_duration < 60:
            return f"{self.estimated_total_duration:.1f}秒"
        elif self.estimated_total_duration < 3600:
            minutes = int(self.estimated_total_duration // 60)
            seconds = int(self.estimated_total_duration % 60)
            return f"{minutes}分{seconds}秒"
        else:
            hours = int(self.estimated_total_duration // 3600)
            minutes = int((self.estimated_total_duration % 3600) // 60)
            return f"{hours}小时{minutes}分"

    @classmethod
    def from_preview_list(cls, previews: List[Dict[str, Any]]) -> 'OperationPreviewInfo':
        """从预览列表创建预览信息"""
        total_duration = sum(p.get('estimated_duration', 0.0) for p in previews)
        total_size = sum(p.get('file_size', 0) for p in previews)
        conflict_count = sum(1 for p in previews if p.get('has_conflict', False))
        error_count = sum(1 for p in previews if p.get('error_message'))

        return cls(
            previews=previews,
            total_count=len(previews),
            estimated_total_duration=total_duration,
            total_size=total_size,
            conflict_count=conflict_count,
            error_count=error_count
        )


@dataclass(frozen=True)
class OperationHistoryInfo(BaseDTO):
    """
    操作历史信息DTO

    用于get_operation_history()方法的返回值，替代原始List[Dict[str, Any]]
    """
    entries: List[Dict[str, Any]]
    total_count: int
    date_range_start: Optional[float] = None
    date_range_end: Optional[float] = None
    success_count: int = 0
    failure_count: int = 0

    def validate(self) -> List[str]:
        """验证历史信息"""
        errors = []
        if self.total_count < 0:
            errors.append("历史条目总数不能为负数")
        if len(self.entries) != self.total_count:
            errors.append(f"历史条目列表长度({len(self.entries)})与总数({self.total_count})不匹配")
        if self.success_count < 0:
            errors.append("成功数量不能为负数")
        if self.failure_count < 0:
            errors.append("失败数量不能为负数")
        return errors

    def get_success_rate(self) -> float:
        """获取成功率"""
        total = self.success_count + self.failure_count
        if total == 0:
            return 0.0
        return self.success_count / total

    def filter_by_operation_type(self, operation_type: str) -> 'OperationHistoryInfo':
        """按操作类型过滤历史"""
        filtered_entries = [
            entry for entry in self.entries
            if entry.get('operation_type') == operation_type
        ]

        success_count = sum(1 for entry in filtered_entries if entry.get('success', False))
        failure_count = len(filtered_entries) - success_count

        return OperationHistoryInfo(
            entries=filtered_entries,
            total_count=len(filtered_entries),
            success_count=success_count,
            failure_count=failure_count
        )

    @classmethod
    def from_history_list(cls, entries: List[Dict[str, Any]]) -> 'OperationHistoryInfo':
        """从历史列表创建历史信息"""
        success_count = sum(1 for entry in entries if entry.get('success', False))
        failure_count = len(entries) - success_count

        # 计算日期范围
        date_range_start = None
        date_range_end = None
        if entries:
            timestamps = [entry.get('timestamp', 0) for entry in entries]
            date_range_start = min(timestamps)
            date_range_end = max(timestamps)

        return cls(
            entries=entries,
            total_count=len(entries),
            date_range_start=date_range_start,
            date_range_end=date_range_end,
            success_count=success_count,
            failure_count=failure_count
        )
