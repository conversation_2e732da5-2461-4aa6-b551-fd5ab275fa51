#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
目录管理器模块

该模块专门负责目录选择、浏览和管理功能，
从原有的FileTreePanel中分离出来，实现单一职责。

作者: AI助手
日期: 2023-06-01
版本: 2.0.0
"""

import os
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from typing import List, Optional, Callable, Dict, Any
from pathlib import Path

from src.ui.events.event_definitions import UIEventType, create_event_data
from src.data.dto_models import ScanRequest, ViewState
from src.utils.logger import get_logger


class DirectoryManager:
    """
    目录管理器类
    
    专门负责目录选择、浏览和管理功能
    """
    
    def __init__(self, parent: tk.Widget, event_bus, logger=None):
        """
        初始化目录管理器
        
        参数:
            parent: 父容器
            event_bus: 事件总线
            logger: 日志记录器
        """
        self.parent = parent
        self.event_bus = event_bus
        self.logger = logger or get_logger(__name__)
        
        # 状态管理
        self.current_directories: List[str] = []
        self.selected_directory: Optional[str] = None
        self.recent_directories: List[str] = []
        self.max_recent = 10
        
        # UI组件
        self.frame = None
        self.directory_var = tk.StringVar()
        self.directory_listbox = None
        self.add_button = None
        self.remove_button = None
        self.browse_button = None
        self.scan_button = None
        
        # 创建UI
        self._create_ui()
        self._bind_events()
        
        # 加载最近使用的目录
        self._load_recent_directories()
    
    def _create_ui(self):
        """创建用户界面"""
        # 主框架
        self.frame = ttk.LabelFrame(self.parent, text="目录管理", padding=10)
        
        # 目录输入框架
        input_frame = ttk.Frame(self.frame)
        input_frame.pack(fill=tk.X, pady=(0, 5))
        
        # 目录输入框
        ttk.Label(input_frame, text="目录路径:").pack(side=tk.LEFT)
        directory_entry = ttk.Entry(input_frame, textvariable=self.directory_var, width=50)
        directory_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 5))
        
        # 浏览按钮
        self.browse_button = ttk.Button(input_frame, text="浏览", command=self._browse_directory)
        self.browse_button.pack(side=tk.RIGHT)
        
        # 按钮框架
        button_frame = ttk.Frame(self.frame)
        button_frame.pack(fill=tk.X, pady=(0, 5))
        
        # 添加目录按钮
        self.add_button = ttk.Button(button_frame, text="添加目录", command=self._add_directory)
        self.add_button.pack(side=tk.LEFT, padx=(0, 5))
        
        # 移除目录按钮
        self.remove_button = ttk.Button(button_frame, text="移除目录", command=self._remove_directory)
        self.remove_button.pack(side=tk.LEFT, padx=(0, 5))
        
        # 清空列表按钮
        clear_button = ttk.Button(button_frame, text="清空列表", command=self._clear_directories)
        clear_button.pack(side=tk.LEFT, padx=(0, 5))
        
        # 扫描按钮
        self.scan_button = ttk.Button(button_frame, text="开始扫描", command=self._start_scan)
        self.scan_button.pack(side=tk.RIGHT)
        
        # 目录列表框架
        list_frame = ttk.Frame(self.frame)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(5, 0))
        
        # 目录列表
        self.directory_listbox = tk.Listbox(list_frame, height=6)
        self.directory_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.directory_listbox.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.directory_listbox.config(yscrollcommand=scrollbar.set)
        
        # 最近使用目录下拉框
        recent_frame = ttk.Frame(self.frame)
        recent_frame.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Label(recent_frame, text="最近使用:").pack(side=tk.LEFT)
        self.recent_combo = ttk.Combobox(recent_frame, state="readonly", width=60)
        self.recent_combo.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 5))
        
        # 使用最近目录按钮
        use_recent_button = ttk.Button(recent_frame, text="使用", command=self._use_recent_directory)
        use_recent_button.pack(side=tk.RIGHT)
    
    def _bind_events(self):
        """绑定事件"""
        # 目录输入框回车事件
        self.directory_var.trace('w', self._on_directory_changed)
        
        # 列表框选择事件
        self.directory_listbox.bind('<<ListboxSelect>>', self._on_directory_selected)
        self.directory_listbox.bind('<Double-Button-1>', self._on_directory_double_click)
        
        # 最近目录选择事件
        self.recent_combo.bind('<<ComboboxSelected>>', self._on_recent_selected)
    
    def _browse_directory(self):
        """浏览目录"""
        try:
            initial_dir = self.directory_var.get() or os.path.expanduser("~")
            directory = filedialog.askdirectory(
                title="选择要扫描的目录",
                initialdir=initial_dir
            )
            
            if directory:
                self.directory_var.set(directory)
                self._add_to_recent(directory)
                
                # 发布目录选择事件
                event_data = create_event_data(
                    UIEventType.DIRECTORY_SELECTED.value,
                    source="directory_manager",
                    data={"directory": directory}
                )
                self.event_bus.publish(UIEventType.DIRECTORY_SELECTED.value, event_data)
                
        except Exception as e:
            self.logger.error(f"浏览目录失败: {e}")
            messagebox.showerror("错误", f"浏览目录失败: {e}")
    
    def _add_directory(self):
        """添加目录到列表"""
        directory = self.directory_var.get().strip()
        if not directory:
            messagebox.showwarning("警告", "请输入或选择目录路径")
            return
        
        if not os.path.exists(directory):
            messagebox.showerror("错误", f"目录不存在: {directory}")
            return
        
        if not os.path.isdir(directory):
            messagebox.showerror("错误", f"路径不是目录: {directory}")
            return
        
        # 标准化路径
        directory = os.path.abspath(directory)
        
        if directory not in self.current_directories:
            self.current_directories.append(directory)
            self.directory_listbox.insert(tk.END, directory)
            self._add_to_recent(directory)
            
            # 发布目录变化事件
            self._publish_directory_changed()
            
            self.logger.info(f"添加目录: {directory}")
        else:
            messagebox.showinfo("提示", "目录已存在于列表中")
    
    def _remove_directory(self):
        """移除选中的目录"""
        selection = self.directory_listbox.curselection()
        if not selection:
            messagebox.showwarning("警告", "请选择要移除的目录")
            return
        
        index = selection[0]
        directory = self.current_directories[index]
        
        # 从列表中移除
        del self.current_directories[index]
        self.directory_listbox.delete(index)
        
        # 发布目录变化事件
        self._publish_directory_changed()
        
        self.logger.info(f"移除目录: {directory}")
    
    def _clear_directories(self):
        """清空目录列表"""
        if self.current_directories:
            if messagebox.askyesno("确认", "确定要清空所有目录吗？"):
                self.current_directories.clear()
                self.directory_listbox.delete(0, tk.END)
                
                # 发布目录变化事件
                self._publish_directory_changed()
                
                self.logger.info("清空目录列表")
    
    def _start_scan(self):
        """开始扫描"""
        if not self.current_directories:
            messagebox.showwarning("警告", "请先添加要扫描的目录")
            return
        
        # 创建扫描请求
        scan_request = ScanRequest(
            task_id="",  # 将由服务层生成
            directories=self.current_directories.copy(),
            recursive=True,
            update_database=True
        )
        
        # 发布扫描请求事件
        event_data = create_event_data(
            UIEventType.SCAN_REQUESTED.value,
            source="directory_manager",
            data={"scan_request": scan_request.__dict__}
        )
        self.event_bus.publish(UIEventType.SCAN_REQUESTED.value, event_data)
        
        self.logger.info(f"请求扫描 {len(self.current_directories)} 个目录")
    
    def _on_directory_changed(self, *args):
        """目录输入框内容变化"""
        directory = self.directory_var.get().strip()
        # 可以在这里添加实时验证逻辑
    
    def _on_directory_selected(self, event):
        """目录列表选择事件"""
        selection = self.directory_listbox.curselection()
        if selection:
            index = selection[0]
            self.selected_directory = self.current_directories[index]
            self.directory_var.set(self.selected_directory)
    
    def _on_directory_double_click(self, event):
        """目录列表双击事件"""
        selection = self.directory_listbox.curselection()
        if selection:
            index = selection[0]
            directory = self.current_directories[index]
            # 可以在这里添加打开目录的逻辑
            try:
                os.startfile(directory)  # Windows
            except AttributeError:
                os.system(f'open "{directory}"')  # macOS
            except Exception:
                os.system(f'xdg-open "{directory}"')  # Linux
    
    def _on_recent_selected(self, event):
        """最近目录选择事件"""
        selected = self.recent_combo.get()
        if selected:
            self.directory_var.set(selected)
    
    def _use_recent_directory(self):
        """使用最近选择的目录"""
        selected = self.recent_combo.get()
        if selected:
            self.directory_var.set(selected)
            self._add_directory()
    
    def _add_to_recent(self, directory: str):
        """添加到最近使用目录"""
        if directory in self.recent_directories:
            self.recent_directories.remove(directory)
        
        self.recent_directories.insert(0, directory)
        
        # 限制最近目录数量
        if len(self.recent_directories) > self.max_recent:
            self.recent_directories = self.recent_directories[:self.max_recent]
        
        # 更新下拉框
        self.recent_combo['values'] = self.recent_directories
        
        # 保存到配置
        self._save_recent_directories()
    
    def _load_recent_directories(self):
        """加载最近使用的目录"""
        # 这里可以从配置文件加载
        # 暂时使用空列表
        self.recent_directories = []
        self.recent_combo['values'] = self.recent_directories
    
    def _save_recent_directories(self):
        """保存最近使用的目录"""
        # 这里可以保存到配置文件
        pass
    
    def _publish_directory_changed(self):
        """发布目录变化事件"""
        event_data = create_event_data(
            UIEventType.DIRECTORY_CHANGED.value,
            source="directory_manager",
            data={"directories": self.current_directories.copy()}
        )
        self.event_bus.publish(UIEventType.DIRECTORY_CHANGED.value, event_data)
    
    # 公共接口方法
    def get_frame(self) -> ttk.Frame:
        """获取主框架"""
        return self.frame
    
    def get_directories(self) -> List[str]:
        """获取当前目录列表"""
        return self.current_directories.copy()
    
    def set_directories(self, directories: List[str]):
        """设置目录列表"""
        self.current_directories = directories.copy()
        self.directory_listbox.delete(0, tk.END)
        for directory in directories:
            self.directory_listbox.insert(tk.END, directory)
        self._publish_directory_changed()
    
    def add_directory_programmatically(self, directory: str) -> bool:
        """程序化添加目录"""
        if os.path.exists(directory) and os.path.isdir(directory):
            directory = os.path.abspath(directory)
            if directory not in self.current_directories:
                self.current_directories.append(directory)
                self.directory_listbox.insert(tk.END, directory)
                self._add_to_recent(directory)
                self._publish_directory_changed()
                return True
        return False
    
    def set_scan_enabled(self, enabled: bool):
        """设置扫描按钮状态"""
        self.scan_button.config(state=tk.NORMAL if enabled else tk.DISABLED)
