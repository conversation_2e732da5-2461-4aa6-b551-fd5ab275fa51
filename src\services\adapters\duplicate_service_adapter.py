#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
重复文件服务适配器

提供向后兼容性，将新的重复检测服务适配到现有的接口
遵循RULE-001: 模块职责单一原则 - 只负责接口适配
"""

import asyncio
import time
from typing import Dict, List, Optional, Callable, Any
from pathlib import Path

from src.services.implementations.duplicate_detection_service import DuplicateDetectionServiceImpl
from src.data.dto.duplicate_dto import DuplicateCheckRequest, DuplicateCheckType
from src.ui.events.event_bus import EventBus
from src.core.duplicate_finder import DuplicateFinder

# 常量定义
DEFAULT_CALLBACK_INTERVAL = 1.0  # 回调间隔（秒）
DEFAULT_MIN_FILE_SIZE = 1024  # 默认最小文件大小（1KB）


class DuplicateServiceAdapter:
    """
    重复文件服务适配器
    
    将新的DuplicateDetectionServiceImpl适配到现有的DuplicateFinder接口
    确保向后兼容性，同时利用新架构的优势
    """
    
    def __init__(self, event_bus: Optional[EventBus] = None):
        """初始化适配器"""
        self._event_bus = event_bus or EventBus()
        self._service = DuplicateDetectionServiceImpl(self._event_bus)
        self._running_tasks: Dict[str, Any] = {}  # 可以存储Thread或Task
        self._task_status: Dict[str, str] = {}  # 存储任务状态
        
        # 启动事件总线（如果需要）
        if not self._event_bus._running:
            self._event_bus.start()
    
    async def start(self):
        """启动适配器"""
        await self._service.start_service()
    
    async def stop(self):
        """停止适配器"""
        # 取消所有运行中的任务
        for task_id, task in self._running_tasks.items():
            if hasattr(task, 'cancel'):
                task.cancel()
            self._task_status[task_id] = "cancelled"

        # 等待asyncio任务完成
        async_tasks = [task for task in self._running_tasks.values()
                      if hasattr(task, 'cancel')]
        if async_tasks:
            await asyncio.gather(*async_tasks, return_exceptions=True)

        await self._service.stop_service()
    
    def find_duplicates(self, directories: List[str], min_size: int = DEFAULT_MIN_FILE_SIZE,
                       callback: Optional[Callable[[Dict[str, Any]], None]] = None) -> str:
        """
        同步查找重复文件（兼容旧接口）
        
        参数:
            directories: 要扫描的目录列表
            min_size: 最小文件大小（字节）
            callback: 进度回调函数
        
        返回:
            str: 任务ID
        """
        # 创建检测请求
        task_id = self._service.create_task_id()
        request = DuplicateCheckRequest(
            task_id=task_id,
            directories=directories,
            check_type=DuplicateCheckType.BY_SIZE_AND_HASH,
            min_file_size=min_size,
            recursive=True
        )
        
        # 在后台运行异步任务
        def run_async():
            try:
                self._task_status[task_id] = "running"
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(
                    self._run_detection_with_callback(request, callback)
                )
                loop.close()
                self._task_status[task_id] = "completed"
            except Exception as e:
                import logging
                logging.getLogger(__name__).error(f"异步任务执行失败: {e}")
                self._task_status[task_id] = "failed"
            finally:
                # 清理任务
                self._running_tasks.pop(task_id, None)

        import threading
        thread = threading.Thread(target=run_async)
        thread.daemon = True
        thread.start()

        # 任务跟踪
        self._running_tasks[task_id] = thread
        self._task_status[task_id] = "running"

        return task_id
    
    async def find_duplicates_async(self, directories: List[str], min_size: int = DEFAULT_MIN_FILE_SIZE,
                                  callback: Optional[Callable[[Dict[str, Any]], None]] = None) -> Dict[str, List[str]]:
        """
        异步查找重复文件（兼容旧接口）
        
        参数:
            directories: 要扫描的目录列表
            min_size: 最小文件大小（字节）
            callback: 进度回调函数
        
        返回:
            Dict[str, List[str]]: 重复文件组字典（哈希值 -> 文件路径列表）
        """
        # 创建检测请求
        task_id = self._service.create_task_id()
        request = DuplicateCheckRequest(
            task_id=task_id,
            directories=directories,
            check_type=DuplicateCheckType.BY_SIZE_AND_HASH,
            min_file_size=min_size,
            recursive=True
        )
        
        # 运行检测
        await self._run_detection_with_callback(request, callback)
        
        # 获取结果并转换为旧格式
        groups = await self._service.get_duplicate_groups(task_id)
        return self._convert_to_legacy_format(groups)
    
    async def _run_detection_with_callback(self, request: DuplicateCheckRequest,
                                         callback: Optional[Callable[[Dict[str, Any]], None]]):
        """运行检测并处理回调"""
        try:
            last_callback_time = time.time()
            
            async for progress in self._service.detect_duplicates(request):
                # 定期调用回调函数
                current_time = time.time()
                if (callback and 
                    current_time - last_callback_time >= DEFAULT_CALLBACK_INTERVAL):
                    
                    # 转换为旧格式的进度信息
                    progress_info = {
                        "progress": progress.progress,
                        "message": progress.status_message,
                        "processed": progress.processed_items,
                        "total": progress.total_items,
                        "stage": "检测重复文件"
                    }
                    
                    try:
                        callback(progress_info)
                    except Exception as e:
                        # 记录回调错误但继续执行
                        import logging
                        logging.getLogger(__name__).warning(f"进度回调执行失败: {e}")
                    
                    last_callback_time = current_time
        
        except Exception as e:
            # 如果有回调，通知错误
            if callback:
                error_info = {
                    "progress": 0.0,
                    "message": f"检测失败: {str(e)}",
                    "error": True
                }
                try:
                    callback(error_info)
                except Exception as e:
                    # 记录错误回调失败但不影响主流程
                    import logging
                    logging.getLogger(__name__).warning(f"错误回调执行失败: {e}")
            raise
        
        finally:
            # 清理任务
            self._running_tasks.pop(request.task_id, None)
    
    def _convert_to_legacy_format(self, groups) -> Dict[str, List[str]]:
        """将新格式的重复组转换为旧格式"""
        result = {}
        
        for group in groups:
            # 使用组ID作为键（模拟哈希值）
            file_paths = [file_info.path for file_info in group.files]
            result[group.group_id] = file_paths
        
        return result
    
    def get_task_status(self, task_id: str) -> Optional[str]:
        """获取任务状态（兼容旧接口）"""
        return self._task_status.get(task_id)
    
    async def get_task_result(self, task_id: str) -> Optional[Dict[str, List[str]]]:
        """获取任务结果（兼容旧接口）"""
        groups = await self._service.get_duplicate_groups(task_id)
        if groups:
            return self._convert_to_legacy_format(groups)
        return None
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务（兼容旧接口）"""
        if task_id in self._running_tasks:
            task = self._running_tasks[task_id]
            if hasattr(task, 'cancel'):
                task.cancel()
            self._task_status[task_id] = "cancelled"
            self._running_tasks.pop(task_id, None)
            return True
        return False
    
    async def get_detection_statistics(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取检测统计信息"""
        return await self._service.get_detection_statistics(task_id)


class LegacyDuplicateFinderAdapter(DuplicateFinder):
    """
    遗留DuplicateFinder的适配器
    
    继承自原有的DuplicateFinder类，重写关键方法以使用新的服务
    """
    
    def __init__(self, db_manager=None, use_database: bool = True):
        """初始化适配器"""
        # 不调用父类初始化，避免创建旧的组件
        self.logger = self._get_logger()
        self.use_database = use_database and db_manager is not None
        self.db_manager = db_manager
        
        # 使用新的服务适配器
        self._adapter = DuplicateServiceAdapter()
        
        self.logger.info("遗留DuplicateFinder适配器初始化完成")
    
    def _get_logger(self):
        """获取日志器"""
        try:
            from src.utils.logger import get_logger
            return get_logger(__name__)
        except ImportError:
            import logging
            return logging.getLogger(__name__)
    
    async def find_duplicates_async(self, directories: List[str], min_size: int = DEFAULT_MIN_FILE_SIZE,
                                  callback: Optional[Callable] = None) -> Dict[str, List[str]]:
        """
        异步查找重复文件（重写父类方法）
        """
        await self._adapter.start()
        
        try:
            return await self._adapter.find_duplicates_async(directories, min_size, callback)
        finally:
            await self._adapter.stop()
    
    def find_duplicates(self, directories: List[str], min_size: int = DEFAULT_MIN_FILE_SIZE,
                       callback: Optional[Callable] = None) -> str:
        """
        同步查找重复文件（重写父类方法）
        """
        # 启动适配器
        asyncio.create_task(self._adapter.start())
        
        return self._adapter.find_duplicates(directories, min_size, callback)
    
    async def shutdown(self):
        """关闭适配器"""
        await self._adapter.stop()


# 向后兼容性函数
async def find_duplicates_intelligent(file_paths: List[str],
                                    progress_callback: Optional[Callable] = None) -> Dict[str, List[str]]:
    """
    向后兼容的智能重复文件查找函数（重写）
    
    使用新的服务架构，但保持旧的接口
    """
    # 从文件路径中提取目录
    directories = list(set(str(Path(fp).parent) for fp in file_paths))
    
    adapter = DuplicateServiceAdapter()
    await adapter.start()
    
    try:
        return await adapter.find_duplicates_async(directories, 0, progress_callback)
    finally:
        await adapter.stop()


# 全局适配器实例（用于向后兼容）
_global_adapter: Optional[DuplicateServiceAdapter] = None


def get_global_duplicate_adapter() -> DuplicateServiceAdapter:
    """获取全局重复文件服务适配器"""
    global _global_adapter
    if _global_adapter is None:
        _global_adapter = DuplicateServiceAdapter()
    return _global_adapter
